import { sequelize } from '../config/database.js';
import { readdir } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function runMigrations() {
  try {
    console.log('🔄 Starting migrations...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established');
    
    // Get list of migration files
    const migrationsPath = join(__dirname, '..', 'migrations');
    const files = await readdir(migrationsPath);
    const migrationFiles = files
      .filter(file => file.endsWith('.js') && file.match(/^\d{3}-.*\.js$/))
      .sort();
    
    console.log(`📁 Found ${migrationFiles.length} migration files`);
    
    // Check which migrations have been run
    let executedMigrations = [];
    try {
      const [results] = await sequelize.query(
        "SELECT name FROM \"SequelizeMeta\" ORDER BY name"
      );
      executedMigrations = results.map(row => row.name);
    } catch (error) {
      console.log('⚠️  Could not read SequelizeMeta table, assuming no migrations executed');
    }
    
    console.log(`✅ Already executed: ${executedMigrations.length} migrations`);
    
    // Run pending migrations
    for (const file of migrationFiles) {
      if (!executedMigrations.includes(file)) {
        console.log(`🔄 Running migration: ${file}`);
        
        try {
          const migration = await import(join(migrationsPath, file));
          const migrationModule = migration.default || migration;
          
          if (migrationModule.up) {
            await migrationModule.up(sequelize.getQueryInterface(), sequelize.Sequelize);
            
            // Record migration as executed
            await sequelize.query(
              "INSERT INTO SequelizeMeta (name) VALUES (?)",
              { replacements: [file] }
            );
            
            console.log(`✅ Migration ${file} completed successfully`);
          } else {
            console.log(`⚠️  Migration ${file} has no 'up' method, skipping`);
          }
        } catch (error) {
          console.error(`❌ Error running migration ${file}:`, error.message);
          throw error;
        }
      } else {
        console.log(`⏭️  Migration ${file} already executed, skipping`);
      }
    }
    
    console.log('🎉 All migrations completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run migrations if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigrations()
    .then(() => {
      console.log('✅ Migrations completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migrations failed:', error);
      process.exit(1);
    });
}

export default runMigrations;
