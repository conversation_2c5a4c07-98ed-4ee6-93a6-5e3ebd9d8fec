import { Permission } from '../models/associations.js';
import { sequelize } from '../config/database.js';
import logger from '../config/logger.config.js';

const newPermissions = [
  // Project management permissions
  {
    key: 'project:publish',
    name: 'Publish Projects',
    description: 'Ability to publish and unpublish projects',
    category: 'project',
    is_system_permission: true
  },
  {
    key: 'project:assign_users',
    name: 'Assign Users to Projects',
    description: 'Ability to assign instructors, TAs, and other users to projects',
    category: 'project',
    is_system_permission: true
  },
  {
    key: 'project:manage_assignments',
    name: 'Manage Project Assignments',
    description: 'Ability to create, update, and delete project assignments',
    category: 'project',
    is_system_permission: true
  },
  {
    key: 'project:view_assignments',
    name: 'View Project Assignments',
    description: 'Ability to view project assignments and team members',
    category: 'project',
    is_system_permission: true
  },
  
  // Project template permissions
  {
    key: 'project_template:create',
    name: 'Create Project Templates',
    description: 'Ability to create project templates',
    category: 'project',
    is_system_permission: true
  },
  {
    key: 'project_template:read',
    name: 'Read Project Templates',
    description: 'Ability to view and search project templates',
    category: 'project',
    is_system_permission: true
  },
  {
    key: 'project_template:update',
    name: 'Update Project Templates',
    description: 'Ability to modify existing project templates',
    category: 'project',
    is_system_permission: true
  },
  {
    key: 'project_template:delete',
    name: 'Delete Project Templates',
    description: 'Ability to remove project templates',
    category: 'project',
    is_system_permission: true
  },
  {
    key: 'project_template:rate',
    name: 'Rate Project Templates',
    description: 'Ability to rate and provide feedback on project templates',
    category: 'project',
    is_system_permission: true
  },
  {
    key: 'project_template:approve',
    name: 'Approve Project Templates',
    description: 'Ability to approve or reject project templates for publication',
    category: 'project',
    is_system_permission: true
  },
  {
    key: 'project_template:feature',
    name: 'Feature Project Templates',
    description: 'Ability to mark project templates as featured',
    category: 'project',
    is_system_permission: true
  },
  {
    key: 'project_template:archive',
    name: 'Archive Project Templates',
    description: 'Ability to archive inactive project templates',
    category: 'project',
    is_system_permission: true
  },
  {
    key: 'project_template:duplicate',
    name: 'Duplicate Project Templates',
    description: 'Ability to create copies of existing project templates',
    category: 'project',
    is_system_permission: true
  },
  
  // Course role permissions
  {
    key: 'course_role:view',
    name: 'View Course Roles',
    description: 'Ability to view user roles and permissions in courses',
    category: 'course',
    is_system_permission: true
  },
  {
    key: 'course_role:manage',
    name: 'Manage Course Roles',
    description: 'Ability to assign and modify user roles in courses',
    category: 'course',
    is_system_permission: true
  }
];

async function seedPermissions() {
  try {
    logger.info('Starting permission seeding...');
    
    for (const permissionData of newPermissions) {
      const [permission, created] = await Permission.findOrCreate({
        where: { key: permissionData.key },
        defaults: permissionData
      });
      
      if (created) {
        logger.info(`Created permission: ${permissionData.key}`);
      } else {
        logger.info(`Permission already exists: ${permissionData.key}`);
      }
    }
    
    logger.info('Permission seeding completed successfully');
  } catch (error) {
    logger.error('Error seeding permissions:', error);
    throw error;
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedPermissions()
    .then(() => {
      logger.info('Permission seeding script completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Permission seeding script failed:', error);
      process.exit(1);
    });
}

export default seedPermissions;
