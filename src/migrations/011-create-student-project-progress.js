export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('student_project_progress', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      student_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      project_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      course_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'courses',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      enrollment_date: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      start_date: {
        type: Sequelize.DATE,
        allowNull: true
      },
      completion_date: {
        type: Sequelize.DATE,
        allowNull: true
      },
      progress_percentage: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 0.00
      },
      current_phase: {
        type: Sequelize.STRING,
        allowNull: true
      },
      time_spent_hours: {
        type: Sequelize.DECIMAL(8, 2),
        allowNull: false,
        defaultValue: 0.00
      },
      last_activity: {
        type: Sequelize.DATE,
        allowNull: true
      },
      status: {
        type: Sequelize.ENUM('not_started', 'in_progress', 'completed', 'overdue'),
        allowNull: false,
        defaultValue: 'not_started'
      },
      grade: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: true
      },
      feedback: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('student_project_progress', ['student_id']);
    await queryInterface.addIndex('student_project_progress', ['project_id']);
    await queryInterface.addIndex('student_project_progress', ['course_id']);
    await queryInterface.addIndex('student_project_progress', ['status']);
    await queryInterface.addIndex('student_project_progress', ['last_activity']);
    
    // Add unique constraint
    await queryInterface.addConstraint('student_project_progress', {
      fields: ['student_id', 'project_id'],
      type: 'unique',
      name: 'unique_student_project'
    });

    // Add comments
    await queryInterface.sequelize.query(`
      COMMENT ON TABLE student_project_progress IS 'Tracks student progress through individual projects';
      COMMENT ON COLUMN student_project_progress.progress_percentage IS 'Progress percentage from 0.00 to 100.00';
      COMMENT ON COLUMN student_project_progress.time_spent_hours IS 'Total time spent on project in hours';
      COMMENT ON COLUMN student_project_progress.current_phase IS 'Current phase or milestone in the project';
      COMMENT ON COLUMN student_project_progress.metadata IS 'Additional progress metadata and tracking information';
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('student_project_progress');
  }
};
