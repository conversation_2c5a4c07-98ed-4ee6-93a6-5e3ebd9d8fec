'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create checkpoints table
    await queryInterface.createTable('checkpoints', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      project_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      checkpoint_number: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      due_date: {
        type: Sequelize.DATE,
        allowNull: true
      },
      weight_percentage: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 0
      },
      is_required: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      status: {
        type: Sequelize.ENUM('draft', 'published', 'archived'),
        defaultValue: 'draft'
      },
      created_by: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create indexes for checkpoints table
    await queryInterface.addIndex('checkpoints', ['project_id']);
    await queryInterface.addIndex('checkpoints', ['created_by']);
    await queryInterface.addIndex('checkpoints', ['status']);
    await queryInterface.addIndex('checkpoints', ['checkpoint_number']);
    await queryInterface.addIndex('checkpoints', ['project_id', 'checkpoint_number'], {
      unique: true,
      name: 'checkpoints_project_checkpoint_unique'
    });

    // Create checkpoint_goals table
    await queryInterface.createTable('checkpoint_goals', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      checkpoint_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'checkpoints',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      goal_type: {
        type: Sequelize.ENUM('file_upload', 'code_completion', 'analysis', 'documentation', 'presentation', 'quiz', 'discussion'),
        allowNull: false
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      required_files: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: []
      },
      completion_criteria: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: []
      },
      points: {
        type: Sequelize.DECIMAL(8, 2),
        allowNull: false,
        defaultValue: 0
      },
      order_index: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      is_required: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      estimated_time_minutes: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create indexes for checkpoint_goals table
    await queryInterface.addIndex('checkpoint_goals', ['checkpoint_id']);
    await queryInterface.addIndex('checkpoint_goals', ['goal_type']);
    await queryInterface.addIndex('checkpoint_goals', ['order_index']);
    await queryInterface.addIndex('checkpoint_goals', ['checkpoint_id', 'order_index'], {
      unique: true,
      name: 'checkpoint_goals_checkpoint_order_unique'
    });

    // Create checkpoint_progress table
    await queryInterface.createTable('checkpoint_progress', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      checkpoint_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'checkpoints',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      project_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      status: {
        type: Sequelize.ENUM('not_started', 'in_progress', 'submitted', 'reviewed', 'completed', 'returned'),
        defaultValue: 'not_started'
      },
      started_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      submitted_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      completed_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      returned_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      instructor_feedback: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      student_notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      goal_progress: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      files_submitted: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: []
      },
      auto_save_data: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      time_spent_minutes: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0
      },
      last_activity: {
        type: Sequelize.DATE,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create indexes for checkpoint_progress table
    await queryInterface.addIndex('checkpoint_progress', ['checkpoint_id']);
    await queryInterface.addIndex('checkpoint_progress', ['user_id']);
    await queryInterface.addIndex('checkpoint_progress', ['project_id']);
    await queryInterface.addIndex('checkpoint_progress', ['status']);
    await queryInterface.addIndex('checkpoint_progress', ['checkpoint_id', 'user_id', 'project_id'], {
      unique: true,
      name: 'checkpoint_progress_unique'
    });

    // Create checkpoint_grades table
    await queryInterface.createTable('checkpoint_grades', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      checkpoint_progress_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'checkpoint_progress',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      evaluator_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      rubric_scores: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {}
      },
      total_score: {
        type: Sequelize.DECIMAL(8, 2),
        allowNull: false
      },
      max_score: {
        type: Sequelize.DECIMAL(8, 2),
        allowNull: false
      },
      percentage: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false
      },
      letter_grade: {
        type: Sequelize.STRING(2),
        allowNull: true
      },
      feedback: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      detailed_feedback: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      auto_graded_components: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      grading_time_minutes: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      is_final: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      graded_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      released_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create indexes for checkpoint_grades table
    await queryInterface.addIndex('checkpoint_grades', ['checkpoint_progress_id']);
    await queryInterface.addIndex('checkpoint_grades', ['evaluator_id']);
    await queryInterface.addIndex('checkpoint_grades', ['percentage']);
    await queryInterface.addIndex('checkpoint_grades', ['letter_grade']);
    await queryInterface.addIndex('checkpoint_grades', ['is_final']);
    await queryInterface.addIndex('checkpoint_grades', ['graded_at']);
  },

  async down(queryInterface, Sequelize) {
    // Drop tables in reverse order
    await queryInterface.dropTable('checkpoint_grades');
    await queryInterface.dropTable('checkpoint_progress');
    await queryInterface.dropTable('checkpoint_goals');
    await queryInterface.dropTable('checkpoints');
  }
};
