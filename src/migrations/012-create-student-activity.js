export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('student_activities', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      student_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      project_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'projects',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      course_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'courses',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      activity_type: {
        type: Sequelize.ENUM('project_started', 'checkpoint_submitted', 'project_completed', 'grade_received', 'feedback_received', 'deadline_approaching', 'course_enrolled'),
        allowNull: false
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      timestamp: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      is_read: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('student_activities', ['student_id']);
    await queryInterface.addIndex('student_activities', ['project_id']);
    await queryInterface.addIndex('student_activities', ['course_id']);
    await queryInterface.addIndex('student_activities', ['activity_type']);
    await queryInterface.addIndex('student_activities', ['timestamp']);
    await queryInterface.addIndex('student_activities', ['is_read']);
    
    // Add composite index for efficient queries
    await queryInterface.addIndex('student_activities', ['student_id', 'timestamp']);
    await queryInterface.addIndex('student_activities', ['student_id', 'activity_type']);

    // Add comments
    await queryInterface.sequelize.query(`
      COMMENT ON TABLE student_activities IS 'Tracks student activities and notifications';
      COMMENT ON COLUMN student_activities.activity_type IS 'Type of activity performed by the student';
      COMMENT ON COLUMN student_activities.metadata IS 'Additional activity metadata and context information';
      COMMENT ON COLUMN student_activities.is_read IS 'Whether the student has read this activity notification';
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('student_activities');
  }
};
