import express from 'express';
import {
  createEnhancedProject,
  createProjectTemplate,
  getProjectTemplates,
  assignUsersToProject,
  getProjectAssignments,
  removeUserAssignment,
  publishProject,
  unpublishProject,
  saveProjectAsDraft,
  getProjectWithDetails,
  duplicateProjectFromTemplate,
  rateProjectTemplate,
  getProjectsByUserAssignment,
  getUserProjectWorkload,
  getProjectAssignmentStats
} from '../controllers/enhancedProject.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { body, query, param } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

// Apply JWT middleware to all routes
router.use(jwtMiddleware);

// Create enhanced project
router.post('/enhanced', [
  requirePermissions(['create_projects']),
  body('title')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Title must be between 2 and 200 characters'),
  body('description')
    .trim()
    .notEmpty()
    .withMessage('Description is required'),
  body('courseId')
    .isUUID()
    .withMessage('Valid course ID is required'),
  body('projectType')
    .optional()
    .isIn(['individual', 'group', 'research', 'competition', 'tutorial'])
    .withMessage('Invalid project type'),
  body('difficultyLevel')
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced'])
    .withMessage('Invalid difficulty level'),
  body('estimatedHours')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Estimated hours must be a positive integer'),
  body('totalPoints')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Total points must be a positive integer'),
  body('dueDate')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid ISO 8601 date'),
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  body('learningObjectives')
    .optional()
    .isString()
    .withMessage('Learning objectives must be an array'),
  body('prerequisites')
    .optional()
    .isString()
    .withMessage('Prerequisites must be an array'),
  body('projectOverview')
    .optional()
    .isString()
    .withMessage('Prerequisites must be an array'),
  body('skillsCovered')
    .optional()
    .isArray()
    .withMessage('Skills covered must be an array'),
  body('technologiesUsed')
    .optional()
    .isArray()
    .withMessage('Technologies used must be an array'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('isTemplate')
    .optional()
    .isBoolean()
    .withMessage('isTemplate must be a boolean'),
  body('assignments')
    .optional()
    .isArray()
    .withMessage('Assignments must be an array'),
  body('categoryId').isUUID().optional(),
  body('instructorIds')
    .optional()
    .isArray()
    .withMessage('instructorIds must be an array of UUIDs'),
  body('instructorIds.*')
    .optional()
    .isUUID()
    .withMessage('Each instructorIds item must be a UUID'),
  body('teachingAssId')
    .optional()
    .isArray()
    .withMessage('teachingAssId must be an array of UUIDs'),
  body('teachingAssId.*')
    .optional()
    .isUUID()
    .withMessage('Each teachingAssId item must be a UUID'),
  body('maxSubmissions')
    .optional()
    .isInt({ min: 0 })
    .withMessage('maxSubmissions must be an integer')
    .toInt(),
  body('lateSubmissionsAllowed')
    .optional()
    .isBoolean()
    .withMessage('lateSubmissionsAllowed must be boolean'),
  body('status').optional().isIn(['draft', 'published', 'archived']),
  body('isScreen')
    .exists()
    .withMessage('is required')
    .isInt({ min: 1, max: 4 })
    .withMessage('must be a number between 1 and 4'),
  body('id')
    .if(body('isScreen').custom(v => Number(v) > 1)) // only run next checks when > 0
    .exists({ checkFalsy: true })
    .withMessage('is required when isScreen > 1')
    .bail()
    .isUUID()
    .withMessage('must be a valid UUID')
], validate, createEnhancedProject);

// Create project template
router.post('/templates', [
  requirePermissions(['create_projects']),
  body('projectId')
    .isUUID()
    .withMessage('Valid project ID is required'),
  body('templateName')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Template name must be between 2 and 100 characters'),
  body('category')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Category cannot be empty'),
  body('difficultyLevel')
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced'])
    .withMessage('Invalid difficulty level'),
  body('totalPoints')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Total points must be a positive integer'),
  body('learningObjectives')
    .optional()
    .isArray()
    .withMessage('Learning objectives must be an array'),
  body('prerequisites')
    .optional()
    .isArray()
    .withMessage('Prerequisites must be an array'),
  body('skillsCovered')
    .optional()
    .isArray()
    .withMessage('Skills covered must be an array'),
  body('technologiesUsed')
    .optional()
    .isArray()
    .withMessage('Technologies used must be an array'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array')
], validate, createProjectTemplate);

// Get project templates
router.get('/templates', [
  requirePermissions(['view_projects']),
  query('category')
    .optional()
    .trim()
    .notEmpty(),
  query('subcategory')
    .optional()
    .trim()
    .notEmpty(),
  query('difficultyLevel')
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced'])
    .withMessage('Invalid difficulty level'),
  query('isFeatured')
    .optional()
    .isBoolean()
    .toBoolean(),
  query('isPublic')
    .optional()
    .isBoolean()
    .toBoolean(),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .toInt(),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .toInt(),
  query('search')
    .optional()
    .trim()
    .notEmpty()
], validate, getProjectTemplates);

// Assign users to project
router.post('/:id/assignments', [
  requirePermissions(['project:assign_users']),
  param('id').isUUID().withMessage('Valid project ID is required'),
  body('assignments')
    .isArray({ min: 1 })
    .withMessage('Assignments array is required with at least one assignment'),
  body('assignments.*.userId')
    .isUUID()
    .withMessage('Each assignment must have a valid user ID'),
  body('assignments.*.role')
    .isIn(['instructor', 'ta', 'reviewer', 'mentor'])
    .withMessage('Each assignment must have a valid role'),
  body('assignments.*.assignmentType')
    .optional()
    .isIn(['primary', 'secondary', 'guest'])
    .withMessage('Invalid assignment type'),
  body('assignments.*.startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  body('assignments.*.endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date')
], validate, assignUsersToProject);

// Get project assignments
router.get('/:id/assignments', [
  requirePermissions(['project:view_assignments']),
  param('id').isUUID().withMessage('Valid project ID is required')
], validate, getProjectAssignments);

// Remove user assignment from project
router.delete('/:id/assignments/:userId', [
  requirePermissions(['project:manage_assignments']),
  param('id').isUUID().withMessage('Valid project ID is required'),
  param('userId').isUUID().withMessage('Valid user ID is required')
], validate, removeUserAssignment);

// Publish project
router.post('/:id/publish', [
  requirePermissions(['project:publish']),
  param('id').isUUID().withMessage('Valid project ID is required')
], validate, publishProject);

// Unpublish project
router.post('/:id/unpublish', [
  requirePermissions(['project:publish']),
  param('id').isUUID().withMessage('Valid project ID is required')
], validate, unpublishProject);

// Save project as draft
router.post('/:id/save-draft', [
  requirePermissions(['project:update']),
  param('id').isUUID().withMessage('Valid project ID is required')
], validate, saveProjectAsDraft);

// Get project with full details
router.get('/:id/details', [
  requirePermissions(['project:read']),
  param('id').isUUID().withMessage('Valid project ID is required')
], validate, getProjectWithDetails);

// Duplicate project from template
router.post('/templates/:id/duplicate', [
  requirePermissions(['project:create']),
  param('id').isUUID().withMessage('Valid template ID is required'),
  body('courseId')
    .isUUID()
    .withMessage('Valid course ID is required'),
  body('title')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Title must be between 2 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Description cannot be empty')
], validate, duplicateProjectFromTemplate);

// Rate project template
router.post('/templates/:id/rate', [
  requirePermissions(['project_template:rate']),
  param('id').isUUID().withMessage('Valid template ID is required'),
  body('rating')
    .isFloat({ min: 0, max: 5 })
    .withMessage('Rating must be between 0 and 5')
], validate, rateProjectTemplate);

// Assignment integration routes
router.get('/assignments/user/:role?', [
  requirePermissions(['project:read']),
  param('role').optional().isIn(['instructor', 'ta', 'reviewer', 'mentor'])
    .withMessage('Invalid role. Must be instructor, ta, reviewer, or mentor')
], validate, getProjectsByUserAssignment);

router.get('/workload', [
  requirePermissions(['project:read'])
], getUserProjectWorkload);

router.get('/:id/assignment-stats', [
  requirePermissions(['project:view_assignments']),
  param('id').isUUID().withMessage('Valid project ID is required')
], validate, getProjectAssignmentStats);

export default router;
