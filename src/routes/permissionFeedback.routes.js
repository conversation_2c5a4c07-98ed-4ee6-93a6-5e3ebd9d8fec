import express from 'express';
import {
  getPermissionSummary,
  getResourcePermissions,
  getAvailableActions,
  checkActionPermission,
  getPermissionRequirements,
  getDetailedPermissions,
  getUIContext
} from '../controllers/permissionFeedback.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { param, body } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

// Apply JWT middleware to all routes
router.use(jwtMiddleware);

/**
 * @swagger
 * tags:
 *   name: Permission Feedback
 *   description: Permission feedback and UI context APIs for better UX
 */

/**
 * @swagger
 * /api/permissions/summary:
 *   get:
 *     summary: Get user's permission summary
 *     tags: [Permission Feedback]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Permission summary retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/summary', getPermissionSummary);

/**
 * @swagger
 * /api/permissions/resource/{resourceType}/{resourceId}:
 *   get:
 *     summary: Get user's permissions for specific resource
 *     tags: [Permission Feedback]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: resourceType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [project, course, user, submission, grade, project_template]
 *         description: Type of resource
 *       - in: path
 *         name: resourceId
 *         required: false
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Resource ID (optional)
 *     responses:
 *       200:
 *         description: Resource permissions retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/resource/:resourceType/:resourceId?', [
  param('resourceType').isIn(['project', 'course', 'user', 'submission', 'grade', 'project_template'])
    .withMessage('Invalid resource type')
], validate, getResourcePermissions);

/**
 * @swagger
 * /api/permissions/actions/{resourceType}/{resourceId}:
 *   get:
 *     summary: Get available actions for user
 *     tags: [Permission Feedback]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: resourceType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [project, course, user, submission, grade, project_template]
 *         description: Type of resource
 *       - in: path
 *         name: resourceId
 *         required: false
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Resource ID (optional)
 *     responses:
 *       200:
 *         description: Available actions retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/actions/:resourceType/:resourceId?', [
  param('resourceType').isIn(['project', 'course', 'user', 'submission', 'grade', 'project_template'])
    .withMessage('Invalid resource type')
], validate, getAvailableActions);

/**
 * @swagger
 * /api/permissions/check-action:
 *   post:
 *     summary: Check if user can perform specific action
 *     tags: [Permission Feedback]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *               - resourceType
 *             properties:
 *               action:
 *                 type: string
 *                 description: Action to check
 *               resourceType:
 *                 type: string
 *                 description: Type of resource
 *               resourceId:
 *                 type: string
 *                 format: uuid
 *                 description: Resource ID (optional)
 *     responses:
 *       200:
 *         description: Action permission checked successfully
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 */
router.post('/check-action', [
  body('action').notEmpty().withMessage('Action is required'),
  body('resourceType').notEmpty().withMessage('Resource type is required'),
  body('resourceId').optional().isUUID().withMessage('Resource ID must be valid UUID')
], validate, checkActionPermission);

/**
 * @swagger
 * /api/permissions/requirements/{action}:
 *   get:
 *     summary: Get permission requirements for specific action
 *     tags: [Permission Feedback]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: action
 *         required: true
 *         schema:
 *           type: string
 *         description: Action to get requirements for
 *     responses:
 *       200:
 *         description: Permission requirements retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/requirements/:action', getPermissionRequirements);

/**
 * @swagger
 * /api/permissions/detailed:
 *   get:
 *     summary: Get user's detailed permissions
 *     tags: [Permission Feedback]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Detailed permissions retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/detailed', getDetailedPermissions);

/**
 * @swagger
 * /api/permissions/ui-context/{resourceType}/{resourceId}:
 *   get:
 *     summary: Get UI context for permissions
 *     tags: [Permission Feedback]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: resourceType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [project, course, user, submission, grade, project_template]
 *         description: Type of resource
 *       - in: path
 *         name: resourceId
 *         required: false
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Resource ID (optional)
 *     responses:
 *       200:
 *         description: UI context retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/ui-context/:resourceType/:resourceId?', [
  param('resourceType').isIn(['project', 'course', 'user', 'submission', 'grade', 'project_template'])
    .withMessage('Invalid resource type')
], validate, getUIContext);

export default router;
