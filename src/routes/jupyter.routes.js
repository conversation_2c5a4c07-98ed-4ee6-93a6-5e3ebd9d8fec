import express from 'express';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import {
  jupyterProxy,
  ensureJupyterToken
} from '../middlewares/jupyterhub.middlewares.js';
import jupyterController from '../controllers/jupyter.controller.js';
const router = express.Router();

// All requests to /api/jupyter/* are first authenticated, then passed to the proxy.
router.use('/', jwtMiddleware, ensureJupyterToken, jupyterProxy);

export default router;
