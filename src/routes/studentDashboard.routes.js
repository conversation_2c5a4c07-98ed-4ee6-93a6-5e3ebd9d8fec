import express from 'express';
import {
  getStudentDashboard,
  getProjectProgress,
  getUpcomingDeadlines,
  getRecentActivity,
  getCourseProgress,
  updateProjectProgress,
  markActivityAsRead,
  getUnreadActivityCount,
  getStudentProjectStats,
  getStudentProjectOverview
} from '../controllers/studentDashboard.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { query, param, body } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

router.use(jwtMiddleware);

/**
 * @swagger
 * tags:
 *   name: Student Dashboard
 *   description: Student dashboard and progress tracking endpoints
 */

/**
 * @swagger
 * /api/student/dashboard:
 *   get:
 *     summary: Get comprehensive student dashboard
 *     tags: [Student Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: courseId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific course (optional)
 *     responses:
 *       200:
 *         description: Student dashboard retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     studentId:
 *                       type: string
 *                       format: uuid
 *                     projectOverview:
 *                       type: object
 *                     upcomingDeadlines:
 *                       type: array
 *                     recentActivity:
 *                       type: array
 *                     courseProgress:
 *                       type: array
 *                     lastUpdated:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal Server Error
 */
router.get('/dashboard', [
  requirePermissions(['view_projects']),
  query('courseId').optional().isUUID().withMessage('Valid course ID is required')
], validate, getStudentDashboard);

/**
 * @swagger
 * /api/student/projects/{projectId}/progress:
 *   get:
 *     summary: Get student's progress for specific project
 *     tags: [Student Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Project ID
 *     responses:
 *       200:
 *         description: Project progress retrieved successfully
 *       404:
 *         description: Project progress not found
 *       500:
 *         description: Internal Server Error
 */
router.get('/projects/:projectId/progress', [
  requirePermissions(['view_projects']),
  param('projectId').isUUID().withMessage('Valid project ID is required')
], validate, getProjectProgress);

/**
 * @swagger
 * /api/student/projects/overview:
 *   get:
 *     summary: Get student's project overview
 *     tags: [Student Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: courseId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific course (optional)
 *     responses:
 *       200:
 *         description: Project overview retrieved successfully
 *       500:
 *         description: Internal Server Error
 */
router.get('/projects/overview', [
  requirePermissions(['view_projects']),
  query('courseId').optional().isUUID().withMessage('Valid course ID is required')
], validate, getStudentProjectOverview);

/**
 * @swagger
 * /api/student/projects/stats:
 *   get:
 *     summary: Get student's project statistics
 *     tags: [Student Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: courseId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific course (optional)
 *     responses:
 *       200:
 *         description: Project statistics retrieved successfully
 *       500:
 *         description: Internal Server Error
 */
router.get('/projects/stats', [
  requirePermissions(['view_projects']),
  query('courseId').optional().isUUID().withMessage('Valid course ID is required')
], validate, getStudentProjectStats);

/**
 * @swagger
 * /api/student/deadlines:
 *   get:
 *     summary: Get upcoming project deadlines
 *     tags: [Student Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 365
 *           default: 30
 *         description: Number of days to look ahead
 *     responses:
 *       200:
 *         description: Upcoming deadlines retrieved successfully
 *       500:
 *         description: Internal Server Error
 */
router.get('/deadlines', [
  requirePermissions(['view_projects']),
  query('days').optional().isInt({ min: 1, max: 365 }).withMessage('Days must be between 1 and 365')
], validate, getUpcomingDeadlines);

/**
 * @swagger
 * /api/student/activity:
 *   get:
 *     summary: Get student's recent activity
 *     tags: [Student Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of activities to return
 *       - in: query
 *         name: activityType
 *         schema:
 *           type: string
 *           enum: [project_started, checkpoint_submitted, project_completed, grade_received, feedback_received, deadline_approaching, course_enrolled]
 *         description: Filter by activity type (optional)
 *     responses:
 *       200:
 *         description: Recent activity retrieved successfully
 *       500:
 *         description: Internal Server Error
 */
router.get('/activity', [
  requirePermissions(['view_projects']),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('activityType').optional().isIn(['project_started', 'checkpoint_submitted', 'project_completed', 'grade_received', 'feedback_received', 'deadline_approaching', 'course_enrolled'])
    .withMessage('Invalid activity type')
], validate, getRecentActivity);

/**
 * @swagger
 * /api/student/activity/unread-count:
 *   get:
 *     summary: Get count of unread activities
 *     tags: [Student Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Unread count retrieved successfully
 *       500:
 *         description: Internal Server Error
 */
router.get('/activity/unread-count', [
  requirePermissions(['view_projects'])
], getUnreadActivityCount);

/**
 * @swagger
 * /api/student/courses/{courseId}/progress:
 *   get:
 *     summary: Get student's progress for specific course
 *     tags: [Student Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: courseId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Course ID
 *     responses:
 *       200:
 *         description: Course progress retrieved successfully
 *       500:
 *         description: Internal Server Error
 */
router.get('/courses/:courseId/progress', [
  requirePermissions(['view_projects']),
  param('courseId').isUUID().withMessage('Valid course ID is required')
], validate, getCourseProgress);

/**
 * @swagger
 * /api/student/projects/{projectId}/progress:
 *   put:
 *     summary: Update project progress
 *     tags: [Student Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Project ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - courseId
 *             properties:
 *               courseId:
 *                 type: string
 *                 format: uuid
 *                 description: Course ID
 *               progressPercentage:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *                 description: Progress percentage (0-100)
 *               currentPhase:
 *                 type: string
 *                 description: Current project phase
 *               timeSpent:
 *                 type: number
 *                 minimum: 0
 *                 description: Time spent in hours
 *               status:
 *                 type: string
 *                 enum: [not_started, in_progress, completed, overdue]
 *                 description: Project status
 *     responses:
 *       200:
 *         description: Project progress updated successfully
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal Server Error
 */
router.put('/projects/:projectId/progress', [
  requirePermissions(['view_projects']),
  param('projectId').isUUID().withMessage('Valid project ID is required'),
  body('courseId').isUUID().withMessage('Valid course ID is required'),
  body('progressPercentage').optional().isFloat({ min: 0, max: 100 }).withMessage('Progress percentage must be between 0 and 100'),
  body('timeSpent').optional().isFloat({ min: 0 }).withMessage('Time spent must be a positive number'),
  body('status').optional().isIn(['not_started', 'in_progress', 'completed', 'overdue']).withMessage('Invalid status')
], validate, updateProjectProgress);

/**
 * @swagger
 * /api/student/activity/{activityId}/read:
 *   put:
 *     summary: Mark activity as read
 *     tags: [Student Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: activityId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Activity ID
 *     responses:
 *       200:
 *         description: Activity marked as read successfully
 *       404:
 *         description: Activity not found
 *       500:
 *         description: Internal Server Error
 */
router.put('/activity/:activityId/read', [
  requirePermissions(['view_projects']),
  param('activityId').isUUID().withMessage('Valid activity ID is required')
], validate, markActivityAsRead);

export default router;
