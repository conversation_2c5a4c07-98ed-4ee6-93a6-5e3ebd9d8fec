import express from 'express';
import {
  createProjectAssignment,
  getProjectAssignments,
  getProjectAssignment,
  updateProjectAssignment,
  deleteProjectAssignment,
  getUserAssignments,
  bulkCreateAssignments,
  getProjectAssignmentHistory,
  getAssignmentStats
} from '../controllers/projectAssignment.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { query, param, body } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

// Apply JWT middleware to all routes
router.use(jwtMiddleware);

/**
 * @swagger
 * components:
 *   schemas:
 *     ProjectAssignment:
 *       type: object
 *       required:
 *         - project_id
 *         - user_id
 *         - role
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the assignment
 *         project_id:
 *           type: string
 *           format: uuid
 *           description: ID of the project
 *         user_id:
 *           type: string
 *           format: uuid
 *           description: ID of the assigned user
 *         role:
 *           type: string
 *           enum: [instructor, ta, reviewer, mentor]
 *           description: Role of the user in the project
 *         assignment_type:
 *           type: string
 *           enum: [primary, secondary, guest]
 *           default: primary
 *           description: Type of assignment
 *         permissions:
 *           type: object
 *           description: Specific permissions for this user
 *         assigned_at:
 *           type: string
 *           format: date-time
 *           description: When the assignment was made
 *         assigned_by:
 *           type: string
 *           format: uuid
 *           description: User who made the assignment
 *         start_date:
 *           type: string
 *           format: date-time
 *           description: When the assignment becomes active
 *         end_date:
 *           type: string
 *           format: date-time
 *           description: When the assignment expires
 *         is_active:
 *           type: boolean
 *           default: true
 *           description: Whether the assignment is currently active
 *         notes:
 *           type: string
 *           description: Additional notes about the assignment
 *         metadata:
 *           type: object
 *           description: Additional assignment metadata
 *         project:
 *           $ref: '#/components/schemas/Project'
 *         assignedUser:
 *           $ref: '#/components/schemas/User'
 *         assignedBy:
 *           $ref: '#/components/schemas/User'
 */

/**
 * @swagger
 * /api/project-assignments:
 *   post:
 *     summary: Create a new project assignment
 *     tags: [ProjectAssignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - project_id
 *               - user_id
 *               - role
 *             properties:
 *               project_id:
 *                 type: string
 *                 format: uuid
 *               user_id:
 *                 type: string
 *                 format: uuid
 *               role:
 *                 type: string
 *                 enum: [instructor, ta, reviewer, mentor]
 *               assignment_type:
 *                 type: string
 *                 enum: [primary, secondary, guest]
 *                 default: primary
 *               permissions:
 *                 type: object
 *               start_date:
 *                 type: string
 *                 format: date-time
 *               end_date:
 *                 type: string
 *                 format: date-time
 *               notes:
 *                 type: string
 *               metadata:
 *                 type: object
 *     responses:
 *       201:
 *         description: Project assignment created successfully
 *       400:
 *         description: Invalid input data
 *       404:
 *         description: Project or user not found
 *       409:
 *         description: Assignment already exists
 *       500:
 *         description: Internal server error
 */
router.post('/', [
  requirePermissions(['project:assign_users']),
  body('project_id').isUUID().withMessage('Valid project ID is required'),
  body('user_id').isUUID().withMessage('Valid user ID is required'),
  body('role').isIn(['instructor', 'ta', 'reviewer', 'mentor']).withMessage('Valid role is required'),
  body('assignment_type').optional().isIn(['primary', 'secondary', 'guest']).withMessage('Invalid assignment type'),
  body('start_date').optional().isISO8601().withMessage('Start date must be a valid ISO 8601 date'),
  body('end_date').optional().isISO8601().withMessage('End date must be a valid ISO 8601 date')
], validate, createProjectAssignment);

/**
 * @swagger
 * /api/project-assignments:
 *   get:
 *     summary: Get all project assignments with filtering
 *     tags: [ProjectAssignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: project_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by project ID
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by user ID
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [instructor, ta, reviewer, mentor]
 *         description: Filter by role
 *       - in: query
 *         name: assignment_type
 *         schema:
 *           type: string
 *           enum: [primary, secondary, guest]
 *         description: Filter by assignment type
 *       - in: query
 *         name: is_active
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *           default: assigned_at
 *         description: Field to sort by
 *       - in: query
 *         name: sort_order
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Project assignments retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/', [
  requirePermissions(['project:view_assignments']),
  query('project_id').optional().isUUID().withMessage('Valid project ID is required'),
  query('user_id').optional().isUUID().withMessage('Valid user ID is required'),
  query('role').optional().isIn(['instructor', 'ta', 'reviewer', 'mentor']).withMessage('Invalid role'),
  query('assignment_type').optional().isIn(['primary', 'secondary', 'guest']).withMessage('Invalid assignment type'),
  query('is_active').optional().isBoolean().withMessage('is_active must be a boolean'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('sort_by').optional().isString().withMessage('Sort by must be a string'),
  query('sort_order').optional().isIn(['ASC', 'DESC']).withMessage('Sort order must be ASC or DESC')
], validate, getProjectAssignments);

/**
 * @swagger
 * /api/project-assignments/{id}:
 *   get:
 *     summary: Get a specific project assignment
 *     tags: [ProjectAssignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Assignment ID
 *     responses:
 *       200:
 *         description: Project assignment retrieved successfully
 *       404:
 *         description: Assignment not found
 *       500:
 *         description: Internal server error
 */
router.get('/:id', [
  requirePermissions(['project:view_assignments']),
  param('id').isUUID().withMessage('Valid assignment ID is required')
], validate, getProjectAssignment);

/**
 * @swagger
 * /api/project-assignments/{id}:
 *   put:
 *     summary: Update a project assignment
 *     tags: [ProjectAssignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Assignment ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               role:
 *                 type: string
 *                 enum: [instructor, ta, reviewer, mentor]
 *               assignment_type:
 *                 type: string
 *                 enum: [primary, secondary, guest]
 *               permissions:
 *                 type: object
 *               start_date:
 *                 type: string
 *                 format: date-time
 *               end_date:
 *                 type: string
 *                 format: date-time
 *               is_active:
 *                 type: boolean
 *               notes:
 *                 type: string
 *               metadata:
 *                 type: object
 *     responses:
 *       200:
 *         description: Project assignment updated successfully
 *       403:
 *         description: Permission denied
 *       404:
 *         description: Assignment not found
 *       500:
 *         description: Internal server error
 */
router.put('/:id', [
  requirePermissions(['project:manage_assignments']),
  param('id').isUUID().withMessage('Valid assignment ID is required'),
  body('role').optional().isIn(['instructor', 'ta', 'reviewer', 'mentor']).withMessage('Valid role is required'),
  body('assignment_type').optional().isIn(['primary', 'secondary', 'guest']).withMessage('Invalid assignment type'),
  body('start_date').optional().isISO8601().withMessage('Start date must be a valid ISO 8601 date'),
  body('end_date').optional().isISO8601().withMessage('End date must be a valid ISO 8601 date'),
  body('is_active').optional().isBoolean().withMessage('is_active must be a boolean')
], validate, updateProjectAssignment);

/**
 * @swagger
 * /api/project-assignments/{id}:
 *   delete:
 *     summary: Delete a project assignment
 *     tags: [ProjectAssignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Assignment ID
 *     responses:
 *       200:
 *         description: Project assignment deleted successfully
 *       403:
 *         description: Permission denied
 *       404:
 *         description: Assignment not found
 *       500:
 *         description: Internal server error
 */
router.delete('/:id', [
  requirePermissions(['project:manage_assignments']),
  param('id').isUUID().withMessage('Valid assignment ID is required')
], validate, deleteProjectAssignment);

/**
 * @swagger
 * /api/project-assignments/user/{userId}:
 *   get:
 *     summary: Get assignments for a specific user
 *     tags: [ProjectAssignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: User ID
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [instructor, ta, reviewer, mentor]
 *         description: Filter by role
 *       - in: query
 *         name: is_active
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: User assignments retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/user/:userId', [
  requirePermissions(['project:view_assignments']),
  param('userId').isUUID().withMessage('Valid user ID is required'),
  query('role').optional().isIn(['instructor', 'ta', 'reviewer', 'mentor']).withMessage('Invalid role'),
  query('is_active').optional().isBoolean().withMessage('is_active must be a boolean'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
], validate, getUserAssignments);

/**
 * @swagger
 * /api/project-assignments/bulk:
 *   post:
 *     summary: Bulk create project assignments
 *     tags: [ProjectAssignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assignments
 *             properties:
 *               assignments:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - project_id
 *                     - user_id
 *                     - role
 *                   properties:
 *                     project_id:
 *                       type: string
 *                       format: uuid
 *                     user_id:
 *                       type: string
 *                       format: uuid
 *                     role:
 *                       type: string
 *                       enum: [instructor, ta, reviewer, mentor]
 *                     assignment_type:
 *                       type: string
 *                       enum: [primary, secondary, guest]
 *                     permissions:
 *                       type: object
 *                     start_date:
 *                       type: string
 *                       format: date-time
 *                     end_date:
 *                       type: string
 *                       format: date-time
 *                     notes:
 *                       type: string
 *                     metadata:
 *                       type: object
 *     responses:
 *       201:
 *         description: Bulk assignments processed successfully
 *       400:
 *         description: Invalid input data
 *       500:
 *         description: Internal server error
 */
router.post('/bulk', [
  requirePermissions(['project:assign_users']),
  body('assignments').isArray({ min: 1 }).withMessage('Assignments array is required and cannot be empty'),
  body('assignments.*.project_id').isUUID().withMessage('Each assignment must have a valid project ID'),
  body('assignments.*.user_id').isUUID().withMessage('Each assignment must have a valid user ID'),
  body('assignments.*.role').isIn(['instructor', 'ta', 'reviewer', 'mentor']).withMessage('Each assignment must have a valid role')
], validate, bulkCreateAssignments);

/**
 * @swagger
 * /api/project-assignments/project/{projectId}/history:
 *   get:
 *     summary: Get assignment history for a project
 *     tags: [ProjectAssignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Project ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Project assignment history retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/project/:projectId/history', [
  requirePermissions(['project:view_assignments']),
  param('projectId').isUUID().withMessage('Valid project ID is required'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
], validate, getProjectAssignmentHistory);

/**
 * @swagger
 * /api/project-assignments/stats:
 *   get:
 *     summary: Get assignment statistics
 *     tags: [ProjectAssignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: project_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by project ID
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by user ID
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [instructor, ta, reviewer, mentor]
 *         description: Filter by role
 *     responses:
 *       200:
 *         description: Assignment statistics retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/stats', [
  requirePermissions(['project:view_assignments']),
  query('project_id').optional().isUUID().withMessage('Valid project ID is required'),
  query('user_id').optional().isUUID().withMessage('Valid user ID is required'),
  query('role').optional().isIn(['instructor', 'ta', 'reviewer', 'mentor']).withMessage('Invalid role')
], validate, getAssignmentStats);

export default router;
