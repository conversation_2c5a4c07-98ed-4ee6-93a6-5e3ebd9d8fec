import express from 'express';
import {
  createAnnouncement,
  getCourseAnnouncements,
  getAnnouncementById,
  updateAnnouncement,
  publishAnnouncement,
  archiveAnnouncement,
  togglePinStatus,
  deleteAnnouncement,
  getInstructorAnnouncements,
  getStudentAnnouncements,
  scheduleAnnouncement
} from '../controllers/announcement.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { body, query, param } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

// Apply JWT middleware to all routes
router.use(jwtMiddleware);

// Create new announcement (Instructor/Admin only)
router.post('/', [
  requirePermissions(['create_announcements']),
  body('title')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Title must be between 2 and 200 characters'),
  body('content')
    .trim()
    .notEmpty()
    .withMessage('Content is required'),
  body('courseId')
    .isUUID()
    .withMessage('Valid course ID is required'),
  body('announcementType')
    .optional()
    .isIn(['general', 'project_update', 'deadline_reminder', 'course_update', 'important', 'urgent'])
    .withMessage('Invalid announcement type'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority level'),
  body('isPinned')
    .optional()
    .isBoolean()
    .withMessage('isPinned must be a boolean'),
  body('scheduledFor')
    .optional()
    .isISO8601()
    .withMessage('scheduledFor must be a valid ISO 8601 date'),
  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('expiresAt must be a valid ISO 8601 date'),
  body('targetAudience')
    .optional()
    .isArray()
    .withMessage('targetAudience must be an array'),
  body('attachments')
    .optional()
    .isArray()
    .withMessage('attachments must be an array')
], validate, createAnnouncement);

// Get course announcements
router.get('/course/:courseId', [
  requirePermissions(['view_announcements']),
  param('courseId').isUUID().withMessage('Valid course ID is required'),
  query('status')
    .optional()
    .isIn(['all', 'draft', 'published', 'archived'])
    .withMessage('Invalid status'),
  query('announcementType')
    .optional()
    .isIn(['general', 'project_update', 'deadline_reminder', 'course_update', 'important', 'urgent'])
    .withMessage('Invalid announcement type'),
  query('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority level'),
  query('isPinned')
    .optional()
    .isBoolean()
    .toBoolean(),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .toInt(),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .toInt(),
  query('includeExpired')
    .optional()
    .isBoolean()
    .toBoolean()
], validate, getCourseAnnouncements);

// Get announcement by ID
router.get('/:id', [
  requirePermissions(['view_announcements']),
  param('id').isUUID().withMessage('Valid announcement ID is required')
], validate, getAnnouncementById);

// Update announcement (Creator/Admin only)
router.put('/:id', [
  requirePermissions(['edit_announcements']),
  param('id').isUUID().withMessage('Valid announcement ID is required'),
  body('title')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Title must be between 2 and 200 characters'),
  body('content')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Content cannot be empty'),
  body('announcementType')
    .optional()
    .isIn(['general', 'project_update', 'deadline_reminder', 'course_update', 'important', 'urgent'])
    .withMessage('Invalid announcement type'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority level'),
  body('isPinned')
    .optional()
    .isBoolean()
    .withMessage('isPinned must be a boolean'),
  body('scheduledFor')
    .optional()
    .isISO8601()
    .withMessage('scheduledFor must be a valid ISO 8601 date'),
  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('expiresAt must be a valid ISO 8601 date'),
  body('targetAudience')
    .optional()
    .isArray()
    .withMessage('targetAudience must be an array'),
  body('attachments')
    .optional()
    .isArray()
    .withMessage('attachments must be an array')
], validate, updateAnnouncement);

// Publish announcement
router.post('/:id/publish', [
  requirePermissions(['publish_announcements']),
  param('id').isUUID().withMessage('Valid announcement ID is required')
], validate, publishAnnouncement);

// Archive announcement (Creator/Admin only)
router.post('/:id/archive', [
  requirePermissions(['edit_announcements']),
  param('id').isUUID().withMessage('Valid announcement ID is required')
], validate, archiveAnnouncement);

// Toggle pin status (Creator/Admin only)
router.post('/:id/toggle-pin', [
  requirePermissions(['edit_announcements']),
  param('id').isUUID().withMessage('Valid announcement ID is required')
], validate, togglePinStatus);

// Delete announcement (Creator/Admin only)
router.delete('/:id', [
  requirePermissions(['delete_announcements']),
  param('id').isUUID().withMessage('Valid announcement ID is required')
], validate, deleteAnnouncement);

// Get instructor announcements
router.get('/instructor', [
  requirePermissions(['view_announcements']),
  query('courseId')
    .optional()
    .isUUID()
    .withMessage('Valid course ID is required')
], validate, getInstructorAnnouncements);

// Get student announcements
router.get('/student', [
  requirePermissions(['view_announcements']),
  query('courseId')
    .optional()
    .isUUID()
    .withMessage('Valid course ID is required')
], validate, getStudentAnnouncements);

// Schedule announcement (Creator/Admin only)
router.post('/:id/schedule', [
  requirePermissions(['edit_announcements']),
  param('id').isUUID().withMessage('Valid announcement ID is required'),
  body('scheduledFor')
    .isISO8601()
    .withMessage('scheduledFor must be a valid ISO 8601 date')
], validate, scheduleAnnouncement);

export default router;
