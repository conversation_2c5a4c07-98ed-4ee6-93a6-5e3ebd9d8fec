import express from 'express';
import { Role, Permission, User } from '../models/associations.js';
import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import { requireRoles } from '../middlewares/rbac.middlewares.js';
import { body, param, query } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Roles
 *   description: Role and permission management
 */

/**
 * @desc    Get all roles
 * @route   GET /api/roles
 * @access  Private (Admin)
 */
const getRoles = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    search,
    sortBy = 'name',
    sortOrder = 'asc'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const whereClause = {};

  if (search) {
    whereClause[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  const { count, rows: roles } = await Role.findAndCountAll({
    where: whereClause,
    include: [{
      model: Permission,
      as: 'permissions',
      through: { attributes: [] }
    }],
    limit: parseInt(limit),
    offset,
    order: [[sortBy, sortOrder.toUpperCase()]]
  });

  const transformedRoles = roles.map(role => ({
    id: role.id,
    name: role.name,
    description: role.description,
    lmsRoleReference: role.lms_role_reference,
    isSystemRole: role.is_system_role,
    priority: role.priority,
    permissions: role.permissions?.map(permission => ({
      id: permission.id,
      key: permission.key,
      name: permission.name,
      category: permission.category
    })) || [],
    createdAt: role.created_at,
    updatedAt: role.updated_at
  }));

  res.json({
    success: true,
    data: {
      roles: transformedRoles,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / parseInt(limit)),
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

/**
 * @desc    Get role by ID
 * @route   GET /api/roles/:id
 * @access  Private (Admin)
 */
const getRoleById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const role = await Role.findByPk(id, {
    include: [{
      model: Permission,
      as: 'permissions',
      through: { attributes: [] }
    }, {
      model: User,
      as: 'users',
      through: { attributes: ['is_primary'] },
      attributes: ['id', 'name', 'email']
    }]
  });

  if (!role) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Role not found'
    });
  }

  const roleResponse = {
    id: role.id,
    name: role.name,
    description: role.description,
    lmsRoleReference: role.lms_role_reference,
    isSystemRole: role.is_system_role,
    priority: role.priority,
    metadata: role.metadata,
    permissions: role.permissions?.map(permission => ({
      id: permission.id,
      key: permission.key,
      name: permission.name,
      description: permission.description,
      category: permission.category
    })) || [],
    users: role.users?.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      isPrimary: user.UserRole?.is_primary
    })) || [],
    createdAt: role.created_at,
    updatedAt: role.updated_at
  };

  res.json({
    success: true,
    role: roleResponse
  });
});

/**
 * @desc    Create new role
 * @route   POST /api/roles
 * @access  Private (Admin)
 */
const createRole = asyncHandler(async (req, res) => {
  const {
    name,
    description,
    lmsRoleReference,
    priority = 0,
    permissionIds = []
  } = req.body;

  // Check if role name already exists
  const existingRole = await Role.findOne({ where: { name } });
  if (existingRole) {
    return res.status(409).json({
      error: 'Conflict',
      message: 'Role name already exists'
    });
  }

  // Create role
  const role = await Role.create({
    name,
    description,
    lms_role_reference: lmsRoleReference,
    priority,
    is_system_role: false
  });

  // Assign permissions if provided
  if (permissionIds.length > 0) {
    const permissions = await Permission.findAll({
      where: { id: { [Op.in]: permissionIds } }
    });
    await role.setPermissions(permissions);
  }

  logger.info(`Role created: ${name} by ${req.user.email}`);

  res.status(201).json({
    success: true,
    message: 'Role created successfully',
    role: {
      id: role.id,
      name: role.name,
      description: role.description,
      createdAt: role.created_at
    }
  });
});

/**
 * @desc    Remove permission from role
 * @route   DELETE /api/roles/:id/permissions/:permissionId
 * @access  Private (Admin)
 */
const removePermissionFromRole = asyncHandler(async (req, res) => {
  const { id, permissionId } = req.params;

  const role = await Role.findByPk(id);
  if (!role) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Role not found'
    });
  }

  const permission = await Permission.findByPk(permissionId);
  if (!permission) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Permission not found'
    });
  }

  await role.removePermission(permission);

  logger.info(`Permission removed from role: ${permission.key} from ${role.name} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Permission removed successfully'
  });
});

/**
 * @desc    Get all permissions
 * @route   GET /api/roles/permissions
 * @access  Private (Admin)
 */
const getPermissions = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 50,
    category,
    search
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const whereClause = {};

  if (category) {
    whereClause.category = category;
  }

  if (search) {
    whereClause[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { key: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  const { count, rows: permissions } = await Permission.findAndCountAll({
    where: whereClause,
    limit: parseInt(limit),
    offset,
    order: [['category', 'ASC'], ['name', 'ASC']]
  });

  res.json({
    success: true,
    data: {
      permissions: permissions.map(permission => ({
        id: permission.id,
        key: permission.key,
        name: permission.name,
        description: permission.description,
        category: permission.category,
        isSystemPermission: permission.is_system_permission
      })),
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / parseInt(limit)),
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

// Route definitions
router.get('/', [requireRoles(['admin']), query('search').optional().trim()], validate, getRoles);
router.get('/permissions', [requireRoles(['admin'])], validate, getPermissions);
router.get('/:id', [requireRoles(['admin']), param('id').isUUID()], validate, getRoleById);
router.post('/', [requireRoles(['admin']), body('name').isLength({ min: 2, max: 50 }).trim()], validate, createRole);
router.delete('/:id/permissions/:permissionId', [requireRoles(['admin']), param('id').isUUID(), param('permissionId').isUUID()], validate, removePermissionFromRole);

export default router;