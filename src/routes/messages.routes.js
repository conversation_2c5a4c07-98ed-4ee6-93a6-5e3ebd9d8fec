import express from 'express';
import {
  sendMessage,
  replyToMessage,
  getInbox,
  getSentMessages,
  getConversationThread,
  markAsRead,
  markMultipleAsRead,
  getMessageById,
  updateMessage,
  deleteMessage,
  getUnreadCount,
  getInstructorMessageStats,
  sendCourseAnnouncement
} from '../controllers/message.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { body, query, param } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

// Apply JWT middleware to all routes
router.use(jwtMiddleware);

// Send a new message
router.post('/', [
  requirePermissions(['send_messages']),
  body('recipientId')
    .isUUID()
    .withMessage('Valid recipient ID is required'),
  body('subject')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Subject must be between 2 and 200 characters'),
  body('content')
    .trim()
    .notEmpty()
    .withMessage('Content is required'),
  body('messageType')
    .optional()
    .isIn(['personal', 'course_related', 'project_related', 'system', 'notification'])
    .withMessage('Invalid message type'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority level'),
  body('courseId')
    .optional()
    .isUUID()
    .withMessage('Valid course ID is required'),
  body('projectId')
    .optional()
    .isUUID()
    .withMessage('Valid project ID is required'),
  body('attachments')
    .optional()
    .isArray()
    .withMessage('Attachments must be an array')
], validate, sendMessage);

// Reply to a message
router.post('/:id/reply', [
  requirePermissions(['send_messages']),
  param('id').isUUID().withMessage('Valid message ID is required'),
  body('subject')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Subject must be between 2 and 200 characters'),
  body('content')
    .trim()
    .notEmpty()
    .withMessage('Content is required'),
  body('attachments')
    .optional()
    .isArray()
    .withMessage('Attachments must be an array')
], validate, replyToMessage);

// Get user's inbox
router.get('/inbox', [
  requirePermissions(['view_messages']),
  query('status')
    .optional()
    .isIn(['all', 'sent', 'delivered', 'read', 'archived'])
    .withMessage('Invalid status'),
  query('messageType')
    .optional()
    .isIn(['personal', 'course_related', 'project_related', 'system', 'notification'])
    .withMessage('Invalid message type'),
  query('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority level'),
  query('isRead')
    .optional()
    .isBoolean()
    .toBoolean(),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .toInt(),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .toInt(),
  query('courseId')
    .optional()
    .isUUID()
    .withMessage('Valid course ID is required'),
  query('projectId')
    .optional()
    .isUUID()
    .withMessage('Valid project ID is required')
], validate, getInbox);

// Get user's sent messages
router.get('/sent', [
  requirePermissions(['view_messages']),
  query('messageType')
    .optional()
    .isIn(['personal', 'course_related', 'project_related', 'system', 'notification'])
    .withMessage('Invalid message type'),
  query('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority level'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .toInt(),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .toInt(),
  query('courseId')
    .optional()
    .isUUID()
    .withMessage('Valid course ID is required'),
  query('projectId')
    .optional()
    .isUUID()
    .withMessage('Valid project ID is required')
], validate, getSentMessages);

// Get conversation thread
router.get('/thread/:threadId', [
  requirePermissions(['view_messages']),
  param('threadId').isUUID().withMessage('Valid thread ID is required')
], validate, getConversationThread);

// Mark message as read
router.post('/:id/read', [
  requirePermissions(['view_messages']),
  param('id').isUUID().withMessage('Valid message ID is required')
], validate, markAsRead);

// Mark multiple messages as read
router.post('/mark-read', [
  requirePermissions(['view_messages']),
  body('messageIds')
    .isArray({ min: 1 })
    .withMessage('Message IDs array is required with at least one ID'),
  body('messageIds.*')
    .isUUID()
    .withMessage('Each message ID must be a valid UUID')
], validate, markMultipleAsRead);

// Get message by ID
router.get('/:id', [
  requirePermissions(['view_messages']),
  param('id').isUUID().withMessage('Valid message ID is required')
], validate, getMessageById);

// Update message (Sender only)
router.put('/:id', [
  requirePermissions(['edit_messages']),
  param('id').isUUID().withMessage('Valid message ID is required'),
  body('subject')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Subject must be between 2 and 200 characters'),
  body('content')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Content cannot be empty'),
  body('attachments')
    .optional()
    .isArray()
    .withMessage('Attachments must be an array')
], validate, updateMessage);

// Delete message (Sender only)
router.delete('/:id', [
  requirePermissions(['delete_messages']),
  param('id').isUUID().withMessage('Valid message ID is required')
], validate, deleteMessage);

// Get unread message count
router.get('/unread-count', [
  requirePermissions(['view_messages'])
], getUnreadCount);

// Get instructor message statistics
router.get('/instructor/stats', [
  requirePermissions(['view_messages'])
], getInstructorMessageStats);

// Send course-wide announcement message (Instructor/Admin only)
router.post('/course-announcement', [
  requirePermissions(['send_course_announcements']),
  body('courseId')
    .isUUID()
    .withMessage('Valid course ID is required'),
  body('subject')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Subject must be between 2 and 200 characters'),
  body('content')
    .trim()
    .notEmpty()
    .withMessage('Content is required'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority level'),
  body('attachments')
    .optional()
    .isArray()
    .withMessage('Attachments must be an array')
], validate, sendCourseAnnouncement);

export default router;
