import express from 'express';
import {
  getGrades,
  getGradeById,
  createOrUpdateGrade,
  updateGrade,
  deleteGrade,
  getGradingQueue,
  getGradeStatistics,
  bulkGradeSubmissions
} from '../controllers/grade.controller.js';
import { requirePermissions, requireRoles } from '../middlewares/rbac.middlewares.js';
import { body, param, query } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Grades
 *   description: Grade management and evaluation
 */

/**
 * @swagger
 * /api/grades:
 *   get:
 *     summary: Get all grades with pagination and filtering
 *     tags: [Grades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: projectId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by project ID
 *       - in: query
 *         name: courseId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by course ID
 *       - in: query
 *         name: studentId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by student ID
 *       - in: query
 *         name: evaluatorId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by evaluator ID
 *     responses:
 *       200:
 *         description: Grades retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */
router.get('/', [
  requirePermissions(['view_grades']),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('projectId').optional().isUUID(),
  query('courseId').optional().isUUID(),
  query('studentId').optional().isUUID(),
  query('evaluatorId').optional().isUUID()
], validate, getGrades);

/**
 * @swagger
 * /api/grades/{id}:
 *   get:
 *     summary: Get grade by ID
 *     tags: [Grades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Grade retrieved successfully
 *       404:
 *         description: Grade not found
 *       403:
 *         description: Access denied
 */
router.get('/:id', [
  param('id').isUUID()
], validate, getGradeById);

/**
 * @swagger
 * /api/grades:
 *   post:
 *     summary: Create or update grade
 *     tags: [Grades]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - submissionId
 *               - totalScore
 *             properties:
 *               submissionId:
 *                 type: string
 *                 format: uuid
 *               totalScore:
 *                 type: number
 *                 minimum: 0
 *               maxScore:
 *                 type: number
 *                 minimum: 0
 *               percentage:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *               letterGrade:
 *                 type: string
 *               feedback:
 *                 type: string
 *               rubricScores:
 *                 type: object
 *                 description: Scores for individual rubric criteria
 *     responses:
 *       200:
 *         description: Grade saved successfully
 *       400:
 *         description: Validation error
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Submission not found
 */
router.post('/', [
  requirePermissions(['grade_submissions']),
  body('submissionId').isUUID(),
  body('totalScore').isFloat({ min: 0 }),
  body('maxScore').optional().isFloat({ min: 0 }),
  body('percentage').optional().isFloat({ min: 0, max: 100 }),
  body('letterGrade').optional().isString(),
  body('feedback').optional().isString(),
  body('rubricScores').optional().isObject()
], validate, createOrUpdateGrade);

/**
 * @swagger
 * /api/grades/{id}:
 *   put:
 *     summary: Update grade
 *     tags: [Grades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               totalScore:
 *                 type: number
 *                 minimum: 0
 *               maxScore:
 *                 type: number
 *                 minimum: 0
 *               percentage:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *               letterGrade:
 *                 type: string
 *               feedback:
 *                 type: string
 *               rubricScores:
 *                 type: object
 *                 description: Scores for individual rubric criteria
 *     responses:
 *       200:
 *         description: Grade updated successfully
 *       400:
 *         description: Validation error
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Grade not found
 */
router.put('/:id', [
  requirePermissions(['grade_submissions']),
  param('id').isUUID(),
  body('totalScore').optional().isFloat({ min: 0 }),
  body('maxScore').optional().isFloat({ min: 0 }),
  body('percentage').optional().isFloat({ min: 0, max: 100 }),
  body('letterGrade').optional().isString(),
  body('feedback').optional().isString(),
  body('rubricScores').optional().isObject()
], validate, updateGrade);

/**
 * @swagger
 * /api/grades/{id}:
 *   delete:
 *     summary: Delete grade
 *     tags: [Grades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Grade deleted successfully
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Grade not found
 */
router.delete('/:id', [
  requirePermissions(['grade_submissions']),
  param('id').isUUID()
], validate, deleteGrade);

/**
 * @swagger
 * /api/grades/queue:
 *   get:
 *     summary: Get grading queue (submissions ready to grade)
 *     tags: [Grades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: courseId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by course ID
 *       - in: query
 *         name: projectId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by project ID
 *     responses:
 *       200:
 *         description: Grading queue retrieved successfully
 *       403:
 *         description: Insufficient permissions
 */
router.get('/queue', [
  requirePermissions(['grade_submissions']),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('courseId').optional().isUUID(),
  query('projectId').optional().isUUID()
], validate, getGradingQueue);

/**
 * @swagger
 * /api/grades/bulk:
 *   post:
 *     summary: Bulk grade submissions
 *     tags: [Grades]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - submissions
 *             properties:
 *               submissions:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - submissionId
 *                     - totalScore
 *                   properties:
 *                     submissionId:
 *                       type: string
 *                       format: uuid
 *                     totalScore:
 *                       type: number
 *                       minimum: 0
 *                     maxScore:
 *                       type: number
 *                       minimum: 0
 *                     percentage:
 *                       type: number
 *                       minimum: 0
 *                       maximum: 100
 *                     letterGrade:
 *                       type: string
 *                     feedback:
 *                       type: string
 *                     rubricScores:
 *                       type: object
 *     responses:
 *       200:
 *         description: Bulk grading completed
 *       400:
 *         description: Validation error
 *       403:
 *         description: Insufficient permissions
 */
router.post('/bulk', [
  requirePermissions(['grade_submissions']),
  body('submissions').isArray({ min: 1 }),
  body('submissions.*.submissionId').isUUID(),
  body('submissions.*.totalScore').isFloat({ min: 0 }),
  body('submissions.*.maxScore').optional().isFloat({ min: 0 }),
  body('submissions.*.percentage').optional().isFloat({ min: 0, max: 100 }),
  body('submissions.*.letterGrade').optional().isString(),
  body('submissions.*.feedback').optional().isString(),
  body('submissions.*.rubricScores').optional().isObject()
], validate, bulkGradeSubmissions);

/**
 * @swagger
 * /api/grades/project/{projectId}/statistics:
 *   get:
 *     summary: Get grade statistics for a project
 *     tags: [Grades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Project not found
 */
router.get('/project/:projectId/statistics', [
  requirePermissions(['view_grades']),
  param('projectId').isUUID()
], validate, getGradeStatistics);

export default router;