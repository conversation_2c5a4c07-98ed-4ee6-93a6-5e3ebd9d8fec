import express from 'express';
import {
  getInstructorDashboard,
  getRecentActivity,
  getInstructorProjects,
  getProjectDetailedStats,
  getCourseOverview,
  getUserActivitySummary,
  getActivityAnalytics
} from '../controllers/instructorDashboard.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { body, query, param } from 'express-validator';

const router = express.Router();

// Apply JWT authentication to all routes
router.use(jwtMiddleware);

// Dashboard overview
router.get('/dashboard', [
  query('courseId').optional().isUUID().withMessage('Invalid course ID format')
], getInstructorDashboard);

// Recent activity
router.get('/activity', [
  query('courseId').optional().isUUID().withMessage('Invalid course ID format'),
  query('projectId').optional().isUUID().withMessage('Invalid project ID format'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
], getRecentActivity);

// Enhanced project listing with statistics
router.get('/projects', [
  query('courseId').optional().isUUID().withMessage('Invalid course ID format'),
  query('status').optional().isIn(['draft', 'published', 'archived']).withMessage('Invalid status'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('includeStats').optional().isBoolean().withMessage('includeStats must be a boolean')
], getInstructorProjects);

// Project detailed statistics
router.get('/projects/:id/stats', [
  param('id').isUUID().withMessage('Invalid project ID format')
], getProjectDetailedStats);

// Course overview
router.get('/courses/:id/overview', [
  param('id').isUUID().withMessage('Invalid course ID format')
], getCourseOverview);

// User activity summary
router.get('/users/:id/activity', [
  param('id').isUUID().withMessage('Invalid user ID format'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
], getUserActivitySummary);

// Activity analytics
router.get('/analytics', [
  query('courseId').optional().isUUID().withMessage('Invalid course ID format'),
  query('startDate').optional().isISO8601().withMessage('Invalid start date format'),
  query('endDate').optional().isISO8601().withMessage('Invalid end date format'),
  query('activityType').optional().isIn([
    'project_created',
    'project_published',
    'project_archived',
    'checkpoint_created',
    'checkpoint_published',
    'checkpoint_submitted',
    'checkpoint_graded',
    'grade_assigned',
    'feedback_given',
    'student_enrolled',
    'student_submitted',
    'student_graded',
    'course_created',
    'course_updated'
  ]).withMessage('Invalid activity type')
], getActivityAnalytics);

export default router;
