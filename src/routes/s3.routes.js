import express from 'express';
import { body, query, param } from 'express-validator';
import { asyncHand<PERSON> } from '../middlewares/errorHandler.middlewares.js';
import { validate } from '../middlewares/validation.middlewares.js';
// import { uploadToS3, generatePresignedUrl, deleteFromS3, listS3Objects } from '../services/s3Service.js';
import multer from 'multer';
import logger from '../config/logger.config.js';
import S3Service from '../services/s3.service.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow specific file types
    const allowedMimes = [
      'text/plain',
      'application/json',
      'text/csv',
      'application/zip',
      'application/x-zip-compressed',
      'application/octet-stream',
      'image/png',
      'image/jpeg',
      'image/gif',
      'application/pdf'
    ];
    
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type'), false);
    }
  }
});

/**
 * @swagger
 * tags:
 *   name: S3
 *   description: File storage and management
 */

/**
 * @desc    Upload file to S3
 * @route   POST /api/s3/upload
 * @access  Private
 */
const uploadFile = asyncHandler(async (req, res) => {
  const { folder = 'general', description } = req.body;
  
  if (!req.file) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'No file provided'
    });
  }

  try {
    const fileName = `${folder}/${req.user.id}/${Date.now()}_${req.file.originalname}`;
    const s3Url = await S3Service.uploadFile(req.file.buffer, fileName, req.file.mimetype);
    
    logger.info(`File uploaded: ${fileName} by ${req.user.email}`);
    
    res.json({
      success: true,
      message: 'File uploaded successfully',
      data: {
        url: s3Url,
        fileName: req.file.originalname,
        size: req.file.size,
        mimeType: req.file.mimetype,
        folder,
        description
      }
    });
  } catch (error) {
    logger.error('File upload error:', error);
    res.status(500).json({
      error: 'Upload Error',
      message: 'Failed to upload file'
    });
  }
});

/**
 * @desc    Generate presigned URL for file access
 * @route   POST /api/s3/presigned-url
 * @access  Private
 */
const generatePresignedURL = asyncHandler(async (req, res) => {
  const { s3Key, expiresIn = 3600 } = req.body;
  
  if (!s3Key) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'S3 key is required'
    });
  }

  try {
    const presignedUrl = await S3Service.generateFilePath(s3Key, expiresIn);
    
    res.json({
      success: true,
      data: {
        url: presignedUrl,
        expiresIn,
        expiresAt: new Date(Date.now() + expiresIn * 1000)
      }
    });
  } catch (error) {
    logger.error('Presigned URL generation error:', error);
    res.status(500).json({
      error: 'URL Generation Error',
      message: 'Failed to generate presigned URL'
    });
  }
});

/**
 * @desc    Delete file from S3
 * @route   DELETE /api/s3/delete
 * @access  Private
 */
const deleteFile = asyncHandler(async (req, res) => {
  const { s3Key } = req.body;
  
  if (!s3Key) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'S3 key is required'
    });
  }

  try {
    await S3Service.deleteFile(s3Key);
    
    logger.info(`File deleted: ${s3Key} by ${req.user.email}`);
    
    res.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    logger.error('File deletion error:', error);
    res.status(500).json({
      error: 'Deletion Error',
      message: 'Failed to delete file'
    });
  }
});

/**
 * @desc    List user files in S3
 * @route   GET /api/s3/files
 * @access  Private
 */
const listUserFiles = asyncHandler(async (req, res) => {
  const { folder = '', limit = 50 } = req.query;
  
  try {
    const prefix = folder ? `${folder}/${req.user.id}/` : `${req.user.id}/`;
    const files = await listS3Objects(prefix, limit);
    
    res.json({
      success: true,
      data: {
        files: files.map(file => ({
          key: file.Key,
          size: file.Size,
          lastModified: file.LastModified,
          fileName: file.Key.split('/').pop()
        })),
        prefix
      }
    });
  } catch (error) {
    logger.error('File listing error:', error);
    res.status(500).json({
      error: 'Listing Error',
      message: 'Failed to list files'
    });
  }
});

/**
 * @desc    Upload notebook file
 * @route   POST /api/s3/upload-notebook
 * @access  Private (Student)
 */
const uploadNotebook = asyncHandler(async (req, res) => {
  const { projectId, notebookContent } = req.body;
  
  if (!projectId || !notebookContent) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Project ID and notebook content are required'
    });
  }

  try {
    const fileName = `notebooks/${req.user.id}/${projectId}/notebook_${Date.now()}.ipynb`;
    const s3Url = await S3Service.uploadFile(
      Buffer.from(notebookContent), 
      fileName, 
      'application/json'
    );
    
    logger.info(`Notebook uploaded: ${fileName} by ${req.user.email}`);
    
    res.json({
      success: true,
      message: 'Notebook uploaded successfully',
      data: {
        url: s3Url,
        fileName: fileName.split('/').pop(),
        projectId
      }
    });
  } catch (error) {
    logger.error('Notebook upload error:', error);
    res.status(500).json({
      error: 'Upload Error',
      message: 'Failed to upload notebook'
    });
  }
});

/**
 * @desc    Upload dataset file
 * @route   POST /api/s3/upload-dataset
 * @access  Private
 */
const uploadDataset = asyncHandler(async (req, res) => {
  const { description, isPublic = false } = req.body;
  
  if (!req.file) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'No dataset file provided'
    });
  }

  try {
    const folder = isPublic ? 'datasets/public' : 'datasets/private';
    const fileName = `${folder}/${req.user.id}/${Date.now()}_${req.file.originalname}`;
    const s3Url = await S3Service.uploadFile(req.file.buffer, fileName, req.file.mimetype);
    
    logger.info(`Dataset uploaded: ${fileName} by ${req.user.email}`);
    
    res.json({
      success: true,
      message: 'Dataset uploaded successfully',
      data: {
        url: s3Url,
        fileName: req.file.originalname,
        size: req.file.size,
        mimeType: req.file.mimetype,
        description,
        isPublic
      }
    });
  } catch (error) {
    logger.error('Dataset upload error:', error);
    res.status(500).json({
      error: 'Upload Error',
      message: 'Failed to upload dataset'
    });
  }
});

// Route definitions
router.post('/upload', upload.single('file'), uploadFile);
router.post('/upload-notebook', [
  body('projectId').isUUID(),
  body('notebookContent').isString()
], validate, uploadNotebook);
router.post('/upload-dataset', upload.single('dataset'), uploadDataset);
router.post('/presigned-url', [
  body('s3Key').isString(),
  body('expiresIn').optional().isInt({ min: 60, max: 86400 })
], validate, generatePresignedURL);
router.delete('/delete', [
  body('s3Key').isString()
], validate, deleteFile);
router.get('/files', [
  query('folder').optional().isString(),
  query('limit').optional().isInt({ min: 1, max: 100 })
], validate, listUserFiles);

/**
 * @desc    Upload project notebook template file
 * @route   POST /api/s3/upload-project-template
 * @access  Private (Instructor/Admin)
 * @form    multipart/form-data with field name: template
 */
const uploadProjectTemplate = asyncHandler(async (req, res) => {
  const { courseId, projectId } = req.body;

  if (!req.file) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'No template file provided'
    });
  }

  if (!courseId || !projectId) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'courseId and projectId are required in form fields'
    });
  }

  // Validate file type (ipynb, py, zip allowed for template)
  const errors = S3Service.validateFile(req.file, ['ipynb', 'py', 'zip', 'json'], 50 * 1024 * 1024);
  if (errors.length) {
    return res.status(400).json({ error: 'Validation Error', message: errors.join('; ') });
  }

  try {
    const result = await S3Service.uploadFile(req.file, 'project-template', req.user.id, { courseId, projectId });
    logger.info(`Project template uploaded for project ${projectId} by ${req.user.email}`);
    return res.json({ success: true, data: result });
  } catch (error) {
    logger.error('Project template upload error:', error);
    return res.status(500).json({ error: 'Upload Error', message: 'Failed to upload project template' });
  }
});

/**
 * @desc    Upload project dataset file
 * @route   POST /api/s3/upload-project-dataset
 * @access  Private (Instructor/Admin)
 * @form    multipart/form-data with field name: dataset
 */
const uploadProjectDataset = asyncHandler(async (req, res) => {
  const { courseId, projectId, isPublic = false } = req.body;

  if (!req.file) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'No dataset file provided'
    });
  }

  if (!courseId || !projectId) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'courseId and projectId are required in form fields'
    });
  }

  // Validate file type (csv, json, zip allowed for dataset)
  const errors = S3Service.validateFile(req.file, ['csv', 'json', 'zip', 'parquet'], 200 * 1024 * 1024);
  if (errors.length) {
    return res.status(400).json({ error: 'Validation Error', message: errors.join('; ') });
  }

  try {
    const result = await S3Service.uploadFile(req.file, 'project-dataset', req.user.id, { courseId, projectId });
    logger.info(`Project dataset uploaded for project ${projectId} by ${req.user.email}`);
    return res.json({ success: true, data: { ...result, isPublic: Boolean(isPublic) } });
  } catch (error) {
    logger.error('Project dataset upload error:', error);
    return res.status(500).json({ error: 'Upload Error', message: 'Failed to upload project dataset' });
  }
});

/**
 * @desc    Upload additional project files (multiple)
 * @route   POST /api/s3/upload-project-files
 * @access  Private (Instructor/Admin)
 * @form    multipart/form-data with field name: files (array)
 */
const uploadProjectFiles = asyncHandler(async (req, res) => {
  const { courseId, projectId } = req.body;

  if (!req.files || req.files.length === 0) {
    return res.status(400).json({ error: 'Validation Error', message: 'No files provided' });
  }

  if (!courseId || !projectId) {
    return res.status(400).json({ error: 'Validation Error', message: 'courseId and projectId are required' });
  }

  // Validate each file (allow common doc/code/data files)
  const allowed = ['ipynb', 'py', 'txt', 'md', 'csv', 'json', 'zip', 'pdf', 'png', 'jpg', 'jpeg'];
  for (const f of req.files) {
    const errs = S3Service.validateFile(f, allowed, 100 * 1024 * 1024);
    if (errs.length) {
      return res.status(400).json({ error: 'Validation Error', message: `${f.originalname}: ${errs.join('; ')}` });
    }
  }

  try {
    const uploads = await Promise.all(
      req.files.map(file => S3Service.uploadFile(file, 'misc', req.user.id, { courseId, projectId }))
    );
    logger.info(`Additional project files uploaded for project ${projectId} by ${req.user.email}`);
    return res.json({ success: true, data: uploads });
  } catch (error) {
    logger.error('Project files upload error:', error);
    return res.status(500).json({ error: 'Upload Error', message: 'Failed to upload project files' });
  }
});

// New routes for project resource uploads
router.post('/upload-project-template', [
  requirePermissions(['edit_projects']),
  upload.single('template')
], validate, uploadProjectTemplate);

router.post('/upload-project-dataset', [
  requirePermissions(['edit_projects']),
  upload.single('dataset')
], validate, uploadProjectDataset);

router.post('/upload-project-files', [
  requirePermissions(['edit_projects']),
  upload.array('files', 20)
], validate, uploadProjectFiles);

export default router;