import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';
import bcrypt from 'bcryptjs';

const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 100]
    }
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  lms_user_id: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true,
    comment: 'User ID from D2L-Brightspace LMS'
  },
  google_id: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true,
    comment: 'Google OAuth user ID'
  },
  password_hash: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'For local authentication (if needed)'
  },
  profile_picture: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'URL to profile picture'
  },
  last_login: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'suspended'),
    defaultValue: 'active'
  },
  preferences: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'User preferences and settings'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional metadata from LMS sync'
  }
}, {
  tableName: 'users',
  indexes: [
    {
      fields: ['email']
    },
    {
      fields: ['lms_user_id']
    },
    {
      fields: ['google_id']
    },
    {
      fields: ['status']
    }
  ],
  hooks: {
    beforeCreate: async (user) => {
      if (user.password_hash) {
        const salt = await bcrypt.genSalt(10);
        user.password_hash = await bcrypt.hash(user.password_hash, salt);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password_hash') && user.password_hash) {
        const salt = await bcrypt.genSalt(10);
        user.password_hash = await bcrypt.hash(user.password_hash, salt);
      }
    }
  }
});

// Instance methods
User.prototype.comparePassword = async function(password) {
  if (!this.password_hash) return false;
  return bcrypt.compare(password, this.password_hash);
};

User.prototype.updateLastLogin = async function() {
  this.last_login = new Date();
  await this.save({ fields: ['last_login'] });
};

User.prototype.toJSON = function() {
  const values = { ...this.get() };
  delete values.password_hash;
  return values;
};

export default User;