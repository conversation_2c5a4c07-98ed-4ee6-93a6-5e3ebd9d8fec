import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const ProjectTemplate = sequelize.define('ProjectTemplate', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: false,
    unique: true,
    references: {
      model: 'projects',
      key: 'id'
    }
  },
  template_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 100]
    }
  },
  template_description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  category: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'general'
  },
  subcategory: {
    type: DataTypes.STRING,
    allowNull: true
  },
  difficulty_level: {
    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'),
    allowNull: false,
    defaultValue: 'beginner'
  },
  estimated_hours: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Estimated completion time in hours'
  },
  total_points: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 100
  },
  learning_objectives: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of learning objectives'
  },
  prerequisites: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of prerequisite topics or skills'
  },
  skills_covered: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of skills covered in this template'
  },
  technologies_used: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of technologies and tools used'
  },
  tags: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of tags for categorization'
  },
  is_featured: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether this template is featured'
  },
  is_public: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this template is publicly available'
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    comment: 'Average rating (0.00 to 5.00)'
  },
  rating_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of ratings received'
  },
  usage_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of times this template has been used'
  },
  download_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of times this template has been downloaded'
  },
  last_used: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Last time this template was used'
  },
  version: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: '1.0.0'
  },
  changelog: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of version changes and updates'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional template metadata'
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  reviewed_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  review_status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected', 'archived'),
    allowNull: false,
    defaultValue: 'pending'
  },
  review_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Review notes from admin/reviewer'
  },
  review_date: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'project_templates',
  indexes: [
    {
      fields: ['project_id']
    },
    {
      fields: ['category']
    },
    {
      fields: ['subcategory']
    },
    {
      fields: ['difficulty_level']
    },
    {
      fields: ['is_featured']
    },
    {
      fields: ['is_public']
    },
    {
      fields: ['rating']
    },
    {
      fields: ['usage_count']
    },
    {
      fields: ['download_count']
    },
    {
      fields: ['review_status']
    },
    {
      fields: ['created_by']
    },
    {
      fields: ['reviewed_by']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['category', 'difficulty_level']
    },
    {
      fields: ['is_public', 'review_status']
    }
  ]
});

export default ProjectTemplate;
