import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const CheckpointProgress = sequelize.define('CheckpointProgress', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  checkpoint_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'checkpoints',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'projects',
      key: 'id'
    }
  },
  status: {
    type: DataTypes.ENUM('not_started', 'in_progress', 'submitted', 'reviewed', 'completed', 'returned'),
    defaultValue: 'not_started'
  },
  started_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When student first started working on this checkpoint'
  },
  submitted_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When student submitted this checkpoint for review'
  },
  completed_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When checkpoint was marked as completed (after review)'
  },
  returned_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When checkpoint was returned for revision'
  },
  instructor_feedback: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Feedback from instructor/TA'
  },
  student_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Notes from student about their work'
  },
  goal_progress: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Progress tracking for each goal within checkpoint'
  },
  files_submitted: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of S3 URLs for submitted files with metadata'
  },
  auto_save_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Auto-saved progress data and drafts'
  },
  time_spent_minutes: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: 'Total time spent on this checkpoint in minutes'
  },
  last_activity: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Last time student worked on this checkpoint'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional progress metadata'
  }
}, {
  tableName: 'checkpoint_progress',
  indexes: [
    {
      fields: ['checkpoint_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['project_id']
    },
    {
      fields: ['status']
    },
    {
      unique: true,
      fields: ['checkpoint_id', 'user_id', 'project_id']
    }
  ]
});

export default CheckpointProgress;
