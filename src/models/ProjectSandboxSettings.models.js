import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const ProjectSandboxSettings = sequelize.define('ProjectSandboxSettings', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'projects',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE'
  },
  mode: {
    type: DataTypes.ENUM('uv', 'docker', 'kubernetes', 'local'),
    defaultValue: 'uv',
    allowNull: false,
    comment: 'Sandbox execution mode'
  },
  cpuLimit: {
    type: DataTypes.STRING,
    defaultValue: '0.5',
    allowNull: false,
    comment: 'CPU limit (e.g., "0.5", "1.0", "2.0")'
  },
  memLimit: {
    type: DataTypes.STRING,
    defaultValue: '1g',
    allowNull: false,
    comment: 'Memory limit (e.g., "512m", "1g", "2g")'
  },
  timeout: {
    type: DataTypes.INTEGER,
    defaultValue: 1800,
    allowNull: false,
    comment: 'Execution timeout in seconds'
  },
  maxConcurrentUsers: {
    type: DataTypes.INTEGER,
    defaultValue: 10,
    allowNull: false,
    comment: 'Maximum number of concurrent users'
  },
  allowedPackages: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'List of allowed Python packages'
  },
  environmentVariables: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Environment variables for the sandbox'
  },
  volumeMounts: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Volume mounts configuration'
  },
  networkPolicy: {
    type: DataTypes.ENUM('restricted', 'open', 'custom'),
    defaultValue: 'restricted',
    allowNull: false,
    comment: 'Network access policy'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    allowNull: false,
    comment: 'Whether these settings are currently active'
  },
  createdBy: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updatedBy: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'project_sandbox_settings',
  indexes: [
    {
      fields: ['projectId']
    },
    {
      fields: ['isActive']
    },
    {
      fields: ['mode']
    }
  ],
  hooks: {
    beforeCreate: async (settings) => {
      // Ensure only one active setting per project
      if (settings.isActive) {
        await ProjectSandboxSettings.update(
          { isActive: false },
          { 
            where: { 
              projectId: settings.projectId,
              isActive: true 
            } 
          }
        );
      }
    },
    beforeUpdate: async (settings) => {
      // Ensure only one active setting per project
      if (settings.changed('isActive') && settings.isActive) {
        await ProjectSandboxSettings.update(
          { isActive: false },
          { 
            where: { 
              projectId: settings.projectId,
              isActive: true,
              id: { [sequelize.Sequelize.Op.ne]: settings.id }
            } 
          }
        );
      }
    }
  }
});

export default ProjectSandboxSettings;
