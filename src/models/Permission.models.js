import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const Permission = sequelize.define('Permission', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  key: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      is: /^[a-z_]+$/i, // Only lowercase letters and underscores
      len: [2, 100]
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 100]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  category: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      isIn: [['user', 'course', 'project', 'submission', 'grade', 'system', 'admin']]
    }
  },
  is_system_permission: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'System permissions cannot be deleted'
  }
}, {
  tableName: 'permissions',
  indexes: [
    {
      fields: ['key']
    },
    {
      fields: ['category']
    }
  ]
});

export default Permission;