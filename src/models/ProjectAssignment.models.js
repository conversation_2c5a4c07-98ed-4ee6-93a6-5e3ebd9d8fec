import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const ProjectAssignment = sequelize.define('ProjectAssignment', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'projects',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  role: {
    type: DataTypes.ENUM('instructor', 'ta', 'reviewer', 'mentor'),
    allowNull: false,
    comment: 'Role of the user in this project'
  },
  assignment_type: {
    type: DataTypes.ENUM('primary', 'secondary', 'guest'),
    allowNull: false,
    defaultValue: 'primary',
    comment: 'Type of assignment (primary instructor, secondary TA, etc.)'
  },
  permissions: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Specific permissions for this user on this project'
  },
  assigned_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  assigned_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who made this assignment'
  },
  start_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When this assignment becomes active'
  },
  end_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When this assignment expires'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this assignment is currently active'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Additional notes about this assignment'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional assignment metadata'
  }
}, {
  tableName: 'project_assignments',
  indexes: [
    {
      fields: ['project_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['role']
    },
    {
      fields: ['assignment_type']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['assigned_at']
    },
    {
      fields: ['project_id', 'user_id'],
      unique: true
    },
    {
      fields: ['project_id', 'role']
    },
    {
      fields: ['user_id', 'is_active']
    }
  ]
});

export default ProjectAssignment;
