import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const LtiContext = sequelize.define('LtiContext', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  platformId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'platform_id'
  },
  deploymentId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'deployment_id'
  },
  courseId: {
    type: DataTypes.UUID,
    allowNull: true,
    field: 'course_id'
  },
  contextId: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'context_id',
    comment: 'LTI context identifier (course ID from LMS)'
  },
  contextType: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'context_type',
    comment: 'Type of context (Course, Group, etc.)'
  },
  contextTitle: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'context_title'
  },
  contextLabel: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'context_label'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  }
}, {
  tableName: 'lti_contexts',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default LtiContext;