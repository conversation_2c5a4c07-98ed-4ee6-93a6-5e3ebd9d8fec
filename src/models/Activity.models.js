import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const Activity = sequelize.define('Activity', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'projects',
      key: 'id'
    }
  },
  course_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'courses',
      key: 'id'
    }
  },
  activity_type: {
    type: DataTypes.ENUM(
      'project_created',
      'project_published',
      'project_archived',
      'checkpoint_created',
      'checkpoint_published',
      'checkpoint_submitted',
      'checkpoint_graded',
      'grade_assigned',
      'feedback_given',
      'student_enrolled',
      'student_submitted',
      'student_graded',
      'course_created',
      'course_updated'
    ),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional activity metadata (e.g., scores, file names, etc.)'
  },
  ip_address: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'IP address of the user performing the action'
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'User agent string for audit purposes'
  }
}, {
  tableName: 'activities',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['project_id']
    },
    {
      fields: ['course_id']
    },
    {
      fields: ['activity_type']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['user_id', 'created_at']
    },
    {
      fields: ['course_id', 'created_at']
    }
  ]
});

export default Activity;
