import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const Checkpoint = sequelize.define('Checkpoint', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'projects',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 200]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  checkpoint_number: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Sequential order within project (1, 2, 3...)'
  },
  due_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When this checkpoint should be completed'
  },
  weight_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0,
    comment: 'Percentage weight of this checkpoint in final grade'
  },
  is_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether this checkpoint is mandatory for project completion'
  },
  status: {
    type: DataTypes.ENUM('draft', 'published', 'archived'),
    defaultValue: 'draft'
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional checkpoint metadata'
  }
}, {
  tableName: 'checkpoints',
  indexes: [
    {
      fields: ['project_id']
    },
    {
      fields: ['created_by']
    },
    {
      fields: ['status']
    },
    {
      fields: ['checkpoint_number']
    },
    {
      unique: true,
      fields: ['project_id', 'checkpoint_number']
    }
  ]
});

export default Checkpoint;
