import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const LtiLaunchSession = sequelize.define('LtiLaunchSession', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  platformId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'platform_id'
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: true,
    field: 'user_id'
  },
  sessionId: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    field: 'session_id'
  },
  state: {
    type: DataTypes.STRING,
    allowNull: false
  },
  nonce: {
    type: DataTypes.STRING,
    allowNull: false
  },
  idToken: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'id_token'
  },
  launchData: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    field: 'launch_data'
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'expires_at'
  },
  isUsed: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_used'
  }
}, {
  tableName: 'lti_launch_sessions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default LtiLaunchSession;