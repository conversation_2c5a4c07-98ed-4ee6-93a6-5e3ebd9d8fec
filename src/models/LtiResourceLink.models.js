import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const LtiResourceLink = sequelize.define('LtiResourceLink', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  platformId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'platform_id'
  },
  contextId: {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'context_id'
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: true,
    field: 'project_id'
  },
  resourceLinkId: {
    type: DataTypes.STRING,
    allowNull: false,
    field: 'resource_link_id',
    comment: 'LTI resource link identifier'
  },
  resourceLinkTitle: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'resource_link_title'
  },
  resourceLinkDescription: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'resource_link_description'
  },
  customParameters: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    field: 'custom_parameters'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  }
}, {
  tableName: 'lti_resource_links',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default LtiResourceLink;