import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const ProjectStatistics = sequelize.define('ProjectStatistics', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: false,
    unique: true,
    references: {
      model: 'projects',
      key: 'id'
    }
  },
  total_students: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Total number of students enrolled in the course'
  },
  active_students: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of students actively working on the project'
  },
  submissions_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Total number of submissions received'
  },
  graded_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of submissions that have been graded'
  },
  pending_grades: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of submissions waiting to be graded'
  },
  average_grade: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Average grade across all graded submissions'
  },
  progress_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0,
    comment: 'Overall progress percentage across all students'
  },
  checkpoint_completion_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0,
    comment: 'Average checkpoint completion rate across students'
  },
  average_time_to_completion: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Average time to complete project in hours'
  },
  last_activity: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Last activity timestamp for this project'
  },
  last_updated: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional statistical metadata'
  }
}, {
  tableName: 'project_statistics',
  indexes: [
    {
      fields: ['project_id']
    },
    {
      fields: ['last_updated']
    },
    {
      fields: ['progress_percentage']
    },
    {
      fields: ['average_grade']
    }
  ]
});

export default ProjectStatistics;
