import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const CheckpointGoal = sequelize.define('CheckpointGoal', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  checkpoint_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'checkpoints',
      key: 'id'
    }
  },
  goal_type: {
    type: DataTypes.ENUM('file_upload', 'code_completion', 'analysis', 'documentation', 'presentation', 'quiz', 'discussion'),
    allowNull: false
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 200]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  required_files: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of required file types/extensions and descriptions'
  },
  completion_criteria: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of criteria that must be met for completion'
  },
  points: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  order_index: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Order of this goal within the checkpoint'
  },
  is_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether this goal is mandatory for checkpoint completion'
  },
  estimated_time_minutes: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Estimated time to complete this goal in minutes'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional goal metadata'
  }
}, {
  tableName: 'checkpoint_goals',
  indexes: [
    {
      fields: ['checkpoint_id']
    },
    {
      fields: ['goal_type']
    },
    {
      fields: ['order_index']
    },
    {
      unique: true,
      fields: ['checkpoint_id', 'order_index']
    }
  ]
});

export default CheckpointGoal;
