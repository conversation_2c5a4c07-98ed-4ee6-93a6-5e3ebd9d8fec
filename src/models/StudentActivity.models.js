import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const StudentActivity = sequelize.define('StudentActivity', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  student_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Student user ID'
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'projects',
      key: 'id'
    },
    comment: 'Project ID (optional for course-level activities)'
  },
  course_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'courses',
      key: 'id'
    },
    comment: 'Course ID (optional for project-level activities)'
  },
  activity_type: {
    type: DataTypes.ENUM('project_started', 'checkpoint_submitted', 'project_completed', 'grade_received', 'feedback_received', 'deadline_approaching', 'course_enrolled'),
    allowNull: false,
    comment: 'Type of activity performed by the student'
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 255]
    },
    comment: 'Activity title for display'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Detailed description of the activity'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional activity metadata and context information'
  },
  timestamp: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'When the activity occurred'
  },
  is_read: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the student has read this activity notification'
  }
}, {
  tableName: 'student_activities',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['student_id']
    },
    {
      fields: ['project_id']
    },
    {
      fields: ['course_id']
    },
    {
      fields: ['activity_type']
    },
    {
      fields: ['timestamp']
    },
    {
      fields: ['is_read']
    },
    {
      fields: ['student_id', 'timestamp']
    },
    {
      fields: ['student_id', 'activity_type']
    }
  ]
});

export default StudentActivity;
