import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const Message = sequelize.define('Message', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  sender_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  recipient_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  subject: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 200]
    }
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  message_type: {
    type: DataTypes.ENUM(
      'personal',
      'course_related',
      'project_related',
      'system',
      'notification'
    ),
    allowNull: false,
    defaultValue: 'personal'
  },
  priority: {
    type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'normal'
  },
  status: {
    type: DataTypes.ENUM('sent', 'delivered', 'read', 'archived'),
    allowNull: false,
    defaultValue: 'sent'
  },
  is_read: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  read_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  parent_message_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'messages',
      key: 'id'
    },
    comment: 'For reply chains'
  },
  thread_id: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: 'Group messages in the same conversation thread'
  },
  course_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'courses',
      key: 'id'
    },
    comment: 'For course-related messages'
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'projects',
      key: 'id'
    },
    comment: 'For project-related messages'
  },
  attachments: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of attachment metadata (S3 URLs, file names, etc.)'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional message metadata'
  },
  scheduled_for: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When to send the message (for scheduled messages)'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the message expires'
  }
}, {
  tableName: 'messages',
  indexes: [
    {
      fields: ['sender_id']
    },
    {
      fields: ['recipient_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['is_read']
    },
    {
      fields: ['message_type']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['parent_message_id']
    },
    {
      fields: ['thread_id']
    },
    {
      fields: ['course_id']
    },
    {
      fields: ['project_id']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['recipient_id', 'is_read']
    },
    {
      fields: ['recipient_id', 'status']
    },
    {
      fields: ['sender_id', 'recipient_id']
    }
  ]
});

export default Message;
