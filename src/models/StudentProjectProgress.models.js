import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const StudentProjectProgress = sequelize.define('StudentProjectProgress', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  student_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Student user ID'
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'projects',
      key: 'id'
    },
    comment: 'Project ID'
  },
  course_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'courses',
      key: 'id'
    },
    comment: 'Course ID'
  },
  enrollment_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'When student was enrolled in this project'
  },
  start_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When student actually started working on the project'
  },
  completion_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When student completed the project'
  },
  progress_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    validate: {
      min: 0.00,
      max: 100.00
    },
    comment: 'Progress percentage from 0.00 to 100.00'
  },
  current_phase: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Current phase or milestone in the project'
  },
  time_spent_hours: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: false,
    defaultValue: 0.00,
    validate: {
      min: 0.00
    },
    comment: 'Total time spent on project in hours'
  },
  last_activity: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Last time student worked on this project'
  },
  status: {
    type: DataTypes.ENUM('not_started', 'in_progress', 'completed', 'overdue'),
    allowNull: false,
    defaultValue: 'not_started',
    comment: 'Current status of student progress'
  },
  grade: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    validate: {
      min: 0.00,
      max: 100.00
    },
    comment: 'Final grade received for the project'
  },
  feedback: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Feedback received from instructor/TA'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional progress metadata and tracking information'
  }
}, {
  tableName: 'student_project_progress',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['student_id']
    },
    {
      fields: ['project_id']
    },
    {
      fields: ['course_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['last_activity']
    },
    {
      fields: ['student_id', 'project_id'],
      unique: true
    }
  ]
});

export default StudentProjectProgress;
