import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const Announcement = sequelize.define('Announcement', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  course_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'courses',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 200]
    }
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  announcement_type: {
    type: DataTypes.ENUM(
      'general',
      'project_update',
      'deadline_reminder',
      'course_update',
      'important',
      'urgent'
    ),
    allowNull: false,
    defaultValue: 'general'
  },
  priority: {
    type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'normal'
  },
  status: {
    type: DataTypes.ENUM('draft', 'published', 'archived'),
    allowNull: false,
    defaultValue: 'draft'
  },
  is_pinned: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  scheduled_for: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When to automatically publish the announcement'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the announcement expires'
  },
  target_audience: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: ['all'],
    comment: 'Array of target roles: all, students, instructors, tas'
  },
  attachments: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of attachment metadata (S3 URLs, file names, etc.)'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional announcement metadata'
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  published_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  published_at: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'announcements',
  indexes: [
    {
      fields: ['course_id']
    },
    {
      fields: ['created_by']
    },
    {
      fields: ['status']
    },
    {
      fields: ['announcement_type']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['is_pinned']
    },
    {
      fields: ['scheduled_for']
    },
    {
      fields: ['expires_at']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['course_id', 'status']
    },
    {
      fields: ['course_id', 'is_pinned']
    }
  ]
});

export default Announcement;
