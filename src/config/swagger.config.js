import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'BITS Pilani DataScience Platform API',
      version: '1.0.0',
      description: 'A comprehensive educational environment for data science learning and teaching',
      contact: {
        name: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        url: 'https://www.bits-pilani.ac.in'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: process.env.API_BASE_URL || 'http://localhost:5000/api',
        description: 'Development server'
      },
      {
        url: 'https://api.bits-dataScience-platform.com/api',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Unique user identifier'
            },
            name: {
              type: 'string',
              description: 'User full name'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address'
            },
            profilePicture: {
              type: 'string',
              format: 'uri',
              description: 'URL to user profile picture'
            },
            lastLogin: {
              type: 'string',
              format: 'date-time',
              description: 'Last login timestamp'
            },
            status: {
              type: 'string',
              enum: ['active', 'inactive', 'suspended'],
              description: 'User account status'
            },
            roles: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/Role'
              }
            }
          }
        },
        Role: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid'
            },
            name: {
              type: 'string',
              description: 'Role name'
            },
            description: {
              type: 'string',
              description: 'Role description'
            },
            permissions: {
              type: 'array',
              items: {
                type: 'string'
              },
              description: 'List of permission keys'
            }
          }
        },
        Course: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid'
            },
            name: {
              type: 'string',
              description: 'Course name'
            },
            code: {
              type: 'string',
              description: 'Course code'
            },
            description: {
              type: 'string',
              description: 'Course description'
            },
            term: {
              type: 'string',
              description: 'Academic term'
            },
            status: {
              type: 'string',
              enum: ['active', 'inactive', 'archived', 'draft']
            },
            instructor: {
              $ref: '#/components/schemas/User'
            }
          }
        },
        Project: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid'
            },
            title: {
              type: 'string',
              description: 'Project title'
            },
            description: {
              type: 'string',
              description: 'Project description'
            },
            status: {
              type: 'string',
              enum: ['draft', 'published', 'archived']
            },
            difficultyLevel: {
              type: 'string',
              enum: ['beginner', 'intermediate', 'advanced']
            },
            dueDate: {
              type: 'string',
              format: 'date-time'
            },
            courseId: {
              type: 'string',
              format: 'uuid'
            }
          }
        },
        Submission: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid'
            },
            userId: {
              type: 'string',
              format: 'uuid'
            },
            projectId: {
              type: 'string',
              format: 'uuid'
            },
            status: {
              type: 'string',
              enum: ['in_progress', 'submitted', 'grading', 'graded', 'returned']
            },
            submittedAt: {
              type: 'string',
              format: 'date-time'
            },
            notebookS3Url: {
              type: 'string',
              format: 'uri'
            }
          }
        },
        Grade: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid'
            },
            submissionId: {
              type: 'string',
              format: 'uuid'
            },
            evaluatorId: {
              type: 'string',
              format: 'uuid'
            },
            totalScore: {
              type: 'number',
              format: 'float'
            },
            maxScore: {
              type: 'number',
              format: 'float'
            },
            percentage: {
              type: 'number',
              format: 'float'
            },
            letterGrade: {
              type: 'string'
            },
            feedback: {
              type: 'string'
            }
          }
        },
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'string',
              description: 'Error message'
            },
            message: {
              type: 'string',
              description: 'Detailed error description'
            },
            status: {
              type: 'integer',
              description: 'HTTP status code'
            },
            timestamp: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        PaginationResponse: {
          type: 'object',
          properties: {
            currentPage: {
              type: 'integer',
              minimum: 1
            },
            totalPages: {
              type: 'integer',
              minimum: 0
            },
            totalItems: {
              type: 'integer',
              minimum: 0
            },
            itemsPerPage: {
              type: 'integer',
              minimum: 1
            }
          }
        }
      },
      responses: {
        UnauthorizedError: {
          description: 'Authentication information is missing or invalid',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        ForbiddenError: {
          description: 'Access forbidden - insufficient permissions',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        NotFoundError: {
          description: 'Resource not found',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        ValidationError: {
          description: 'Validation error',
          content: {
            'application/json': {
              schema: {
                allOf: [
                  { $ref: '#/components/schemas/Error' },
                  {
                    type: 'object',
                    properties: {
                      details: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            field: { type: 'string' },
                            message: { type: 'string' },
                            value: { type: 'string' }
                          }
                        }
                      }
                    }
                  }
                ]
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './src/routes/*.js',
    './src/controllers/*.js',
    './src/models/*.js'
  ]
};

const specs = swaggerJsdoc(options);

export { specs, swaggerUi };

// Swagger setup function
export default (app) => {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: `
      .swagger-ui .topbar { display: none; }
      .swagger-ui .info .title { color: #2B2B88; }
    `,
    customSiteTitle: 'BITS DataScience Platform API Documentation'
  }));

  // JSON endpoint
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });

  console.log('📚 Swagger documentation available at /api-docs');
};