import { Project, Course, User, Submission, Grade, Rubric, CourseEnrollment } from '../models/associations.js';
import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import { Op, fn, col } from 'sequelize';
import logger from '../config/logger.config.js';

/**
 * @desc    Get all projects with pagination and filtering
 * @route   GET /api/projects
 * @access  Private
 */
export const getProjects = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    search,
    courseId,
    status,
    difficultyLevel,
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const whereClause = {};

  // Add search functionality
  if (search) {
    whereClause[Op.or] = [
      { title: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Filter by course
  if (courseId) {
    whereClause.course_id = courseId;
  }

  // Filter by status
  if (status) {
    whereClause.status = status;
  }

  // Filter by difficulty level
  if (difficultyLevel) {
    whereClause.difficulty_level = difficultyLevel;
  }

  // For non-admin users, only show projects from courses they have access to
  let includeClause = [
    {
      model: Course,
      as: 'course',
      attributes: ['id', 'name', 'code', 'term'],
      include: [{
        model: User,
        as: 'instructor',
        attributes: ['id', 'name', 'email']
      }]
    },
    {
      model: User,
      as: 'creator',
      attributes: ['id', 'name', 'email']
    }
  ];

  // Add course access filter for non-admin users
  if (!req.userRoles.includes('admin')) {
    includeClause[0].include.push({
      model: CourseEnrollment,
      as: 'enrollments',
      where: { user_id: req.user.id },
      required: true
    });
  }

  const { count, rows: projects } = await Project.findAndCountAll({
    where: whereClause,
    include: includeClause,
    limit: parseInt(limit),
    offset,
    order: [[sortBy, sortOrder.toUpperCase()]],
    distinct: true
  });

  // Transform project data for response
  const transformedProjects = await Promise.all(projects.map(async project => {
    // Get submission count for this project
    const submissionCount = await Submission.count({
      where: { project_id: project.id }
    });

    // Get user's submission if exists
    let userSubmission = null;
    if (req.user && !req.userRoles.includes('admin')) {
      userSubmission = await Submission.findOne({
        where: { project_id: project.id, user_id: req.user.id },
        attributes: ['id', 'status', 'submitted_at', 'grade']
      });
    }

    return {
      id: project.id,
      title: project.title,
      description: project.description,
      status: project.status,
      difficultyLevel: project.difficulty_level,
      estimatedHours: project.estimated_hours,
      dueDate: project.due_date,
      instructions: project.instructions,
      submissionCount,
      course: {
        id: project.course.id,
        name: project.course.name,
        code: project.course.code,
        term: project.course.term,
        instructor: project.course.instructor
      },
      creator: {
        id: project.creator.id,
        name: project.creator.name,
        email: project.creator.email
      },
      userSubmission,
      createdAt: project.created_at,
      updatedAt: project.updated_at
    };
  }));

  res.json({
    success: true,
    data: {
      projects: transformedProjects,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / parseInt(limit)),
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

/**
 * @desc    Get project by ID
 * @route   GET /api/projects/:id
 * @access  Private
 */
export const getProjectById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const project = await Project.findByPk(id, {
    include: [
      {
        model: Course,
        as: 'course',
        include: [{
          model: User,
          as: 'instructor',
          attributes: ['id', 'name', 'email']
        }]
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Rubric,
        as: 'rubrics'
      }
    ]
  });

  if (!project) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Project not found'
    });
  }

  // Check if user has access to this project
  const hasAccess = req.userRoles.includes('admin') || 
                   project.course.instructor_id === req.user.id ||
                   project.creator_id === req.user.id;

  if (!hasAccess) {
    // Check if user is enrolled in the course
    const enrollment = await CourseEnrollment.findOne({
      where: { course_id: project.course_id, user_id: req.user.id }
    });
    
    if (!enrollment) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You do not have permission to view this project'
      });
    }
  }

  // Get user's submission if exists
  let userSubmission = null;
  if (!req.userRoles.includes('admin')) {
    userSubmission = await Submission.findOne({
      where: { project_id: project.id, user_id: req.user.id },
      include: [{
        model: Grade,
        as: 'grade',
        include: [{
          model: User,
          as: 'evaluator',
          attributes: ['id', 'name', 'email']
        }]
      }]
    });
  }

  const projectResponse = {
    id: project.id,
    title: project.title,
    description: project.description,
    status: project.status,
    difficultyLevel: project.difficulty_level,
    estimatedHours: project.estimated_hours,
    dueDate: project.due_date,
    instructions: project.instructions,
    requirements: project.requirements,
    resources: project.resources,
    settings: project.settings,
    course: {
      id: project.course.id,
      name: project.course.name,
      code: project.course.code,
      term: project.course.term,
      instructor: project.course.instructor
    },
    creator: {
      id: project.creator.id,
      name: project.creator.name,
      email: project.creator.email
    },
    rubrics: project.rubrics?.map(rubric => ({
      id: rubric.id,
      name: rubric.name,
      description: rubric.description,
      criteria: rubric.criteria,
      maxScore: rubric.max_score,
      weight: rubric.weight
    })) || [],
    userSubmission: userSubmission ? {
      id: userSubmission.id,
      status: userSubmission.status,
      submittedAt: userSubmission.submitted_at,
      notebookS3Url: userSubmission.notebook_s3_url,
      grade: userSubmission.grade ? {
        id: userSubmission.grade.id,
        totalScore: userSubmission.grade.total_score,
        maxScore: userSubmission.grade.max_score,
        percentage: userSubmission.grade.percentage,
        letterGrade: userSubmission.grade.letter_grade,
        feedback: userSubmission.grade.feedback,
        evaluator: userSubmission.grade.evaluator
      } : null
    } : null,
    createdAt: project.created_at,
    updatedAt: project.updated_at
  };

  res.json({
    success: true,
    project: projectResponse
  });
});

/**
 * @desc    Create new project
 * @route   POST /api/projects
 * @access  Private (Instructor, Admin)
 */
export const createProject = asyncHandler(async (req, res) => {
  const {
    title,
    description,
    courseId,
    difficultyLevel = 'intermediate',
    estimatedHours,
    dueDate,
    instructions,
    requirements,
    resources,
    settings = {},
    rubrics = []
  } = req.body;

  // Validate required fields
  if (!title || !description || !courseId) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Title, description, and course ID are required'
    });
  }

  // Check if course exists and user has permission
  const course = await Course.findByPk(courseId);
  if (!course) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Course not found'
    });
  }

  const hasPermission = req.userRoles.includes('admin') || 
                       course.instructor_id === req.user.id;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to create projects in this course'
    });
  }

  // Create project
  const project = await Project.create({
    title,
    description,
    course_id: courseId,
    creator_id: req.user.id,
    difficulty_level: difficultyLevel,
    estimated_hours: estimatedHours,
    due_date: dueDate,
    instructions,
    requirements,
    resources,
    settings,
    status: 'draft'
  });

  // Handle optional uploads if request is multipart with files
  // Expect fields: template (single), dataset (single), files (array)
  try {
    const s3Service = (await import('../services/s3.service.js')).default;

    if (req.file) {
      // If a single file provided (treat as template by default)
      const errors = s3Service.validateFile(req.file, ['ipynb', 'py', 'json', 'zip'], 50 * 1024 * 1024);
      if (errors.length) {
        logger.warn(`Template file validation failed: ${errors.join('; ')}`);
      } else {
        const uploaded = await s3Service.uploadFile(req.file, 'project-template', req.user.id, { courseId, projectId: project.id });
        await project.update({ notebook_template_s3_url: uploaded.url });
      }
    }

    if (req.files) {
      const { template, dataset, files } = req.files;

      if (template && template[0]) {
        const t = template[0];
        const errs = s3Service.validateFile(t, ['ipynb', 'py', 'json', 'zip'], 50 * 1024 * 1024);
        if (!errs.length) {
          const up = await s3Service.uploadFile(t, 'project-template', req.user.id, { courseId, projectId: project.id });
          await project.update({ notebook_template_s3_url: up.url });
        } else {
          logger.warn(`Template file validation failed: ${errs.join('; ')}`);
        }
      }

      if (dataset && dataset[0]) {
        const d = dataset[0];
        const errs = s3Service.validateFile(d, ['csv', 'json', 'zip', 'parquet'], 200 * 1024 * 1024);
        if (!errs.length) {
          const up = await s3Service.uploadFile(d, 'project-dataset', req.user.id, { courseId, projectId: project.id });
          await project.update({ dataset_s3_url: up.url });
        } else {
          logger.warn(`Dataset file validation failed: ${errs.join('; ')}`);
        }
      }

      if (files && files.length > 0) {
        const allowed = ['ipynb', 'py', 'txt', 'md', 'csv', 'json', 'zip', 'pdf', 'png', 'jpg', 'jpeg'];
        const uploaded = [];
        for (const f of files) {
          const errs = s3Service.validateFile(f, allowed, 100 * 1024 * 1024);
          if (!errs.length) {
            const up = await s3Service.uploadFile(f, 'misc', req.user.id, { courseId, projectId: project.id });
            uploaded.push(up.url);
          } else {
            logger.warn(`Additional file skipped (${f.originalname}): ${errs.join('; ')}`);
          }
        }
        if (uploaded.length) {
          await project.update({ additional_files_s3_urls: uploaded });
        }
      }
    }
  } catch (e) {
    logger.error('Error handling project resource uploads:', e);
  }

  // Create rubrics if provided
  if (rubrics.length > 0) {
    const rubricPromises = rubrics.map(rubric => 
      Rubric.create({
        ...rubric,
        project_id: project.id
      })
    );
    await Promise.all(rubricPromises);
  }

  logger.info(`Project created: ${title} by ${req.user.email}`);

  res.status(201).json({
    success: true,
    message: 'Project created successfully',
    project: {
      id: project.id,
      title: project.title,
      description: project.description,
      status: project.status,
      courseId: project.course_id,
      createdAt: project.created_at
    }
  });
});

/**
 * @desc    Update project
 * @route   PUT /api/projects/:id
 * @access  Private (Instructor, Admin)
 */
export const updateProject = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    title,
    description,
    difficultyLevel,
    estimatedHours,
    dueDate,
    instructions,
    requirements,
    resources,
    settings,
    status
  } = req.body;

  const project = await Project.findByPk(id, {
    include: [{
      model: Course,
      as: 'course'
    }]
  });

  if (!project) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Project not found'
    });
  }

  // Check permissions
  const hasPermission = req.userRoles.includes('admin') || 
                       project.course.instructor_id === req.user.id ||
                       project.creator_id === req.user.id;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to update this project'
    });
  }

  // Update project
  const updateData = {};
  if (title !== undefined) updateData.title = title;
  if (description !== undefined) updateData.description = description;
  if (difficultyLevel !== undefined) updateData.difficulty_level = difficultyLevel;
  if (estimatedHours !== undefined) updateData.estimated_hours = estimatedHours;
  if (dueDate !== undefined) updateData.due_date = dueDate;
  if (instructions !== undefined) updateData.instructions = instructions;
  if (requirements !== undefined) updateData.requirements = requirements;
  if (resources !== undefined) updateData.resources = resources;
  if (settings !== undefined) updateData.settings = settings;
  if (status !== undefined) updateData.status = status;

  await project.update(updateData);

  logger.info(`Project updated: ${project.title} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Project updated successfully',
    project: {
      id: project.id,
      title: project.title,
      description: project.description,
      status: project.status,
      updatedAt: project.updated_at
    }
  });
});

/**
 * @desc    Duplicate project
 * @route   POST /api/projects/:id/duplicate
 * @access  Private (Instructor, Admin)
 */
export const duplicateProject = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { courseId, title } = req.body;

  const originalProject = await Project.findByPk(id, {
    include: [{
      model: Course,
      as: 'course'
    }, {
      model: Rubric,
      as: 'rubrics'
    }]
  });

  if (!originalProject) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Project not found'
    });
  }

  // Check permissions for original project
  const hasPermission = req.userRoles.includes('admin') || 
                       originalProject.course.instructor_id === req.user.id ||
                       originalProject.creator_id === req.user.id;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to duplicate this project'
    });
  }

  // Check permission for target course
  const targetCourse = await Course.findByPk(courseId);
  if (!targetCourse) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Target course not found'
    });
  }

  const hasTargetPermission = req.userRoles.includes('admin') || 
                             targetCourse.instructor_id === req.user.id;

  if (!hasTargetPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to create projects in the target course'
    });
  }

  // Create duplicate project
  const duplicateProject = await Project.create({
    title: title || `${originalProject.title} (Copy)`,
    description: originalProject.description,
    course_id: courseId,
    creator_id: req.user.id,
    difficulty_level: originalProject.difficulty_level,
    estimated_hours: originalProject.estimated_hours,
    due_date: originalProject.due_date,
    instructions: originalProject.instructions,
    requirements: originalProject.requirements,
    resources: originalProject.resources,
    settings: originalProject.settings,
    status: 'draft'
  });

  // Duplicate rubrics
  if (originalProject.rubrics && originalProject.rubrics.length > 0) {
    const rubricPromises = originalProject.rubrics.map(rubric => 
      Rubric.create({
        name: rubric.name,
        description: rubric.description,
        criteria: rubric.criteria,
        max_score: rubric.max_score,
        weight: rubric.weight,
        project_id: duplicateProject.id
      })
    );
    await Promise.all(rubricPromises);
  }

  logger.info(`Project duplicated: ${originalProject.title} to ${duplicateProject.title} by ${req.user.email}`);

  res.status(201).json({
    success: true,
    message: 'Project duplicated successfully',
    project: {
      id: duplicateProject.id,
      title: duplicateProject.title,
      description: duplicateProject.description,
      status: duplicateProject.status,
      courseId: duplicateProject.course_id,
      createdAt: duplicateProject.created_at
    }
  });
});

/**
 * @desc    Delete project
 * @route   DELETE /api/projects/:id
 * @access  Private (Instructor, Admin)
 */
export const deleteProject = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const project = await Project.findByPk(id, {
    include: [{
      model: Course,
      as: 'course'
    }]
  });

  if (!project) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Project not found'
    });
  }

  // Check permissions
  const hasPermission = req.userRoles.includes('admin') || 
                       project.course.instructor_id === req.user.id ||
                       project.creator_id === req.user.id;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to delete this project'
    });
  }

  // Check if project has submissions
  const submissionCount = await Submission.count({
    where: { project_id: id }
  });

  if (submissionCount > 0) {
    return res.status(409).json({
      error: 'Conflict',
      message: 'Cannot delete project with existing submissions'
    });
  }

  // Delete project (this will cascade delete rubrics)
  await project.destroy();

  logger.info(`Project deleted: ${project.title} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Project deleted successfully'
  });
});

/**
 * @desc    Publish project
 * @route   POST /api/projects/:id/publish
 * @access  Private (Instructor, Admin)
 */
export const publishProject = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const project = await Project.findByPk(id, {
    include: [{
      model: Course,
      as: 'course'
    }]
  });

  if (!project) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Project not found'
    });
  }

  // Check permissions
  const hasPermission = req.userRoles.includes('admin') || 
                       project.course.instructor_id === req.user.id ||
                       project.creator_id === req.user.id;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to publish this project'
    });
  }

  // Update status to published
  await project.update({ status: 'published' });

  logger.info(`Project published: ${project.title} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Project published successfully',
    project: {
      id: project.id,
      title: project.title,
      status: project.status,
      updatedAt: project.updated_at
    }
  });
});

/**
 * @desc    Get project statistics
 * @route   GET /api/projects/:id/statistics
 * @access  Private (Instructor, Admin)
 */
export const getProjectStatistics = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const project = await Project.findByPk(id, {
    include: [{
      model: Course,
      as: 'course'
    }]
  });

  if (!project) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Project not found'
    });
  }

  // Check permissions
  const hasPermission = req.userRoles.includes('admin') || 
                       project.course.instructor_id === req.user.id ||
                       project.creator_id === req.user.id;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view project statistics'
    });
  }

  // Get submission statistics
  const totalSubmissions = await Submission.count({
    where: { project_id: id }
  });

  const submissionsByStatus = await Submission.findAll({
    where: { project_id: id },
    attributes: [
      'status',
      [fn('COUNT', col('id')), 'count']
    ],
    group: ['status']
  });

  const gradedSubmissions = await Submission.count({
    where: { project_id: id, status: 'graded' }
  });

  // Get grade statistics
  const gradeStats = await Grade.findAll({
    include: [{
      model: Submission,
      as: 'submission',
      where: { project_id: id },
      attributes: []
    }],
    attributes: [
      [fn('AVG', col('percentage')), 'averagePercentage'],
      [fn('MIN', col('percentage')), 'minPercentage'],
      [fn('MAX', col('percentage')), 'maxPercentage']
    ]
  });

  res.json({
    success: true,
    statistics: {
      submissions: {
        total: totalSubmissions,
        graded: gradedSubmissions,
        byStatus: submissionsByStatus.map(item => ({
          status: item.status,
          count: parseInt(item.getDataValue('count'))
        }))
      },
      grades: gradeStats[0] ? {
        average: parseFloat(gradeStats[0].getDataValue('averagePercentage')) || 0,
        min: parseFloat(gradeStats[0].getDataValue('minPercentage')) || 0,
        max: parseFloat(gradeStats[0].getDataValue('maxPercentage')) || 0
      } : null
    }
  });
});