import { asyncHand<PERSON> } from '../middlewares/errorHandler.middlewares.js';
import announcementService from '../services/announcement.service.js';
import logger from '../config/logger.config.js';

/**
 * @desc    Create new announcement
 * @route   POST /api/announcements
 * @access  Private (Instructor/Admin)
 */
export const createAnnouncement = asyncHandler(async (req, res) => {
  const {
    title,
    content,
    courseId,
    announcementType = 'general',
    priority = 'normal',
    isPinned = false,
    scheduledFor,
    expiresAt,
    targetAudience = ['all'],
    attachments = []
  } = req.body;

  // Validate required fields
  if (!title || !content || !courseId) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Title, content, and course ID are required'
    });
  }

  try {
    const announcementData = {
      title,
      content,
      course_id: courseId,
      announcement_type: announcementType,
      priority,
      is_pinned: isPinned,
      scheduled_for: scheduledFor,
      expires_at: expiresAt,
      target_audience: targetAudience,
      attachments,
      created_by: req.user.id,
      status: scheduledFor ? 'draft' : 'draft'
    };

    const announcement = await announcementService.createAnnouncement(announcementData);
    
    res.status(201).json({
      success: true,
      message: 'Announcement created successfully',
      announcement: {
        id: announcement.id,
        title: announcement.title,
        status: announcement.status,
        courseId: announcement.course_id,
        createdAt: announcement.created_at
      }
    });
  } catch (error) {
    logger.error('Error creating announcement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create announcement'
    });
  }
});

/**
 * @desc    Get all announcements for a course
 * @route   GET /api/announcements/course/:courseId
 * @access  Private
 */
export const getCourseAnnouncements = asyncHandler(async (req, res) => {
  const { courseId } = req.params;
  const {
    status = 'published',
    announcementType,
    priority,
    isPinned,
    page = 1,
    limit = 20,
    includeExpired = false
  } = req.query;

  try {
    const result = await announcementService.getCourseAnnouncements(courseId, {
      status,
      announcementType,
      priority,
      isPinned,
      page: parseInt(page),
      limit: parseInt(limit),
      includeExpired: includeExpired === 'true'
    });
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error getting course announcements:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve announcements'
    });
  }
});

/**
 * @desc    Get announcement by ID
 * @route   GET /api/announcements/:id
 * @access  Private
 */
export const getAnnouncementById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const announcement = await announcementService.getAnnouncementById(id, req.user.id);
    
    res.json({
      success: true,
      announcement
    });
  } catch (error) {
    if (error.message === 'Announcement not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Announcement not found'
      });
    }
    
    logger.error('Error getting announcement by ID:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve announcement'
    });
  }
});

/**
 * @desc    Update announcement
 * @route   PUT /api/announcements/:id
 * @access  Private (Creator/Admin)
 */
export const updateAnnouncement = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  try {
    const announcement = await announcementService.updateAnnouncement(id, updateData, req.user.id);
    
    res.json({
      success: true,
      message: 'Announcement updated successfully',
      announcement
    });
  } catch (error) {
    if (error.message === 'Announcement not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Announcement not found'
      });
    }
    
    if (error.message.includes('Permission denied')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }
    
    logger.error('Error updating announcement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update announcement'
    });
  }
});

/**
 * @desc    Publish announcement
 * @route   POST /api/announcements/:id/publish
 * @access  Private (Creator/Admin)
 */
export const publishAnnouncement = asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const announcement = await announcementService.publishAnnouncement(id, req.user.id);
    
    res.json({
      success: true,
      message: 'Announcement published successfully',
      announcement
    });
  } catch (error) {
    if (error.message === 'Announcement not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Announcement not found'
      });
    }
    
    logger.error('Error publishing announcement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to publish announcement'
    });
  }
});

/**
 * @desc    Archive announcement
 * @route   POST /api/announcements/:id/archive
 * @access  Private (Creator/Admin)
 */
export const archiveAnnouncement = asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const announcement = await announcementService.archiveAnnouncement(id, req.user.id);
    
    res.json({
      success: true,
      message: 'Announcement archived successfully',
      announcement
    });
  } catch (error) {
    if (error.message === 'Announcement not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Announcement not found'
      });
    }
    
    if (error.message.includes('Permission denied')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }
    
    logger.error('Error archiving announcement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to archive announcement'
    });
  }
});

/**
 * @desc    Toggle pin status
 * @route   POST /api/announcements/:id/toggle-pin
 * @access  Private (Creator/Admin)
 */
export const togglePinStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const announcement = await announcementService.togglePinStatus(id, req.user.id);
    
    res.json({
      success: true,
      message: `Announcement ${announcement.is_pinned ? 'pinned' : 'unpinned'} successfully`,
      announcement
    });
  } catch (error) {
    if (error.message === 'Announcement not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Announcement not found'
      });
    }
    
    if (error.message.includes('Permission denied')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }
    
    logger.error('Error toggling pin status:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to toggle pin status'
    });
  }
});

/**
 * @desc    Delete announcement
 * @route   DELETE /api/announcements/:id
 * @access  Private (Creator/Admin)
 */
export const deleteAnnouncement = asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const result = await announcementService.deleteAnnouncement(id, req.user.id);
    
    res.json({
      success: true,
      message: result.message
    });
  } catch (error) {
    if (error.message === 'Announcement not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Announcement not found'
      });
    }
    
    if (error.message.includes('Permission denied')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }
    
    logger.error('Error deleting announcement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete announcement'
    });
  }
});

/**
 * @desc    Get instructor announcements
 * @route   GET /api/announcements/instructor
 * @access  Private (Instructor/Admin)
 */
export const getInstructorAnnouncements = asyncHandler(async (req, res) => {
  const { courseId } = req.query;

  try {
    const result = await announcementService.getInstructorAnnouncements(req.user.id, courseId);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error getting instructor announcements:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve instructor announcements'
    });
  }
});

/**
 * @desc    Get student announcements
 * @route   GET /api/announcements/student
 * @access  Private (Student)
 */
export const getStudentAnnouncements = asyncHandler(async (req, res) => {
  const { courseId } = req.query;

  try {
    const announcements = await announcementService.getStudentAnnouncements(req.user.id, courseId);
    
    res.json({
      success: true,
      announcements
    });
  } catch (error) {
    logger.error('Error getting student announcements:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve student announcements'
    });
  }
});

/**
 * @desc    Schedule announcement
 * @route   POST /api/announcements/:id/schedule
 * @access  Private (Creator/Admin)
 */
export const scheduleAnnouncement = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { scheduledFor } = req.body;

  if (!scheduledFor) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Scheduled date is required'
    });
  }

  try {
    const announcement = await announcementService.scheduleAnnouncement(id, scheduledFor, req.user.id);
    
    res.json({
      success: true,
      message: 'Announcement scheduled successfully',
      announcement
    });
  } catch (error) {
    if (error.message === 'Announcement not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Announcement not found'
      });
    }
    
    if (error.message.includes('Permission denied')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }
    
    logger.error('Error scheduling announcement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to schedule announcement'
    });
  }
});
