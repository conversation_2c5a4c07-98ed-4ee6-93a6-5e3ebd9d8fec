import logger from '../config/logger.config.js';
import jupyterService from '../services/jupyterhub.service.js';

const startJupyterServer = async (req, res) => {
  console.log('In startJupyterServer controller');
  try {
    const user = req.user;
    logger.info(`Starting Jupyter server for user: ${user}`);
    const response = await jupyterService.ensureServerIsRunning(user);
    res.status(200).json(response);
  } catch (error) {
    logger.error(`Failed to start Jupyter server for user: ${user.id}`, error);
    res.status(503).json({ message: 'Failed to start Jupyter server.' });
  }
};

const stopJupyterServer = async (req, res) => {
  try {
    const user = req.user;
    logger.info(`Stopping Jupyter server for user: ${user.id}`);
    const response = await jupyterService.stopServer(user);
    res.status(200).json(response);
  } catch (error) {
    logger.error(`Failed to stop Jupyter server for user: ${user.id}`, error);
    res.status(503).json({ message: 'Failed to stop Ju<PERSON>ter server.' });
  }
};

const jupyterController = {
  startJupyterServer,
  stopJupyterServer
};

export default jupyterController;
