import { Grade, Submission, User, Project, Course, Rubric } from '../models/associations.js';
import { asyncHand<PERSON> } from '../middlewares/errorHandler.middlewares.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';

/**
 * @desc    Get all grades with pagination and filtering
 * @route   GET /api/grades
 * @access  Private
 */
export const getGrades = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    projectId,
    courseId,
    studentId,
    evaluatorId,
    sortBy = 'graded_at',
    sortOrder = 'desc'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const whereClause = {};

  // Build include clause based on filters
  const includeClause = [
    {
      model: Submission,
      as: 'submission',
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'profile_picture']
        },
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'title', 'due_date'],
          include: [{
            model: Course,
            as: 'course',
            attributes: ['id', 'name', 'code']
          }]
        }
      ]
    },
    {
      model: User,
      as: 'evaluator',
      attributes: ['id', 'name', 'email']
    }
  ];

  // Apply filters
  if (projectId) {
    includeClause[0].where = { project_id: projectId };
  }

  if (courseId) {
    includeClause[0].include[1].where = { course_id: courseId };
  }

  if (studentId) {
    includeClause[0].include[0].where = { id: studentId };
  }

  if (evaluatorId) {
    whereClause.evaluator_id = evaluatorId;
  }

  // For students, only show their own grades
  if (req.user.role === 'student') {
    includeClause[0].include[0].where = { id: req.user.id };
  }

  const { count, rows: grades } = await Grade.findAndCountAll({
    where: whereClause,
    include: includeClause,
    limit: parseInt(limit),
    offset,
    order: [[sortBy, sortOrder.toUpperCase()]]
  });

  // Transform grade data for response
  const transformedGrades = grades.map(grade => ({
    id: grade.id,
    totalScore: grade.total_score,
    maxScore: grade.max_score,
    percentage: grade.percentage,
    letterGrade: grade.letter_grade,
    feedback: grade.feedback,
    rubricScores: grade.rubric_scores,
    gradedAt: grade.graded_at,
    submission: {
      id: grade.submission.id,
      submittedAt: grade.submission.submitted_at,
      status: grade.submission.status,
      user: {
        id: grade.submission.user.id,
        name: grade.submission.user.name,
        email: grade.submission.user.email,
        profilePicture: grade.submission.user.profile_picture
      },
      project: {
        id: grade.submission.project.id,
        title: grade.submission.project.title,
        dueDate: grade.submission.project.due_date,
        course: {
          id: grade.submission.project.course.id,
          name: grade.submission.project.course.name,
          code: grade.submission.project.course.code
        }
      }
    },
    evaluator: {
      id: grade.evaluator.id,
      name: grade.evaluator.name,
      email: grade.evaluator.email
    },
    createdAt: grade.created_at,
    updatedAt: grade.updated_at
  }));

  res.json({
    success: true,
    data: {
      grades: transformedGrades,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / parseInt(limit)),
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

/**
 * @desc    Get grade by ID
 * @route   GET /api/grades/:id
 * @access  Private
 */
export const getGradeById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const grade = await Grade.findByPk(id, {
    include: [
      {
        model: Submission,
        as: 'submission',
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'profile_picture']
          },
          {
            model: Project,
            as: 'project',
            include: [{
              model: Course,
              as: 'course',
              include: [{
                model: User,
                as: 'instructor',
                attributes: ['id', 'name', 'email']
              }]
            }, {
              model: Rubric,
              as: 'rubrics'
            }]
          }
        ]
      },
      {
        model: User,
        as: 'evaluator',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!grade) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Grade not found'
    });
  }

  // Check if user has access to this grade
  const hasAccess = req.userRoles.includes('admin') || 
                   grade.submission.user_id === req.user.id ||
                   grade.submission.project.course.instructor_id === req.user.id ||
                   grade.evaluator_id === req.user.id;

  if (!hasAccess) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view this grade'
    });
  }

  const gradeResponse = {
    id: grade.id,
    totalScore: grade.total_score,
    maxScore: grade.max_score,
    percentage: grade.percentage,
    letterGrade: grade.letter_grade,
    feedback: grade.feedback,
    rubricScores: grade.rubric_scores,
    gradedAt: grade.graded_at,
    submission: {
      id: grade.submission.id,
      submittedAt: grade.submission.submitted_at,
      status: grade.submission.status,
      notebookS3Url: grade.submission.notebook_s3_url,
      user: {
        id: grade.submission.user.id,
        name: grade.submission.user.name,
        email: grade.submission.user.email,
        profilePicture: grade.submission.user.profile_picture
      },
      project: {
        id: grade.submission.project.id,
        title: grade.submission.project.title,
        description: grade.submission.project.description,
        dueDate: grade.submission.project.due_date,
        instructions: grade.submission.project.instructions,
        course: {
          id: grade.submission.project.course.id,
          name: grade.submission.project.course.name,
          code: grade.submission.project.course.code,
          instructor: grade.submission.project.course.instructor
        },
        rubrics: grade.submission.project.rubrics?.map(rubric => ({
          id: rubric.id,
          name: rubric.name,
          description: rubric.description,
          criteria: rubric.criteria,
          maxScore: rubric.max_score,
          weight: rubric.weight
        })) || []
      }
    },
    evaluator: {
      id: grade.evaluator.id,
      name: grade.evaluator.name,
      email: grade.evaluator.email
    },
    createdAt: grade.created_at,
    updatedAt: grade.updated_at
  };

  res.json({
    success: true,
    grade: gradeResponse
  });
});

/**
 * @desc    Create or update grade
 * @route   POST /api/grades
 * @access  Private (Instructor, Admin)
 */
export const createOrUpdateGrade = asyncHandler(async (req, res) => {
  const {
    submissionId,
    totalScore,
    maxScore,
    percentage,
    letterGrade,
    feedback,
    rubricScores = {}
  } = req.body;

  if (!submissionId) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Submission ID is required'
    });
  }

  // Find the submission
  const submission = await Submission.findByPk(submissionId, {
    include: [{
      model: Project,
      as: 'project',
      include: [{
        model: Course,
        as: 'course'
      }]
    }]
  });

  if (!submission) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Submission not found'
    });
  }

  // Check if user has permission to grade this submission
  const hasPermission = req.userRoles.includes('admin') || 
                       submission.project.course.instructor_id === req.user.id;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to grade this submission'
    });
  }

  // Check if submission is in grading status
  if (submission.status !== 'submitted' && submission.status !== 'grading') {
    return res.status(400).json({
      error: 'Invalid Status',
      message: 'Can only grade submitted submissions'
    });
  }

  // Validate score inputs
  if (totalScore < 0 || (maxScore && totalScore > maxScore)) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Total score must be between 0 and maximum score'
    });
  }

  // Calculate percentage if not provided
  let calculatedPercentage = percentage;
  if (!calculatedPercentage && maxScore && maxScore > 0) {
    calculatedPercentage = (totalScore / maxScore) * 100;
  }

  // Generate letter grade if not provided
  let calculatedLetterGrade = letterGrade;
  if (!calculatedLetterGrade && calculatedPercentage !== undefined) {
    calculatedLetterGrade = calculateLetterGrade(calculatedPercentage);
  }

  // Find existing grade or create new one
  let grade = await Grade.findOne({
    where: { submission_id: submissionId }
  });

  if (grade) {
    // Update existing grade
    await grade.update({
      total_score: totalScore,
      max_score: maxScore,
      percentage: calculatedPercentage,
      letter_grade: calculatedLetterGrade,
      feedback,
      rubric_scores: rubricScores,
      evaluator_id: req.user.id,
      graded_at: new Date()
    });
  } else {
    // Create new grade
    grade = await Grade.create({
      submission_id: submissionId,
      total_score: totalScore,
      max_score: maxScore,
      percentage: calculatedPercentage,
      letter_grade: calculatedLetterGrade,
      feedback,
      rubric_scores: rubricScores,
      evaluator_id: req.user.id,
      graded_at: new Date()
    });
  }

  // Update submission status to graded
  await submission.update({ status: 'graded' });

  logger.info(`Grade created/updated: Submission ${submissionId} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Grade saved successfully',
    grade: {
      id: grade.id,
      totalScore: grade.total_score,
      maxScore: grade.max_score,
      percentage: grade.percentage,
      letterGrade: grade.letter_grade,
      feedback: grade.feedback,
      gradedAt: grade.graded_at
    }
  });
});

/**
 * @desc    Update grade
 * @route   PUT /api/grades/:id
 * @access  Private (Instructor, Admin)
 */
export const updateGrade = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    totalScore,
    maxScore,
    percentage,
    letterGrade,
    feedback,
    rubricScores
  } = req.body;

  const grade = await Grade.findByPk(id, {
    include: [{
      model: Submission,
      as: 'submission',
      include: [{
        model: Project,
        as: 'project',
        include: [{
          model: Course,
          as: 'course'
        }]
      }]
    }]
  });

  if (!grade) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Grade not found'
    });
  }

  // Check permissions
  const hasPermission = req.userRoles.includes('admin') || 
                       grade.submission.project.course.instructor_id === req.user.id ||
                       grade.evaluator_id === req.user.id;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to update this grade'
    });
  }

  // Validate score inputs
  if (totalScore !== undefined && (totalScore < 0 || (maxScore && totalScore > maxScore))) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Total score must be between 0 and maximum score'
    });
  }

  // Build update data
  const updateData = {};
  if (totalScore !== undefined) updateData.total_score = totalScore;
  if (maxScore !== undefined) updateData.max_score = maxScore;
  if (percentage !== undefined) updateData.percentage = percentage;
  if (letterGrade !== undefined) updateData.letter_grade = letterGrade;
  if (feedback !== undefined) updateData.feedback = feedback;
  if (rubricScores !== undefined) updateData.rubric_scores = rubricScores;

  // Recalculate percentage if total or max score changed
  if (totalScore !== undefined || maxScore !== undefined) {
    const newTotal = totalScore !== undefined ? totalScore : grade.total_score;
    const newMax = maxScore !== undefined ? maxScore : grade.max_score;
    if (newMax && newMax > 0) {
      updateData.percentage = (newTotal / newMax) * 100;
    }
  }

  // Recalculate letter grade if percentage changed
  if (updateData.percentage !== undefined) {
    updateData.letter_grade = calculateLetterGrade(updateData.percentage);
  }

  updateData.evaluator_id = req.user.id;
  updateData.graded_at = new Date();

  await grade.update(updateData);

  logger.info(`Grade updated: ${grade.id} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Grade updated successfully',
    grade: {
      id: grade.id,
      totalScore: grade.total_score,
      maxScore: grade.max_score,
      percentage: grade.percentage,
      letterGrade: grade.letter_grade,
      feedback: grade.feedback,
      gradedAt: grade.graded_at
    }
  });
});

/**
 * @desc    Delete grade
 * @route   DELETE /api/grades/:id
 * @access  Private (Instructor, Admin)
 */
export const deleteGrade = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const grade = await Grade.findByPk(id, {
    include: [{
      model: Submission,
      as: 'submission',
      include: [{
        model: Project,
        as: 'project',
        include: [{
          model: Course,
          as: 'course'
        }]
      }]
    }]
  });

  if (!grade) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Grade not found'
    });
  }

  // Check permissions
  const hasPermission = req.userRoles.includes('admin') || 
                       grade.submission.project.course.instructor_id === req.user.id ||
                       grade.evaluator_id === req.user.id;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to delete this grade'
    });
  }

  // Update submission status back to submitted
  await grade.submission.update({ status: 'submitted' });

  // Delete grade
  await grade.destroy();

  logger.info(`Grade deleted: ${id} by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Grade deleted successfully'
  });
});

/**
 * @desc    Get grading queue (submissions ready to grade)
 * @route   GET /api/grades/queue
 * @access  Private (Instructor, Admin)
 */
export const getGradingQueue = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    courseId,
    projectId,
    sortBy = 'submitted_at',
    sortOrder = 'asc'
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const whereClause = { status: 'submitted' };

  const includeClause = [
    {
      model: User,
      as: 'user',
      attributes: ['id', 'name', 'email', 'profile_picture']
    },
    {
      model: Project,
      as: 'project',
      attributes: ['id', 'title', 'due_date', 'difficulty_level'],
      include: [{
        model: Course,
        as: 'course',
        attributes: ['id', 'name', 'code']
      }]
    },
    {
      model: Grade,
      as: 'grade',
      required: false // LEFT JOIN to include submissions without grades
    }
  ];

  // Apply filters
  if (projectId) {
    whereClause.project_id = projectId;
  }

  if (courseId) {
    includeClause[1].where = { course_id: courseId };
  }

  // Only show submissions from courses the user instructs (unless admin)
  if (!req.userRoles.includes('admin')) {
    includeClause[1].include[0].where = { instructor_id: req.user.id };
  }

  const { count, rows: submissions } = await Submission.findAndCountAll({
    where: whereClause,
    include: includeClause,
    limit: parseInt(limit),
    offset,
    order: [[sortBy, sortOrder.toUpperCase()]]
  });

  // Transform data for response
  const transformedSubmissions = submissions.map(submission => ({
    id: submission.id,
    submittedAt: submission.submitted_at,
    isGraded: !!submission.grade,
    user: {
      id: submission.user.id,
      name: submission.user.name,
      email: submission.user.email,
      profilePicture: submission.user.profile_picture
    },
    project: {
      id: submission.project.id,
      title: submission.project.title,
      dueDate: submission.project.due_date,
      difficultyLevel: submission.project.difficulty_level,
      course: {
        id: submission.project.course.id,
        name: submission.project.course.name,
        code: submission.project.course.code
      }
    },
    grade: submission.grade ? {
      id: submission.grade.id,
      totalScore: submission.grade.total_score,
      maxScore: submission.grade.max_score,
      percentage: submission.grade.percentage,
      letterGrade: submission.grade.letter_grade
    } : null
  }));

  res.json({
    success: true,
    data: {
      submissions: transformedSubmissions,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / parseInt(limit)),
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

/**
 * @desc    Get grade statistics for a project
 * @route   GET /api/grades/project/:projectId/statistics
 * @access  Private (Instructor, Admin)
 */
export const getGradeStatistics = asyncHandler(async (req, res) => {
  const { projectId } = req.params;

  const project = await Project.findByPk(projectId, {
    include: [{
      model: Course,
      as: 'course'
    }]
  });

  if (!project) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Project not found'
    });
  }

  // Check permissions
  const hasPermission = req.userRoles.includes('admin') || 
                       project.course.instructor_id === req.user.id;

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view grade statistics'
    });
  }

  // Get grade statistics
  const gradeStats = await Grade.findAll({
    include: [{
      model: Submission,
      as: 'submission',
      where: { project_id: projectId },
      attributes: []
    }],
    attributes: [
      [sequelize.fn('COUNT', sequelize.col('Grade.id')), 'totalGrades'],
      [sequelize.fn('AVG', sequelize.col('percentage')), 'averagePercentage'],
      [sequelize.fn('MIN', sequelize.col('percentage')), 'minPercentage'],
      [sequelize.fn('MAX', sequelize.col('percentage')), 'maxPercentage'],
      [sequelize.fn('STDDEV', sequelize.col('percentage')), 'standardDeviation']
    ]
  });

  // Get grade distribution
  const gradeDistribution = await Grade.findAll({
    include: [{
      model: Submission,
      as: 'submission',
      where: { project_id: projectId },
      attributes: []
    }],
    attributes: [
      'letter_grade',
      [sequelize.fn('COUNT', sequelize.col('Grade.id')), 'count']
    ],
    group: ['letter_grade']
  });

  const stats = gradeStats[0];

  res.json({
    success: true,
    statistics: {
      overview: {
        totalGrades: parseInt(stats.getDataValue('totalGrades')) || 0,
        averagePercentage: parseFloat(stats.getDataValue('averagePercentage')) || 0,
        minPercentage: parseFloat(stats.getDataValue('minPercentage')) || 0,
        maxPercentage: parseFloat(stats.getDataValue('maxPercentage')) || 0,
        standardDeviation: parseFloat(stats.getDataValue('standardDeviation')) || 0
      },
      distribution: gradeDistribution.map(item => ({
        grade: item.letter_grade,
        count: parseInt(item.getDataValue('count'))
      }))
    }
  });
});

/**
 * Helper function to calculate letter grade from percentage
 */
function calculateLetterGrade(percentage) {
  if (percentage >= 90) return 'A+';
  if (percentage >= 85) return 'A';
  if (percentage >= 80) return 'A-';
  if (percentage >= 75) return 'B+';
  if (percentage >= 70) return 'B';
  if (percentage >= 65) return 'B-';
  if (percentage >= 60) return 'C+';
  if (percentage >= 55) return 'C';
  if (percentage >= 50) return 'C-';
  if (percentage >= 45) return 'D';
  return 'F';
}

/**
 * @desc    Bulk grade submissions
 * @route   POST /api/grades/bulk
 * @access  Private (Instructor, Admin)
 */
export const bulkGradeSubmissions = asyncHandler(async (req, res) => {
  const { submissions } = req.body;

  if (!Array.isArray(submissions) || submissions.length === 0) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Submissions array is required and must not be empty'
    });
  }

  const results = {
    success: [],
    errors: []
  };

  for (const submissionGrade of submissions) {
    try {
      const {
        submissionId,
        totalScore,
        maxScore,
        percentage,
        letterGrade,
        feedback,
        rubricScores
      } = submissionGrade;

      // Validate submission exists and user has permission
      const submission = await Submission.findByPk(submissionId, {
        include: [{
          model: Project,
          as: 'project',
          include: [{
            model: Course,
            as: 'course'
          }]
        }]
      });

      if (!submission) {
        results.errors.push({
          submissionId,
          error: 'Submission not found'
        });
        continue;
      }

      const hasPermission = req.userRoles.includes('admin') || 
                           submission.project.course.instructor_id === req.user.id;

      if (!hasPermission) {
        results.errors.push({
          submissionId,
          error: 'Permission denied'
        });
        continue;
      }

      // Calculate values
      let calculatedPercentage = percentage;
      if (!calculatedPercentage && maxScore && maxScore > 0) {
        calculatedPercentage = (totalScore / maxScore) * 100;
      }

      let calculatedLetterGrade = letterGrade;
      if (!calculatedLetterGrade && calculatedPercentage !== undefined) {
        calculatedLetterGrade = calculateLetterGrade(calculatedPercentage);
      }

      // Create or update grade
      const [grade, created] = await Grade.findOrCreate({
        where: { submission_id: submissionId },
        defaults: {
          total_score: totalScore,
          max_score: maxScore,
          percentage: calculatedPercentage,
          letter_grade: calculatedLetterGrade,
          feedback,
          rubric_scores: rubricScores || {},
          evaluator_id: req.user.id,
          graded_at: new Date()
        }
      });

      if (!created) {
        await grade.update({
          total_score: totalScore,
          max_score: maxScore,
          percentage: calculatedPercentage,
          letter_grade: calculatedLetterGrade,
          feedback,
          rubric_scores: rubricScores || grade.rubric_scores,
          evaluator_id: req.user.id,
          graded_at: new Date()
        });
      }

      // Update submission status
      await submission.update({ status: 'graded' });

      results.success.push({
        submissionId,
        gradeId: grade.id,
        message: created ? 'Grade created' : 'Grade updated'
      });

    } catch (error) {
      results.errors.push({
        submissionId: submissionGrade.submissionId,
        error: error.message
      });
    }
  }

  logger.info(`Bulk grading completed: ${results.success.length} successful, ${results.errors.length} errors by ${req.user.email}`);

  res.json({
    success: true,
    message: 'Bulk grading completed',
    results
  });
});