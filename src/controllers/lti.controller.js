import { async<PERSON><PERSON><PERSON> } from '../middlewares/errorHandler.middlewares.js';
import ltiService from '../services/lti.service.js';
import {
  LtiPlatform,
  LtiDeployment,
  LtiContext,
  LtiResourceLink,
  LtiLineItem
} from '../models/ltiAssociations.models.js';
import { User, Course, Project, Grade, Submission } from '../models/associations.js';
import logger from '../config/logger.config.js';

/**
 * @desc    LTI login initiation endpoint
 * @route   POST /api/lti/login
 * @access  Public
 */
export const initiateLogin = asyncHandler(async (req, res) => {
  const {
    iss,
    login_hint,
    target_link_uri,
    lti_message_hint,
    client_id
  } = req.body;

  // Find platform by issuer
  const platform = await LtiPlatform.findOne({
    where: { platformId: iss, clientId: client_id, isActive: true }
  });

  if (!platform) {
    return res.status(400).json({
      error: 'Invalid Platform',
      message: 'Platform not registered or inactive'
    });
  }

  try {
    // Generate authentication request
    const authUrl = ltiService.generateAuthRequest(
      platform,
      target_link_uri,
      login_hint,
      lti_message_hint
    );

    // Redirect to platform authentication
    res.redirect(authUrl);
  } catch (error) {
    logger.error('LTI login initiation failed:', error);
    res.status(500).json({
      error: 'Login Failed',
      message: 'Could not initiate LTI login'
    });
  }
});

/**
 * @desc    LTI launch endpoint
 * @route   POST /api/lti/launch
 * @access  Public
 */
export const handleLaunch = asyncHandler(async (req, res) => {
  const { id_token, state } = req.body;

  if (!id_token || !state) {
    return res.status(400).json({
      error: 'Missing Parameters',
      message: 'id_token and state are required'
    });
  }

  try {
    // Process LTI launch
    const launchResult = await ltiService.processLaunch(id_token, state);
    
    // Determine the target page based on launch data
    const targetPage = determineLaunchTarget(launchResult.launchData, launchResult.resourceLink);
    
    // Create session for the user
    req.session.user = launchResult.user;
    req.session.ltiContext = launchResult.context;
    req.session.ltiResourceLink = launchResult.resourceLink;
    req.session.ltiLaunchData = launchResult.launchData;

    // Redirect to the appropriate page in the application
    const redirectUrl = `${process.env.FRONTEND_URL}/${targetPage}`;
    
    res.redirect(redirectUrl);
    
  } catch (error) {
    logger.error('LTI launch failed:', error);
    res.status(400).json({
      error: 'Launch Failed',
      message: error.message
    });
  }
});

/**
 * @desc    JWKS endpoint for tool public keys
 * @route   GET /api/lti/jwks
 * @access  Public
 */
export const getJWKS = asyncHandler(async (req, res) => {
  const jwks = ltiService.getJWKS();
  res.json(jwks);
});

/**
 * @desc    Deep linking endpoint
 * @route   POST /api/lti/deep-linking
 * @access  Public
 */
export const handleDeepLinking = asyncHandler(async (req, res) => {
  const { id_token, state } = req.body;

  try {
    // Process launch to get platform and user info
    const launchResult = await ltiService.processLaunch(id_token, state);
    const deepLinkingClaim = launchResult.launchData['https://purl.imsglobal.org/spec/lti-dl/claim/deep_linking_settings'];
    
    if (!deepLinkingClaim) {
      throw new Error('Invalid deep linking request');
    }

    // Get available projects for the context
    const projects = await Project.findAll({
      where: { status: 'published' },
      include: [{
        model: Course,
        as: 'course',
        where: launchResult.context?.courseId ? { id: launchResult.context.courseId } : undefined,
        required: false
      }]
    });

    // Create deep linking response
    const deepLinkingJWT = ltiService.createDeepLinkingResponse(
      projects,
      deepLinkingClaim,
      launchResult.platform
    );

    // Return form that will post back to platform
    const returnUrl = deepLinkingClaim.deep_link_return_url;
    
    res.send(`
      <html>
        <body>
          <form id="deepLinkingForm" action="${returnUrl}" method="post">
            <input type="hidden" name="JWT" value="${deepLinkingJWT}">
          </form>
          <script>
            document.getElementById('deepLinkingForm').submit();
          </script>
        </body>
      </html>
    `);

  } catch (error) {
    logger.error('Deep linking failed:', error);
    res.status(400).json({
      error: 'Deep Linking Failed',
      message: error.message
    });
  }
});

/**
 * @desc    Register new LTI platform
 * @route   POST /api/lti/platforms
 * @access  Private (Admin)
 */
export const registerPlatform = asyncHandler(async (req, res) => {
  const {
    platformId,
    platformName,
    clientId,
    authLoginUrl,
    authTokenUrl,
    keySetUrl,
    settings = {}
  } = req.body;

  // Check if platform already exists
  const existingPlatform = await LtiPlatform.findOne({
    where: { platformId }
  });

  if (existingPlatform) {
    return res.status(409).json({
      error: 'Platform Exists',
      message: 'Platform is already registered'
    });
  }

  try {
    // Generate key pair for this platform
    const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: { type: 'spki', format: 'pem' },
      privateKeyEncoding: { type: 'pkcs8', format: 'pem' }
    });

    const platform = await LtiPlatform.create({
      platformId,
      platformName,
      clientId,
      authLoginUrl,
      authTokenUrl,
      keySetUrl,
      privateKey,
      publicKey,
      keyId: `${platformId}-${Date.now()}`,
      settings
    });

    logger.info(`LTI Platform registered: ${platformName}`);

    res.status(201).json({
      success: true,
      message: 'Platform registered successfully',
      platform: {
        id: platform.id,
        platformId: platform.platformId,
        platformName: platform.platformName,
        clientId: platform.clientId,
        isActive: platform.isActive
      }
    });

  } catch (error) {
    logger.error('Platform registration failed:', error);
    res.status(500).json({
      error: 'Registration Failed',
      message: 'Could not register platform'
    });
  }
});

/**
 * @desc    Get all registered platforms
 * @route   GET /api/lti/platforms
 * @access  Private (Admin)
 */
export const getPlatforms = asyncHandler(async (req, res) => {
  const platforms = await LtiPlatform.findAll({
    attributes: [
      'id', 'platformId', 'platformName', 'clientId', 
      'isActive', 'createdAt', 'updatedAt'
    ],
    include: [{
      model: LtiDeployment,
      as: 'deployments',
      attributes: ['id', 'deploymentId', 'deploymentName', 'isActive']
    }]
  });

  res.json({
    success: true,
    platforms: platforms.map(platform => ({
      id: platform.id,
      platformId: platform.platformId,
      platformName: platform.platformName,
      clientId: platform.clientId,
      isActive: platform.isActive,
      deploymentCount: platform.deployments?.length || 0,
      deployments: platform.deployments || [],
      createdAt: platform.createdAt,
      updatedAt: platform.updatedAt
    }))
  });
});

/**
 * @desc    Update platform configuration
 * @route   PUT /api/lti/platforms/:id
 * @access  Private (Admin)
 */
export const updatePlatform = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    platformName,
    authLoginUrl,
    authTokenUrl,
    keySetUrl,
    isActive,
    settings
  } = req.body;

  const platform = await LtiPlatform.findByPk(id);

  if (!platform) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Platform not found'
    });
  }

  const updateData = {};
  if (platformName) updateData.platformName = platformName;
  if (authLoginUrl) updateData.authLoginUrl = authLoginUrl;
  if (authTokenUrl) updateData.authTokenUrl = authTokenUrl;
  if (keySetUrl) updateData.keySetUrl = keySetUrl;
  if (isActive !== undefined) updateData.isActive = isActive;
  if (settings) updateData.settings = { ...platform.settings, ...settings };

  await platform.update(updateData);

  logger.info(`LTI Platform updated: ${platform.platformName}`);

  res.json({
    success: true,
    message: 'Platform updated successfully',
    platform: {
      id: platform.id,
      platformId: platform.platformId,
      platformName: platform.platformName,
      isActive: platform.isActive,
      updatedAt: platform.updatedAt
    }
  });
});

/**
 * @desc    Delete platform
 * @route   DELETE /api/lti/platforms/:id
 * @access  Private (Admin)
 */
export const deletePlatform = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const platform = await LtiPlatform.findByPk(id);

  if (!platform) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Platform not found'
    });
  }

  // Check if platform has active contexts
  const activeContexts = await LtiContext.count({
    where: { platformId: id, isActive: true }
  });

  if (activeContexts > 0) {
    return res.status(409).json({
      error: 'Platform In Use',
      message: 'Cannot delete platform with active contexts'
    });
  }

  await platform.destroy();

  logger.info(`LTI Platform deleted: ${platform.platformName}`);

  res.json({
    success: true,
    message: 'Platform deleted successfully'
  });
});

/**
 * @desc    Send grade to LMS
 * @route   POST /api/lti/grades
 * @access  Private
 */
export const sendGradeToLMS = asyncHandler(async (req, res) => {
  const { submissionId, gradeId } = req.body;

  // Find submission and grade
  const submission = await Submission.findByPk(submissionId, {
    include: [{
      model: Project,
      as: 'project',
      include: [{
        model: LtiResourceLink,
        as: 'ltiResourceLinks'
      }]
    }, {
      model: User,
      as: 'user'
    }]
  });

  const grade = await Grade.findByPk(gradeId);

  if (!submission || !grade) {
    return res.status(404).json({
      error: 'Not Found',
      message: 'Submission or grade not found'
    });
  }

  // Find LTI resource link
  const resourceLink = submission.project.ltiResourceLinks?.[0];
  if (!resourceLink) {
    return res.status(400).json({
      error: 'No LTI Integration',
      message: 'This project is not linked to an LTI resource'
    });
  }

  try {
    // Find or create line item
    const lineItem = await ltiService.createLineItem(
      resourceLink,
      submission.project,
      grade.maxScore
    );

    // Get platform
    const platform = await LtiPlatform.findByPk(resourceLink.platformId);

    // Send grade to LMS
    await ltiService.sendGrade(
      submission.user.lmsUserId,
      lineItem,
      grade.totalScore,
      platform
    );

    logger.info(`Grade sent to LMS: ${grade.totalScore}/${grade.maxScore} for submission ${submissionId}`);

    res.json({
      success: true,
      message: 'Grade sent to LMS successfully'
    });

  } catch (error) {
    logger.error('Failed to send grade to LMS:', error);
    res.status(500).json({
      error: 'Grade Sync Failed',
      message: 'Could not send grade to LMS'
    });
  }
});

/**
 * @desc    Get LTI contexts for a course
 * @route   GET /api/lti/contexts/course/:courseId
 * @access  Private
 */
export const getCourseContexts = asyncHandler(async (req, res) => {
  const { courseId } = req.params;

  const contexts = await LtiContext.findAll({
    where: { courseId, isActive: true },
    include: [{
      model: LtiPlatform,
      as: 'platform',
      attributes: ['id', 'platformName', 'platformId']
    }, {
      model: LtiResourceLink,
      as: 'resourceLinks',
      include: [{
        model: Project,
        as: 'project',
        attributes: ['id', 'title', 'status']
      }]
    }]
  });

  res.json({
    success: true,
    contexts: contexts.map(context => ({
      id: context.id,
      contextId: context.contextId,
      contextTitle: context.contextTitle,
      contextLabel: context.contextLabel,
      platform: context.platform,
      resourceLinks: context.resourceLinks?.map(link => ({
        id: link.id,
        resourceLinkId: link.resourceLinkId,
        title: link.resourceLinkTitle,
        project: link.project
      })) || []
    }))
  });
});

/**
 * Helper function to determine launch target based on LTI data
 */
function determineLaunchTarget(launchData, resourceLink) {
  const messageType = launchData['https://purl.imsglobal.org/spec/lti/claim/message_type'];
  
  // Deep linking request
  if (messageType === 'LtiDeepLinkingRequest') {
    return 'deep-linking';
  }
  
  // Resource link launch
  if (messageType === 'LtiResourceLinkRequest' && resourceLink?.projectId) {
    return `project/${resourceLink.projectId}`;
  }
  
  // Default to dashboard
  return 'dashboard';
}

/**
 * @desc    Get LTI configuration for platform registration
 * @route   GET /api/lti/config
 * @access  Public
 */
export const getLTIConfiguration = asyncHandler(async (req, res) => {
  const toolUrl = process.env.LTI_TOOL_URL || req.protocol + '://' + req.get('host');
  
  const configuration = {
    title: 'BITS-DataScience Projects Platform',
    description: 'Interactive data science projects and assignments platform for BITS Pilani',
    target_link_uri: `${toolUrl}/lti/launch`,
    oidc_initiation_url: `${toolUrl}/api/lti/login`,
    public_jwk_url: `${toolUrl}/api/lti/jwks`,
    scopes: [
      'openid',
      'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem',
      'https://purl.imsglobal.org/spec/lti-ags/scope/score',
      'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly'
    ],
    extensions: [{
      domain: new URL(toolUrl).hostname,
      tool_id: 'bits-datascience-platform',
      platform: 'bits.edu',
      settings: {
        text: 'BITS DataScience Platform',
        icon_url: `${toolUrl}/assets/bits-logo.png`,
        selection_width: 800,
        selection_height: 600
      },
      privacy_level: 'public'
    }],
    custom_fields: {
      project_id: '$ResourceLink.id',
      context_id: '$Context.id',
      user_id: '$User.id'
    },
    claims: [
      'iss',
      'aud', 
      'exp',
      'iat',
      'nonce',
      'https://purl.imsglobal.org/spec/lti/claim/deployment_id',
      'https://purl.imsglobal.org/spec/lti/claim/message_type',
      'https://purl.imsglobal.org/spec/lti/claim/version',
      'https://purl.imsglobal.org/spec/lti/claim/resource_link',
      'https://purl.imsglobal.org/spec/lti/claim/context',
      'https://purl.imsglobal.org/spec/lti/claim/tool_platform',
      'https://purl.imsglobal.org/spec/lti/claim/roles',
      'https://purl.imsglobal.org/spec/lti/claim/custom'
    ]
  };

  res.json(configuration);
});