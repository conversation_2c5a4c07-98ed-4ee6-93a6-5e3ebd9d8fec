import { asyncHand<PERSON> } from '../middlewares/errorHandler.middlewares.js';
import studentDashboardService from '../services/studentDashboard.service.js';
import logger from '../config/logger.config.js';

/**
 * @desc    Get comprehensive student dashboard
 * @route   GET /api/student/dashboard
 * @access  Private (Student)
 */
export const getStudentDashboard = asyncHandler(async (req, res) => {
  try {
    const studentId = req.user.id;
    const { courseId } = req.query;

    const dashboard = await studentDashboardService.getStudentDashboard(studentId, courseId);
    
    res.json({
      success: true,
      message: 'Student dashboard retrieved successfully',
      data: dashboard
    });
  } catch (error) {
    logger.error('Error getting student dashboard:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve student dashboard'
    });
  }
});

/**
 * @desc    Get project progress data
 * @route   GET /api/student/projects/:projectId/progress
 * @access  Private (Student)
 */
export const getProjectProgress = asyncHandler(async (req, res) => {
  try {
    const { projectId } = req.params;
    const studentId = req.user.id;

    const progress = await studentDashboardService.getProjectProgress(studentId, projectId);
    
    res.json({
      success: true,
      message: 'Project progress retrieved successfully',
      data: progress
    });
  } catch (error) {
    if (error.message === 'Project progress not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Project progress not found'
      });
    }
    
    logger.error('Error getting project progress:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve project progress'
    });
  }
});

/**
 * @desc    Get upcoming deadlines
 * @route   GET /api/student/deadlines
 * @access  Private (Student)
 */
export const getUpcomingDeadlines = asyncHandler(async (req, res) => {
  try {
    const studentId = req.user.id;
    const { days = 30 } = req.query;

    const deadlines = await studentDashboardService.getUpcomingDeadlines(studentId, parseInt(days));
    
    res.json({
      success: true,
      message: 'Upcoming deadlines retrieved successfully',
      data: {
        deadlines,
        total: deadlines.length,
        daysAhead: parseInt(days)
      }
    });
  } catch (error) {
    logger.error('Error getting upcoming deadlines:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve upcoming deadlines'
    });
  }
});

/**
 * @desc    Get recent activity
 * @route   GET /api/student/activity
 * @access  Private (Student)
 */
export const getRecentActivity = asyncHandler(async (req, res) => {
  try {
    const studentId = req.user.id;
    const { limit = 10, activityType } = req.query;

    const activities = await studentDashboardService.getRecentActivity(studentId, parseInt(limit), activityType);
    
    res.json({
      success: true,
      message: 'Recent activity retrieved successfully',
      data: {
        activities,
        total: activities.length,
        limit: parseInt(limit),
        activityType: activityType || 'all'
      }
    });
  } catch (error) {
    logger.error('Error getting recent activity:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve recent activity'
    });
  }
});

/**
 * @desc    Get course progress summary
 * @route   GET /api/student/courses/:courseId/progress
 * @access  Private (Student)
 */
export const getCourseProgress = asyncHandler(async (req, res) => {
  try {
    const { courseId } = req.params;
    const studentId = req.user.id;

    const progress = await studentDashboardService.getCourseProgressSummary(studentId, courseId);
    
    res.json({
      success: true,
      message: 'Course progress retrieved successfully',
      data: progress
    });
  } catch (error) {
    logger.error('Error getting course progress:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve course progress'
    });
  }
});

/**
 * @desc    Update project progress
 * @route   PUT /api/student/projects/:projectId/progress
 * @access  Private (Student)
 */
export const updateProjectProgress = asyncHandler(async (req, res) => {
  try {
    const { projectId } = req.params;
    const studentId = req.user.id;
    const { courseId, progressPercentage, currentPhase, timeSpent, status } = req.body;

    if (!courseId) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Course ID is required'
      });
    }

    const progressData = {
      progress_percentage: progressPercentage,
      current_phase: currentPhase,
      time_spent_hours: timeSpent,
      status,
      last_activity: new Date()
    };

    // Set start date if this is the first time starting
    if (status === 'in_progress') {
      progressData.start_date = new Date();
    }

    // Set completion date if completed
    if (status === 'completed') {
      progressData.completion_date = new Date();
    }

    const progress = await studentDashboardService.updateProjectProgress(
      studentId,
      projectId,
      courseId,
      progressData
    );
    
    res.json({
      success: true,
      message: 'Project progress updated successfully',
      data: progress
    });
  } catch (error) {
    logger.error('Error updating project progress:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update project progress'
    });
  }
});

/**
 * @desc    Mark activity as read
 * @route   PUT /api/student/activity/:activityId/read
 * @access  Private (Student)
 */
export const markActivityAsRead = asyncHandler(async (req, res) => {
  try {
    const { activityId } = req.params;
    const studentId = req.user.id;

    const activity = await studentDashboardService.markActivityAsRead(activityId, studentId);
    
    res.json({
      success: true,
      message: 'Activity marked as read successfully',
      data: activity
    });
  } catch (error) {
    if (error.message === 'Activity not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Activity not found'
      });
    }
    
    logger.error('Error marking activity as read:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to mark activity as read'
    });
  }
});

/**
 * @desc    Get unread activity count
 * @route   GET /api/student/activity/unread-count
 * @access  Private (Student)
 */
export const getUnreadActivityCount = asyncHandler(async (req, res) => {
  try {
    const studentId = req.user.id;

    const count = await studentDashboardService.getUnreadActivityCount(studentId);
    
    res.json({
      success: true,
      message: 'Unread activity count retrieved successfully',
      data: {
        unreadCount: count
      }
    });
  } catch (error) {
    logger.error('Error getting unread activity count:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve unread activity count'
    });
  }
});

/**
 * @desc    Get student project statistics
 * @route   GET /api/student/projects/stats
 * @access  Private (Student)
 */
export const getStudentProjectStats = asyncHandler(async (req, res) => {
  try {
    const studentId = req.user.id;
    const { courseId } = req.query;

    const stats = await studentDashboardService.getStudentProjectStats(studentId, courseId);
    
    res.json({
      success: true,
      message: 'Student project statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    logger.error('Error getting student project stats:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve student project statistics'
    });
  }
});

/**
 * @desc    Get student project overview
 * @route   GET /api/student/projects/overview
 * @access  Private (Student)
 */
export const getStudentProjectOverview = asyncHandler(async (req, res) => {
  try {
    const studentId = req.user.id;
    const { courseId } = req.query;

    const overview = await studentDashboardService.getStudentProjectOverview(studentId, courseId);
    
    res.json({
      success: true,
      message: 'Student project overview retrieved successfully',
      data: overview
    });
  } catch (error) {
    logger.error('Error getting student project overview:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve student project overview'
    });
  }
});
