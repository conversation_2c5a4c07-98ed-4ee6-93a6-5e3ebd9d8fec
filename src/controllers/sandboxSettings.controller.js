import { Project, ProjectSandboxSettings, User } from '../models/associations.js';
import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import logger from '../config/logger.config.js';

/**
 * @desc    Get sandbox settings for a project
 * @route   GET /api/sandbox/projects/:projectId/sandbox-settings
 * @access  Private (Instructor, Admin)
 */
export const getSandboxSettings = asyncHandler(async (req, res) => {
  const { projectId } = req.params;

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(projectId)) {
    return res.status(400).json({
      error: 'Invalid project ID format'
    });
  }

  // Check if project exists
  const project = await Project.findByPk(projectId);
  if (!project) {
    return res.status(404).json({
      error: 'Project not found'
    });
  }

  // Get sandbox settings
  const settings = await ProjectSandboxSettings.findOne({
    where: { projectId, isActive: true }
  });

  if (!settings) {
    // Return default settings
    return res.status(200).json({
      settings: {
        projectId,
        mode: 'uv',
        cpuLimit: '0.5',
        memLimit: '1g',
        timeout: 1800,
        maxConcurrentUsers: 10,
        allowedPackages: [],
        environmentVariables: {},
        volumeMounts: [],
        networkPolicy: 'restricted',
        isActive: true
      }
    });
  }

  res.status(200).json({
    settings
  });
});

/**
 * @desc    Create sandbox settings for a project
 * @route   POST /api/sandbox/projects/:projectId/sandbox-settings
 * @access  Private (Instructor, Admin)
 */
export const createSandboxSettings = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const {
    mode = 'uv',
    cpuLimit = '0.5',
    memLimit = '1g',
    timeout = 1800,
    maxConcurrentUsers = 10,
    allowedPackages = [],
    environmentVariables = {},
    volumeMounts = [],
    networkPolicy = 'restricted'
  } = req.body;

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(projectId)) {
    return res.status(400).json({
      error: 'Invalid project ID format'
    });
  }

  // Check if project exists
  const project = await Project.findByPk(projectId);
  if (!project) {
    return res.status(404).json({
      error: 'Project not found'
    });
  }

  // Check if settings already exist
  const existingSettings = await ProjectSandboxSettings.findOne({
    where: { projectId, isActive: true }
  });

  if (existingSettings) {
    return res.status(409).json({
      error: 'Sandbox settings already exist for this project'
    });
  }

  // Create new settings
  const settings = await ProjectSandboxSettings.create({
    projectId,
    mode,
    cpuLimit,
    memLimit,
    timeout,
    maxConcurrentUsers,
    allowedPackages,
    environmentVariables,
    volumeMounts,
    networkPolicy,
    isActive: true,
    createdBy: req.user.id
  });

  logger.info(`Sandbox settings created for project ${projectId} by ${req.user.email}`);

  res.status(201).json({
    success: true,
    message: 'Sandbox settings created successfully',
    settings
  });
});

/**
 * @desc    Update sandbox settings for a project
 * @route   PUT /api/sandbox/projects/:projectId/sandbox-settings
 * @access  Private (Instructor, Admin)
 */
export const updateSandboxSettings = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const updateData = req.body;

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(projectId)) {
    return res.status(400).json({
      error: 'Invalid project ID format'
    });
  }

  // Check if project exists
  const project = await Project.findByPk(projectId);
  if (!project) {
    return res.status(404).json({
      error: 'Project not found'
    });
  }

  // Find existing settings
  const settings = await ProjectSandboxSettings.findOne({
    where: { projectId, isActive: true }
  });

  if (!settings) {
    return res.status(404).json({
      error: 'Sandbox settings not found'
    });
  }

  // Update settings
  await settings.update({
    ...updateData,
    updatedBy: req.user.id
  });

  logger.info(`Sandbox settings updated for project ${projectId} by ${req.user.email}`);

  res.status(200).json({
    success: true,
    message: 'Sandbox settings updated successfully',
    settings
  });
});

/**
 * @desc    Delete sandbox settings for a project
 * @route   DELETE /api/sandbox/projects/:projectId/sandbox-settings
 * @access  Private (Instructor, Admin)
 */
export const deleteSandboxSettings = asyncHandler(async (req, res) => {
  const { projectId } = req.params;

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(projectId)) {
    return res.status(400).json({
      error: 'Invalid project ID format'
    });
  }

  // Check if project exists
  const project = await Project.findByPk(projectId);
  if (!project) {
    return res.status(404).json({
      error: 'Project not found'
    });
  }

  // Find and deactivate settings
  const settings = await ProjectSandboxSettings.findOne({
    where: { projectId, isActive: true }
  });

  if (!settings) {
    return res.status(404).json({
      error: 'Sandbox settings not found'
    });
  }

  // Deactivate settings (soft delete)
  await settings.update({
    isActive: false,
    updatedBy: req.user.id
  });

  logger.info(`Sandbox settings deleted for project ${projectId} by ${req.user.email}`);

  res.status(200).json({
    success: true,
    message: 'Sandbox settings deleted successfully'
  });
});

/**
 * @desc    Get all sandbox settings for a project (including inactive)
 * @route   GET /api/sandbox/projects/:projectId/sandbox-settings/history
 * @access  Private (Instructor, Admin)
 */
export const getSandboxSettingsHistory = asyncHandler(async (req, res) => {
  const { projectId } = req.params;

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(projectId)) {
    return res.status(400).json({
      error: 'Invalid project ID format'
    });
  }

  // Check if project exists
  const project = await Project.findByPk(projectId);
  if (!project) {
    return res.status(404).json({
      error: 'Project not found'
    });
  }

  // Get all settings for the project
  const settings = await ProjectSandboxSettings.findAll({
    where: { projectId },
    order: [['createdAt', 'DESC']],
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updater',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    settings
  });
});
