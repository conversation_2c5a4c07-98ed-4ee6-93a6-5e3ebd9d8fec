import { as<PERSON><PERSON><PERSON><PERSON> } from '../middlewares/errorHandler.middlewares.js';
import { ProjectAssignment, Project, User, Course } from '../models/associations.js';
import logger from '../config/logger.config.js';

/**
 * @desc    Create a new project assignment
 * @route   POST /api/project-assignments
 * @access  Private (Project Management permissions)
 */
export const createProjectAssignment = asyncHandler(async (req, res) => {
  try {
    const {
      project_id,
      user_id,
      role,
      assignment_type = 'primary',
      permissions = {},
      start_date,
      end_date,
      notes,
      metadata = {}
    } = req.body;

    const assigned_by = req.user.id;

    // Validate that the project exists
    const project = await Project.findByPk(project_id);
    if (!project) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Project not found'
      });
    }

    // Validate that the user exists
    const user = await User.findByPk(user_id);
    if (!user) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }

    // Check if assignment already exists
    const existingAssignment = await ProjectAssignment.findOne({
      where: { project_id, user_id, is_active: true }
    });

    if (existingAssignment) {
      return res.status(409).json({
        error: 'Conflict',
        message: 'User is already assigned to this project'
      });
    }

    // Create the assignment
    const assignment = await ProjectAssignment.create({
      project_id,
      user_id,
      role,
      assignment_type,
      permissions,
      assigned_at: new Date(),
      assigned_by,
      start_date: start_date ? new Date(start_date) : null,
      end_date: end_date ? new Date(end_date) : null,
      notes,
      metadata,
      is_active: true
    });

    // Fetch the created assignment with related data
    const createdAssignment = await ProjectAssignment.findByPk(assignment.id, {
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'title', 'description', 'status']
        },
        {
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'name', 'email', 'role']
        },
        {
          model: User,
          as: 'assignedBy',
          attributes: ['id', 'name', 'email']
        }
      ]
    });

    logger.info(`Project assignment created: ${role} role for user ${user_id} on project ${project_id} by ${assigned_by}`);

    res.status(201).json({
      success: true,
      message: 'Project assignment created successfully',
      data: createdAssignment
    });

  } catch (error) {
    logger.error('Error creating project assignment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create project assignment'
    });
  }
});

/**
 * @desc    Get all project assignments with filtering
 * @route   GET /api/project-assignments
 * @access  Private (Project Management permissions)
 */
export const getProjectAssignments = asyncHandler(async (req, res) => {
  try {
    const {
      project_id,
      user_id,
      role,
      assignment_type,
      is_active,
      page = 1,
      limit = 20,
      sort_by = 'assigned_at',
      sort_order = 'DESC'
    } = req.query;

    // Build where clause
    const whereClause = {};
    if (project_id) whereClause.project_id = project_id;
    if (user_id) whereClause.user_id = user_id;
    if (role) whereClause.role = role;
    if (assignment_type) whereClause.assignment_type = assignment_type;
    if (is_active !== undefined) whereClause.is_active = is_active === 'true';

    // Pagination
    const offset = (page - 1) * limit;

    // Get assignments with pagination
    const { count, rows: assignments } = await ProjectAssignment.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'title', 'description', 'status', 'course_id']
        },
        {
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'name', 'email', 'role']
        },
        {
          model: User,
          as: 'assignedBy',
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [[sort_by, sort_order.toUpperCase()]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      message: 'Project assignments retrieved successfully',
      data: {
        assignments,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });

  } catch (error) {
    logger.error('Error getting project assignments:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve project assignments'
    });
  }
});

/**
 * @desc    Get a specific project assignment
 * @route   GET /api/project-assignments/:id
 * @access  Private (Project Management permissions)
 */
export const getProjectAssignment = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;

    const assignment = await ProjectAssignment.findByPk(id, {
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'title', 'description', 'status', 'course_id']
        },
        {
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'name', 'email', 'role']
        },
        {
          model: User,
          as: 'assignedBy',
          attributes: ['id', 'name', 'email']
        }
      ]
    });

    if (!assignment) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Project assignment not found'
      });
    }

    res.json({
      success: true,
      message: 'Project assignment retrieved successfully',
      data: assignment
    });

  } catch (error) {
    logger.error('Error getting project assignment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve project assignment'
    });
  }
});

/**
 * @desc    Update a project assignment
 * @route   PUT /api/project-assignments/:id
 * @access  Private (Project Management permissions)
 */
export const updateProjectAssignment = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const {
      role,
      assignment_type,
      permissions,
      start_date,
      end_date,
      is_active,
      notes,
      metadata
    } = req.body;

    const assignment = await ProjectAssignment.findByPk(id);
    if (!assignment) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Project assignment not found'
      });
    }

    // Check permissions (only creator or project owner can update)
    const project = await Project.findByPk(assignment.project_id);
    if (assignment.assigned_by !== req.user.id && project.created_by !== req.user.id) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Permission denied: Only creator or project owner can update assignment'
      });
    }

    // Update the assignment
    const updateData = {};
    if (role !== undefined) updateData.role = role;
    if (assignment_type !== undefined) updateData.assignment_type = assignment_type;
    if (permissions !== undefined) updateData.permissions = permissions;
    if (start_date !== undefined) updateData.start_date = start_date ? new Date(start_date) : null;
    if (end_date !== undefined) updateData.end_date = end_date ? new Date(end_date) : null;
    if (is_active !== undefined) updateData.is_active = is_active;
    if (notes !== undefined) updateData.notes = notes;
    if (metadata !== undefined) updateData.metadata = metadata;

    await assignment.update(updateData);

    // Fetch updated assignment with related data
    const updatedAssignment = await ProjectAssignment.findByPk(id, {
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'title', 'description', 'status']
        },
        {
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'name', 'email', 'role']
        },
        {
          model: User,
          as: 'assignedBy',
          attributes: ['id', 'name', 'email']
        }
      ]
    });

    logger.info(`Project assignment updated: ${id} by user ${req.user.id}`);

    res.json({
      success: true,
      message: 'Project assignment updated successfully',
      data: updatedAssignment
    });

  } catch (error) {
    logger.error('Error updating project assignment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update project assignment'
    });
  }
});

/**
 * @desc    Delete a project assignment
 * @route   DELETE /api/project-assignments/:id
 * @access  Private (Project Management permissions)
 */
export const deleteProjectAssignment = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;

    const assignment = await ProjectAssignment.findByPk(id);
    if (!assignment) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Project assignment not found'
      });
    }

    // Check permissions (only creator or project owner can delete)
    const project = await Project.findByPk(assignment.project_id);
    if (assignment.assigned_by !== req.user.id && project.created_by !== req.user.id) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Permission denied: Only creator or project owner can delete assignment'
      });
    }

    // Soft delete by setting is_active to false
    await assignment.update({ is_active: false });

    logger.info(`Project assignment deleted: ${id} by user ${req.user.id}`);

    res.json({
      success: true,
      message: 'Project assignment deleted successfully'
    });

  } catch (error) {
    logger.error('Error deleting project assignment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete project assignment'
    });
  }
});

/**
 * @desc    Get assignments for a specific user
 * @route   GET /api/project-assignments/user/:userId
 * @access  Private (Project Management permissions)
 */
export const getUserAssignments = asyncHandler(async (req, res) => {
  try {
    const { userId } = req.params;
    const { role, is_active, page = 1, limit = 20 } = req.query;

    // Build where clause
    const whereClause = { user_id: userId };
    if (role) whereClause.role = role;
    if (is_active !== undefined) whereClause.is_active = is_active === 'true';

    // Pagination
    const offset = (page - 1) * limit;

    const { count, rows: assignments } = await ProjectAssignment.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'title', 'description', 'status', 'course_id', 'due_date']
        },
        {
          model: User,
          as: 'assignedBy',
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [['assigned_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      message: 'User assignments retrieved successfully',
      data: {
        assignments,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });

  } catch (error) {
    logger.error('Error getting user assignments:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve user assignments'
    });
  }
});

/**
 * @desc    Bulk create project assignments
 * @route   POST /api/project-assignments/bulk
 * @access  Private (Project Management permissions)
 */
export const bulkCreateAssignments = asyncHandler(async (req, res) => {
  try {
    const { assignments } = req.body;
    const assigned_by = req.user.id;

    if (!Array.isArray(assignments) || assignments.length === 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Assignments array is required and cannot be empty'
      });
    }

    // Validate all assignments
    for (const assignment of assignments) {
      if (!assignment.project_id || !assignment.user_id || !assignment.role) {
        return res.status(400).json({
          error: 'Bad Request',
          message: 'Each assignment must have project_id, user_id, and role'
        });
      }
    }

    const results = [];
    const errors = [];

    // Process each assignment
    for (const assignmentData of assignments) {
      try {
        // Check if assignment already exists
        const existingAssignment = await ProjectAssignment.findOne({
          where: { 
            project_id: assignmentData.project_id, 
            user_id: assignmentData.user_id, 
            is_active: true 
          }
        });

        if (existingAssignment) {
          errors.push({
            project_id: assignmentData.project_id,
            user_id: assignmentData.user_id,
            error: 'Assignment already exists'
          });
          continue;
        }

        // Create the assignment
        const assignment = await ProjectAssignment.create({
          ...assignmentData,
          assigned_at: new Date(),
          assigned_by,
          is_active: true
        });

        results.push(assignment);
      } catch (error) {
        errors.push({
          project_id: assignmentData.project_id,
          user_id: assignmentData.user_id,
          error: error.message
        });
      }
    }

    logger.info(`Bulk assignments created: ${results.length} successful, ${errors.length} failed by user ${assigned_by}`);

    res.status(201).json({
      success: true,
      message: 'Bulk assignments processed',
      data: {
        successful: results.length,
        failed: errors.length,
        results,
        errors
      }
    });

  } catch (error) {
    logger.error('Error creating bulk assignments:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create bulk assignments'
    });
  }
});

/**
 * @desc    Get assignment history for a project
 * @route   GET /api/project-assignments/project/:projectId/history
 * @access  Private (Project Management permissions)
 */
export const getProjectAssignmentHistory = asyncHandler(async (req, res) => {
  try {
    const { projectId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    // Pagination
    const offset = (page - 1) * limit;

    const { count, rows: assignments } = await ProjectAssignment.findAndCountAll({
      where: { project_id: projectId },
      include: [
        {
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'name', 'email', 'role']
        },
        {
          model: User,
          as: 'assignedBy',
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [['assigned_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      message: 'Project assignment history retrieved successfully',
      data: {
        assignments,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });

  } catch (error) {
    logger.error('Error getting project assignment history:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve project assignment history'
    });
  }
});

/**
 * @desc    Get assignment statistics
 * @route   GET /api/project-assignments/stats
 * @access  Private (Project Management permissions)
 */
export const getAssignmentStats = asyncHandler(async (req, res) => {
  try {
    const { project_id, user_id, role } = req.query;

    // Build where clause
    const whereClause = { is_active: true };
    if (project_id) whereClause.project_id = project_id;
    if (user_id) whereClause.user_id = user_id;
    if (role) whereClause.role = role;

    // Get total assignments
    const totalAssignments = await ProjectAssignment.count({ where: whereClause });

    // Get assignments by role
    const assignmentsByRole = await ProjectAssignment.findAll({
      where: whereClause,
      attributes: [
        'role',
        [ProjectAssignment.sequelize.fn('COUNT', ProjectAssignment.sequelize.col('id')), 'count']
      ],
      group: ['role']
    });

    // Get assignments by type
    const assignmentsByType = await ProjectAssignment.findAll({
      where: whereClause,
      attributes: [
        'assignment_type',
        [ProjectAssignment.sequelize.fn('COUNT', ProjectAssignment.sequelize.col('id')), 'count']
      ],
      group: ['assignment_type']
    });

    // Get recent assignments (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentAssignments = await ProjectAssignment.count({
      where: {
        ...whereClause,
        assigned_at: { [ProjectAssignment.sequelize.Op.gte]: thirtyDaysAgo }
      }
    });

    res.json({
      success: true,
      message: 'Assignment statistics retrieved successfully',
      data: {
        totalAssignments,
        assignmentsByRole: assignmentsByRole.reduce((acc, item) => {
          acc[item.role] = parseInt(item.dataValues.count);
          return acc;
        }, {}),
        assignmentsByType: assignmentsByType.reduce((acc, item) => {
          acc[item.assignment_type] = parseInt(item.dataValues.count);
          return acc;
        }, {}),
        recentAssignments,
        period: '30 days'
      }
    });

  } catch (error) {
    logger.error('Error getting assignment statistics:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve assignment statistics'
    });
  }
});
