import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import permissionFeedbackService from '../services/permissionFeedback.service.js';
import logger from '../config/logger.config.js';

/**
 * @desc    Get user's permission summary
 * @route   GET /api/permissions/summary
 * @access  Private
 */
export const getPermissionSummary = asyncHandler(async (req, res) => {
  try {
    const summary = await permissionFeedbackService.getPermissionSummary(req.user.id);
    
    res.json({
      success: true,
      message: 'Permission summary retrieved successfully',
      data: summary
    });
  } catch (error) {
    logger.error('Error getting permission summary:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve permission summary'
    });
  }
});

/**
 * @desc    Get user's permissions for specific resource
 * @route   GET /api/permissions/resource/:resourceType/:resourceId?
 * @access  Private
 */
export const getResourcePermissions = asyncHandler(async (req, res) => {
  const { resourceType, resourceId } = req.params;

  try {
    const permissions = await permissionFeedbackService.getUserResourcePermissions(
      req.user.id,
      resourceType,
      resourceId || null
    );
    
    res.json({
      success: true,
      message: 'Resource permissions retrieved successfully',
      data: permissions
    });
  } catch (error) {
    logger.error('Error getting resource permissions:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve resource permissions'
    });
  }
});

/**
 * @desc    Get available actions for user
 * @route   GET /api/permissions/actions/:resourceType/:resourceId?
 * @access  Private
 */
export const getAvailableActions = asyncHandler(async (req, res) => {
  const { resourceType, resourceId } = req.params;

  try {
    const actions = await permissionFeedbackService.getAvailableActions(
      req.user.id,
      resourceType,
      resourceId || null
    );
    
    res.json({
      success: true,
      message: 'Available actions retrieved successfully',
      data: actions
    });
  } catch (error) {
    logger.error('Error getting available actions:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve available actions'
    });
  }
});

/**
 * @desc    Check if user can perform specific action
 * @route   POST /api/permissions/check-action
 * @access  Private
 */
export const checkActionPermission = asyncHandler(async (req, res) => {
  const { action, resourceType, resourceId } = req.body;

  if (!action || !resourceType) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Action and resourceType are required'
    });
  }

  try {
    const result = await permissionFeedbackService.canPerformAction(
      req.user.id,
      action,
      resourceType,
      resourceId || null
    );
    
    res.json({
      success: true,
      message: 'Action permission checked successfully',
      data: result
    });
  } catch (error) {
    logger.error('Error checking action permission:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to check action permission'
    });
  }
});

/**
 * @desc    Get permission requirements for specific action
 * @route   GET /api/permissions/requirements/:action
 * @access  Private
 */
export const getPermissionRequirements = asyncHandler(async (req, res) => {
  const { action } = req.params;

  try {
    const requirements = permissionFeedbackService.getPermissionRequirements(action);
    
    res.json({
      success: true,
      message: 'Permission requirements retrieved successfully',
      data: {
        action,
        requirements,
        totalRequirements: requirements.length
      }
    });
  } catch (error) {
    logger.error('Error getting permission requirements:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve permission requirements'
    });
  }
});

/**
 * @desc    Get user's detailed permissions
 * @route   GET /api/permissions/detailed
 * @access  Private
 */
export const getDetailedPermissions = asyncHandler(async (req, res) => {
  try {
    const permissions = await permissionFeedbackService.getUserPermissions(req.user.id);
    
    res.json({
      success: true,
      message: 'Detailed permissions retrieved successfully',
      data: permissions
    });
  } catch (error) {
    logger.error('Error getting detailed permissions:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve detailed permissions'
    });
  }
});

/**
 * @desc    Get UI context for permissions
 * @route   GET /api/permissions/ui-context/:resourceType/:resourceId?
 * @access  Private
 */
export const getUIContext = asyncHandler(async (req, res) => {
  const { resourceType, resourceId } = req.params;

  try {
    // Get permissions and actions
    const permissions = await permissionFeedbackService.getUserResourcePermissions(
      req.user.id,
      resourceType,
      resourceId || null
    );
    
    const actions = await permissionFeedbackService.getAvailableActions(
      req.user.id,
      resourceType,
      resourceId || null
    );

    // Build UI context
    const uiContext = {
      resourceType,
      resourceId,
      accessLevel: permissions.accessLevel,
      hasAccess: permissions.hasAccess,
      availableActions: actions.actions,
      permissions: permissions.permissions,
      contextPermissions: permissions.contextPermissions,
      uiElements: {
        showCreateButton: actions.actions.some(a => a.action === 'create_project'),
        showEditButton: actions.actions.some(a => a.action === 'edit_project'),
        showDeleteButton: actions.actions.some(a => a.action === 'delete_project'),
        showPublishButton: actions.actions.some(a => a.action === 'publish_project'),
        showAssignButton: actions.actions.some(a => a.action === 'assign_users'),
        showTemplateButton: actions.actions.some(a => a.action === 'create_template')
      },
      featureFlags: {
        canCreate: permissions.accessLevel === 'full' || permissions.accessLevel === 'manage',
        canEdit: permissions.accessLevel === 'full' || permissions.accessLevel === 'manage',
        canDelete: permissions.accessLevel === 'full',
        canPublish: permissions.accessLevel === 'full' || permissions.accessLevel === 'manage',
        canAssign: permissions.accessLevel === 'full' || permissions.accessLevel === 'manage',
        canManageTemplates: permissions.accessLevel === 'full' || permissions.accessLevel === 'manage'
      }
    };
    
    res.json({
      success: true,
      message: 'UI context retrieved successfully',
      data: uiContext
    });
  } catch (error) {
    logger.error('Error getting UI context:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve UI context'
    });
  }
});
