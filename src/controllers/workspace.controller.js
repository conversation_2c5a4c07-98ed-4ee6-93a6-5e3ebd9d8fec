import { Project, UserWorkspace } from '../models/associations.js';
import { asyncHand<PERSON> } from '../middlewares/errorHandler.middlewares.js';
import s3WorkspaceService from '../services/s3Workspace.service.js';
import logger from '../config/logger.config.js';

/**
 * @desc    Fork exercise template to user workspace
 * @route   POST /api/sandbox/workspace/:projectId/fork
 * @access  Private
 */
export const forkExercise = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const userId = req.user.id;

  // Check if project exists
  const project = await Project.findByPk(projectId);
  if (!project) {
    return res.status(404).json({
      error: 'Project not found'
    });
  }

  // Check if workspace already exists
  const existingWorkspace = await UserWorkspace.findOne({
    where: {
      studentId: userId,
      projectId: projectId
    }
  });

  if (existingWorkspace) {
    return res.status(409).json({
      error: 'Workspace already exists for this project'
    });
  }

  try {
    // Fork template to user workspace
    const forkResult = await s3WorkspaceService.forkTemplateToUser(projectId, userId);

    // Create workspace record
    const workspace = await UserWorkspace.create({
      studentId: userId,
      projectId: projectId,
      s3Prefix: forkResult.destinationPrefix,
      forkVersion: new Date().toISOString(),
      isReady: false,
      status: 'creating'
    });

    logger.info(`Workspace forked for user ${userId} and project ${projectId}`);

    res.status(201).json({
      success: true,
      message: 'Workspace forked successfully',
      workspace: {
        id: workspace.id,
        projectId: workspace.projectId,
        s3Prefix: workspace.s3Prefix,
        status: workspace.status,
        isReady: workspace.isReady,
        forkVersion: workspace.forkVersion
      }
    });
  } catch (error) {
    logger.error(`Failed to fork workspace: ${error.message}`);
    res.status(500).json({
      error: 'Failed to fork workspace',
      message: error.message
    });
  }
});

/**
 * @desc    Get workspace status
 * @route   GET /api/sandbox/workspace/:projectId/status
 * @access  Private
 */
export const getWorkspaceStatus = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const userId = req.user.id;

  // Get workspace
  const workspace = await UserWorkspace.findOne({
    where: {
      studentId: userId,
      projectId: projectId
    },
    include: [
      {
        model: Project,
        as: 'project',
        attributes: ['id', 'title', 'description']
      }
    ]
  });

  if (!workspace) {
    return res.status(404).json({
      error: 'Workspace not found'
    });
  }

  try {
    // Get workspace status from S3
    const workspaceExists = await s3WorkspaceService.workspaceExists(workspace.s3Prefix);
    const workspaceSize = await s3WorkspaceService.getWorkspaceSize(workspace.s3Prefix);

    // Update workspace if needed
    if (workspaceExists && workspace.status === 'creating') {
      await workspace.update({
        status: 'ready',
        isReady: true,
        totalSize: workspaceSize.size || 0,
        fileCount: workspaceSize.fileCount || 0
      });
    }

    res.json({
      success: true,
      workspace: {
        id: workspace.id,
        projectId: workspace.projectId,
        project: workspace.project,
        status: workspace.status,
        isReady: workspace.isReady,
        fileCount: workspace.fileCount,
        totalSize: workspace.totalSize,
        lastAccessed: workspace.lastAccessed,
        forkVersion: workspace.forkVersion
      }
    });
  } catch (error) {
    logger.error(`Failed to get workspace status: ${error.message}`);
    res.status(500).json({
      error: 'Failed to get workspace status',
      message: error.message
    });
  }
});

/**
 * @desc    List workspace files
 * @route   GET /api/sandbox/workspace/:projectId/files
 * @access  Private
 */
export const listWorkspaceFiles = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const userId = req.user.id;

  // Get workspace
  const workspace = await UserWorkspace.findOne({
    where: {
      studentId: userId,
      projectId: projectId
    }
  });

  if (!workspace) {
    return res.status(404).json({
      error: 'Workspace not found'
    });
  }

  try {
    // List files from S3
    const files = await s3WorkspaceService.listWorkspaceFiles(workspace.s3Prefix);

    res.json({
      success: true,
      files: files.files || [],
      totalFiles: files.totalFiles || 0
    });
  } catch (error) {
    logger.error(`Failed to list workspace files: ${error.message}`);
    res.status(500).json({
      error: 'Failed to list workspace files',
      message: error.message
    });
  }
});

/**
 * @desc    Upload file to workspace
 * @route   POST /api/sandbox/workspace/:projectId/upload
 * @access  Private
 */
export const uploadWorkspaceFile = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const userId = req.user.id;
  const { filePath, fileContent, options = {} } = req.body;

  if (!filePath || !fileContent) {
    return res.status(400).json({
      error: 'File path and content are required'
    });
  }

  // Get workspace
  const workspace = await UserWorkspace.findOne({
    where: {
      studentId: userId,
      projectId: projectId
    }
  });

  if (!workspace) {
    return res.status(404).json({
      error: 'Workspace not found'
    });
  }

  try {
    // Upload file to S3
    const result = await s3WorkspaceService.uploadFile(workspace.s3Prefix, filePath, fileContent, options);

    // Update workspace metadata
    await workspace.update({
      lastAccessed: new Date(),
      fileCount: workspace.fileCount + 1,
      totalSize: workspace.totalSize + (fileContent.length || 0)
    });

    res.json({
      success: true,
      message: 'File uploaded successfully',
      file: {
        path: filePath,
        size: fileContent.length,
        uploadedAt: new Date()
      }
    });
  } catch (error) {
    logger.error(`Failed to upload file: ${error.message}`);
    res.status(500).json({
      error: 'Failed to upload file',
      message: error.message
    });
  }
});

/**
 * @desc    Delete file from workspace
 * @route   DELETE /api/sandbox/workspace/:projectId/files/:fileName
 * @access  Private
 */
export const deleteWorkspaceFile = asyncHandler(async (req, res) => {
  const { projectId, fileName } = req.params;
  const userId = req.user.id;

  // Get workspace
  const workspace = await UserWorkspace.findOne({
    where: {
      studentId: userId,
      projectId: projectId
    }
  });

  if (!workspace) {
    return res.status(404).json({
      error: 'Workspace not found'
    });
  }

  try {
    // Delete file from S3
    await s3WorkspaceService.deleteWorkspaceFile(workspace.s3Prefix, fileName);

    // Update workspace metadata
    await workspace.update({
      lastAccessed: new Date(),
      fileCount: Math.max(0, workspace.fileCount - 1)
    });

    res.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    logger.error(`Failed to delete file: ${error.message}`);
    res.status(500).json({
      error: 'Failed to delete file',
      message: error.message
    });
  }
});

/**
 * @desc    Get user workspaces
 * @route   GET /api/sandbox/workspaces
 * @access  Private
 */
export const getUserWorkspaces = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  try {
    // Get user workspaces
    const workspaces = await UserWorkspace.findAll({
      where: {
        studentId: userId
      },
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'title', 'description', 'courseId']
        }
      ],
      order: [['lastAccessed', 'DESC']]
    });

    res.json({
      success: true,
      workspaces: workspaces.map(workspace => ({
        id: workspace.id,
        projectId: workspace.projectId,
        project: workspace.project,
        status: workspace.status,
        isReady: workspace.isReady,
        fileCount: workspace.fileCount,
        totalSize: workspace.totalSize,
        lastAccessed: workspace.lastAccessed,
        forkVersion: workspace.forkVersion
      }))
    });
  } catch (error) {
    logger.error(`Failed to get user workspaces: ${error.message}`);
    res.status(500).json({
      error: 'Failed to get user workspaces',
      message: error.message
    });
  }
});

export default {
  forkExercise,
  getWorkspaceStatus,
  listWorkspaceFiles,
  uploadWorkspaceFile,
  deleteWorkspaceFile,
  getUserWorkspaces
};
