import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import { Rubric, Project, User, Course } from '../models/associations.js';
import logger from '../config/logger.config.js';
import rubricService from '../services/rubric.service.js';
import { LoggerInfo, response } from '../utils/helpers.utils.js';
import httpStatus from 'http-status';

/**
 * @desc    Create a new rubric
 * @route   POST /api/rubrics
 * @access  Private (Project Management permissions)
 */
export const createRubric = asyncHandler(async (req, res) => {
    const result = await rubricService.crationOfRubric(req);
    const responseData = await response(httpStatus.OK, result, 'Rubric created successfully');
    LoggerInfo(req, responseData.response.message, 'createRubric', responseData.response);
    res.status(responseData.status).send(responseData.response);
}, { component: 'createRubric', auditComponent: 'Create Rubric' });

/**
 * @desc    Get all rubrics with filtering
 * @route   GET /api/rubrics
 * @access  Private (Project Management permissions)
 */
export const getRubrics = asyncHandler(async (req, res) => {
    const result = await rubricService.getRubrics(req);
    const responseData = await response(httpStatus.OK, result, 'Rubrics retrieved successfully');
    LoggerInfo(req, responseData.response.message, 'getRubrics', responseData.response);
    res.status(responseData.status).send(responseData.response);
}, { component: 'getRubrics', auditComponent: 'Get Rubrics' });

/**
 * @desc    Get a specific rubric
 * @route   GET /api/rubrics/:id
 * @access  Private (Project Management permissions)
 */
export const getRubricById = asyncHandler(async (req, res) => {
    const result = await rubricService.getRubricById(req);
    const responseData = await response(httpStatus.OK, result, 'Rubric retrieved successfully');
    LoggerInfo(req, responseData.response.message, 'getRubricById', responseData.response);
    res.status(responseData.status).send(responseData.response);
}, { component: 'getRubricById', auditComponent: 'Get Rubric By ID' });

/**
 * @desc    Update a rubric
 * @route   PUT /api/rubrics/:id
 * @access  Private (Project Management permissions)
 */
export const updateRubric = asyncHandler(async (req, res) => {
    const result = await rubricService.updateRubric(req);
    const responseData = await response(httpStatus.OK, result, 'Rubric updated successfully');
    LoggerInfo(req, responseData.response.message, 'updateRubric', responseData.response);
    res.status(responseData.status).send(responseData.response);
}, { component: 'updateRubric', auditComponent: 'Update Rubric' });

/**
 * @desc    Delete a rubric
 * @route   DELETE /api/rubrics/:id
 * @access  Private (Project Management permissions)
 */
export const deleteRubric = asyncHandler(async (req, res) => {
    const result = await rubricService.deleteRubric(req);
    const responseData = await response(httpStatus.OK, result, 'Rubric deleted successfully');
    LoggerInfo(req, responseData.response.message, 'deleteRubric', responseData.response);
    res.status(responseData.status).send(responseData.response);
}, { component: 'deleteRubric', auditComponent: 'Delete Rubric' });

/**
 * @desc    Get rubric templates
 * @route   GET /api/rubrics/templates
 * @access  Private (Project Management permissions)
 */
export const getRubricTemplates = asyncHandler(async (req, res) => {
  try {
    const {
      template_name,
      category,
      page = 1,
      limit = 20
    } = req.query;

    // Build where clause for templates
    const whereClause = { is_template: true };
    if (template_name) whereClause.template_name = { [Rubric.sequelize.Op.iLike]: `%${template_name}%` };

    // Pagination
    const offset = (page - 1) * limit;

    const { count, rows: templates } = await Rubric.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      message: 'Rubric templates retrieved successfully',
      data: {
        templates,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });

  } catch (error) {
    logger.error('Error getting rubric templates:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve rubric templates'
    });
  }
});

/**
 * @desc    Duplicate a rubric template for a project
 * @route   POST /api/rubrics/:id/duplicate
 * @access  Private (Project Management permissions)
 */
export const duplicateRubricTemplate = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const { project_id, title, description } = req.body;

    const templateRubric = await Rubric.findByPk(id);
    if (!templateRubric) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Rubric template not found'
      });
    }

    if (!templateRubric.is_template) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Can only duplicate rubric templates'
      });
    }

    // Validate project if provided
    if (project_id) {
      const project = await Project.findByPk(project_id);
      if (!project) {
        return res.status(404).json({
          error: 'Not Found',
          message: 'Project not found'
        });
      }
    }

    // Create new rubric based on template
    const newRubric = await Rubric.create({
      project_id,
      title: title || `${templateRubric.title} (Copy)`,
      description: description || templateRubric.description,
      criteria: templateRubric.criteria,
      total_points: templateRubric.total_points,
      grading_scale: templateRubric.grading_scale,
      is_template: false,
      template_name: null,
      checkpoint_mapping: templateRubric.checkpoint_mapping,
      created_by: req.user.id,
      metadata: {
        ...templateRubric.metadata,
        duplicated_from: templateRubric.id,
        duplicated_at: new Date()
      }
    });

    // Fetch the created rubric with related data
    const createdRubric = await Rubric.findByPk(newRubric.id, {
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'title', 'description', 'status']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'email']
        }
      ]
    });

    logger.info(`Rubric template duplicated: ${templateRubric.id} -> ${newRubric.id} by user ${req.user.id}`);

    res.status(201).json({
      success: true,
      message: 'Rubric template duplicated successfully',
      data: createdRubric
    });

  } catch (error) {
    logger.error('Error duplicating rubric template:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to duplicate rubric template'
    });
  }
});

/**
 * @desc    Get rubrics for a specific project
 * @route   GET /api/rubrics/project/:projectId
 * @access  Private (Project Management permissions)
 */
export const getProjectRubrics = asyncHandler(async (req, res) => {
  try {
    const { projectId } = req.params;

    const project = await Project.findByPk(projectId);
    if (!project) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Project not found'
      });
    }

    const rubrics = await Rubric.findAll({
      where: { project_id: projectId },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      message: 'Project rubrics retrieved successfully',
      data: rubrics
    });

  } catch (error) {
    logger.error('Error getting project rubrics:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve project rubrics'
    });
  }
});

/**
 * @desc    Get rubric statistics
 * @route   GET /api/rubrics/stats
 * @access  Private (Project Management permissions)
 */
export const getRubricStats = asyncHandler(async (req, res) => {
  try {
    const { created_by, is_template } = req.query;

    // Build where clause
    const whereClause = {};
    if (created_by) whereClause.created_by = created_by;
    if (is_template !== undefined) whereClause.is_template = is_template === 'true';

    // Get total rubrics
    const totalRubrics = await Rubric.count({ where: whereClause });

    // Get rubrics by type
    const rubricsByType = await Rubric.findAll({
      where: whereClause,
      attributes: [
        'is_template',
        [Rubric.sequelize.fn('COUNT', Rubric.sequelize.col('id')), 'count']
      ],
      group: ['is_template']
    });

    // Get recent rubrics (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentRubrics = await Rubric.count({
      where: {
        ...whereClause,
        created_at: { [Rubric.sequelize.Op.gte]: thirtyDaysAgo }
      }
    });

    // Get average criteria count
    const avgCriteriaCount = await Rubric.findOne({
      where: whereClause,
      attributes: [
        [Rubric.sequelize.fn('AVG', Rubric.sequelize.fn('JSONB_ARRAY_LENGTH', Rubric.sequelize.col('criteria'))), 'avg_criteria']
      ]
    });

    res.json({
      success: true,
      message: 'Rubric statistics retrieved successfully',
      data: {
        totalRubrics,
        rubricsByType: rubricsByType.reduce((acc, item) => {
          acc[item.is_template ? 'templates' : 'project_specific'] = parseInt(item.dataValues.count);
          return acc;
        }, {}),
        recentRubrics,
        period: '30 days',
        averageCriteriaCount: avgCriteriaCount ? parseFloat(avgCriteriaCount.dataValues.avg_criteria) || 0 : 0
      }
    });

  } catch (error) {
    logger.error('Error getting rubric statistics:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve rubric statistics'
    });
  }
});
