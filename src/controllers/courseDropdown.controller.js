import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import courseRoleService from '../services/courseRole.service.js';
import logger from '../config/logger.config.js';
import { response } from '../utils/helpers.utils.js';
import { LoggerInfo } from '../utils/helpers.utils.js';
import httpStatus from 'http-status';

/**
 * @desc    Get courses for project creation dropdown (instructor/TA only)
 * @route   GET /api/courses/project-creation
 * @access  Private (Instructor/TA)
 */
export const getProjectCreationCourses = asyncHandler(async (req, res) => {
  const courses = await courseRoleService.getProjectCreationCourses(req.user.id);
  const responseData = await response(httpStatus.OK, courses, 'Project creation courses retrieved successfully');
  LoggerInfo(req, responseData.response.message, 'CourseDropdownController', responseData.response);
  res.status(responseData.status).json(responseData.response);
}, { component: 'getProjectCreationCourses', auditComponent: 'Get Project Creation Courses' });

/**
 * @desc    Get course dropdown for specific user role
 * @route   GET /api/courses/dropdown/:role
 * @access  Private
 */
export const getCourseDropdownByRole = asyncHandler(async (req, res) => {
  const { role } = req.params;

  if (!['instructor', 'ta', 'student'].includes(role)) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Invalid role. Must be instructor, ta, or student'
    });
  }

  try {
    let courses = [];

    if (role === 'instructor') {
      // Get courses where user is primary instructor
      courses = await courseRoleService.getPrimaryInstructorCourses(req.user.id);
    } else if (role === 'ta') {
      // Get courses where user has TA role
      const taCourses = await courseRoleService.getTeachingCourses(req.user.id);
      courses = taCourses.filter(course => course.userRole === 'ta');
    } else {
      // For students, get enrolled courses (basic implementation)
      // This could be enhanced based on your student course access requirements
      courses = [];
    }

    res.json({
      success: true,
      message: `Courses for ${role} role retrieved successfully`,
      data: {
        courses,
        total: courses.length,
        role
      }
    });
  } catch (error) {
    logger.error(`Error getting course dropdown for role ${role}:`, error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: `Failed to retrieve courses for ${role} role`
    });
  }
});

/**
 * @desc    Validate user's role in a specific course
 * @route   GET /api/courses/:courseId/user-role
 * @access  Private
 */
export const getUserCourseRole = asyncHandler(async (req, res) => {
  const { courseId } = req.params;
  const userId = req.user.id;

  try {
    const role = await courseRoleService.getUserCourseRole(userId, courseId);

    if (!role) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User is not enrolled in this course'
      });
    }

    res.json({
      success: true,
      message: 'User course role retrieved successfully',
      data: {
        courseId,
        userId,
        role: role.role,
        status: role.status,
        enrolledAt: role.enrolledAt,
        user: role.user
      }
    });
  } catch (error) {
    logger.error('Error getting user course role:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve user course role'
    });
  }
});

/**
 * @desc    Check if user can create projects in a specific course
 * @route   GET /api/courses/:courseId/can-create-projects
 * @access  Private
 */
export const canCreateProjectsInCourse = asyncHandler(async (req, res) => {
  const { courseId } = req.params;
  const userId = req.user.id;

  try {
    const permission = await courseRoleService.canCreateProjects(userId, courseId);

    res.json({
      success: true,
      message: 'Project creation permission checked successfully',
      data: {
        courseId,
        userId,
        canCreate: permission.canCreate,
        reason: permission.reason,
        userRole: permission.userRole
      }
    });
  } catch (error) {
    logger.error('Error checking project creation permission:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to check project creation permission'
    });
  }
});

/**
 * @desc    Get teaching staff for a specific course
 * @route   GET /api/courses/:courseId/teaching-staff
 * @access  Private (Course participants)
 */
export const getCourseTeachingStaff = asyncHandler(async (req, res) => {
  const { courseId } = req.params;
  const teachingStaff = await courseRoleService.getCourseTeachingStaff(courseId);
  const responseData = await response(httpStatus.OK, teachingStaff, 'Course teaching staff retrieved successfully');
  LoggerInfo(req, responseData.response.message, 'CourseDropdownController', responseData.response);
  res.status(responseData.status).json(responseData.response);
}, { component: 'getCourseTeachingStaff', auditComponent: 'Get Course Teaching Staff' });

/**
 * @desc    Get course summary for dropdown display
 * @route   GET /api/courses/:courseId/summary
 * @access  Private
 */
export const getCourseSummary = asyncHandler(async (req, res) => {
  const { courseId } = req.params;
  const userId = req.user.id;

  try {
    const { Course, Project, CourseEnrollment } = await import('../models/associations.js');

    const course = await Course.findByPk(courseId, {
      include: [
        {
          model: Project,
          as: 'projects',
          attributes: ['id', 'title', 'status'],
          where: { status: { [require('sequelize').Op.ne]: 'archived' } },
          required: false
        },
        {
          model: CourseEnrollment,
          as: 'enrollments',
          where: { enrollment_status: 'active' },
          required: false
        }
      ],
      attributes: ['id', 'name', 'code', 'term', 'academic_year', 'start_date', 'end_date', 'status']
    });

    if (!course) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Course not found'
      });
    }

    // Get user's role in this course
    const userRole = await courseRoleService.getUserCourseRole(userId, courseId);

    const courseSummary = {
      id: course.id,
      name: course.name,
      code: course.code,
      term: course.term,
      academicYear: course.academic_year,
      startDate: course.start_date,
      endDate: course.end_date,
      status: course.status,
      projectCount: course.projects?.length || 0,
      enrollmentCount: course.enrollments?.length || 0,
      userRole: userRole?.role || null,
      canCreateProjects: userRole ? ['instructor', 'ta'].includes(userRole.role) : false
    };

    res.json({
      success: true,
      message: 'Course summary retrieved successfully',
      data: courseSummary
    });
  } catch (error) {
    logger.error('Error getting course summary:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve course summary'
    });
  }
});
