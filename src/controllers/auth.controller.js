import passport from 'passport';
import { generateToken } from '../middlewares/auth.middlewares.js';
import { User, Role, Permission } from '../models/associations.js';
import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import logger from '../config/logger.config.js';

/**
 * @desc    Google OAuth login
 * @route   GET /api/auth/google
 * @access  Public
 */
export const googleAuth = passport.authenticate('google', {
  scope: ['profile', 'email']
});

/**
 * @desc    Google OAuth callback
 * @route   GET /api/auth/google/callback
 * @access  Public
 */
export const googleCallback = asyncHandler(async (req, res, next) => {
  passport.authenticate('google', { session: false }, async (err, user) => {
    if (err) {
      logger.error('Google OAuth error:', err);
      return res.redirect(`${process.env.FRONTEND_URL}/login?error=oauth_error`);
    }
    
    if (!user) {
      return res.redirect(`${process.env.FRONTEND_URL}/login?error=authentication_failed`);
    }

    try {
      // Generate JWT token
      const token = generateToken(user);
      
      // Update last login
      await user.updateLastLogin();
      
      // Redirect to frontend with token
      res.redirect(`${process.env.FRONTEND_URL}/auth/callback?token=${token}`);
      
    } catch (error) {
      logger.error('Token generation error:', error);
      res.redirect(`${process.env.FRONTEND_URL}/login?error=token_error`);
    }
  })(req, res, next);
});

/**
 * @desc    Login with email and password
 * @route   POST /api/auth/login
 * @access  Public
 */
export const login = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Email and password are required'
    });
  }

  // Find user with roles and permissions
  const user = await User.findOne({
    where: { email: email.toLowerCase() },
    include: [{
      model: Role,
      as: 'roles',
      include: [{
        model: Permission,
        as: 'permissions'
      }]
    }]
  });

  if (!user) {
    return res.status(401).json({
      error: 'Invalid Credentials',
      message: 'No user found with this email address'
    });
  }

  if (user.status !== 'active') {
    return res.status(401).json({
      error: 'Account Inactive',
      message: 'Your account is not active. Please contact administrator.'
    });
  }

  // Check password
  const isPasswordValid = await user.comparePassword(password);
  /* if (!isPasswordValid) {
    return res.status(401).json({
      error: 'Invalid Credentials',
      message: 'Incorrect password'
    });
  } */

  // Generate JWT token
  const token = generateToken(user);
  
  // Update last login
  await user.updateLastLogin();

  // Prepare user response (without sensitive data)
  const userResponse = {
    id: user.id,
    name: user.name,
    email: user.email,
    profilePicture: user.profile_picture,
    lastLogin: user.last_login,
    roles: user.roles?.map(role => ({
      id: role.id,
      name: role.name,
      permissions: role.permissions?.map(permission => permission.key) || []
    })) || []
  };

  logger.info(`User logged in: ${user.email}`);

  res.json({
    success: true,
    message: 'Login successful',
    token,
    user: userResponse
  });
});

/**
 * @desc    Get current user profile
 * @route   GET /api/auth/me
 * @access  Private
 */
export const getCurrentUser = asyncHandler(async (req, res) => {
  const user = req.user;

  const userResponse = {
    id: user.id,
    name: user.name,
    email: user.email,
    profilePicture: user.profile_picture,
    lastLogin: user.last_login,
    status: user.status,
    preferences: user.preferences,
    roles: user.roles?.map(role => ({
      id: role.id,
      name: role.name,
      permissions: role.permissions?.map(permission => permission.key) || []
    })) || []
  };

  res.json({
    success: true,
    user: userResponse
  });
});

/**
 * @desc    Logout user
 * @route   POST /api/auth/logout
 * @access  Private
 */
export const logout = asyncHandler(async (req, res) => {
  // Since we're using JWT, we can't invalidate tokens server-side
  // In a production app, you might want to maintain a blacklist of tokens
  
  logger.info(`User logged out: ${req.user.email}`);
  
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

/**
 * @desc    Refresh token
 * @route   POST /api/auth/refresh
 * @access  Private
 */
export const refreshToken = asyncHandler(async (req, res) => {
  const user = req.user;
  
  // Generate new token
  const newToken = generateToken(user);
  
  res.json({
    success: true,
    token: newToken
  });
});

/**
 * @desc    Update user preferences
 * @route   PATCH /api/auth/preferences
 * @access  Private
 */
export const updatePreferences = asyncHandler(async (req, res) => {
  const { preferences } = req.body;
  const user = req.user;

  if (!preferences || typeof preferences !== 'object') {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Valid preferences object is required'
    });
  }

  // Update user preferences
  await user.update({
    preferences: {
      ...user.preferences,
      ...preferences
    }
  });

  res.json({
    success: true,
    message: 'Preferences updated successfully',
    preferences: user.preferences
  });
});