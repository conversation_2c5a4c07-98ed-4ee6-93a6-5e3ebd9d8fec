import { asyncHand<PERSON> } from '../middlewares/errorHandler.middlewares.js';
import messageService from '../services/message.service.js';
import logger from '../config/logger.config.js';

/**
 * @desc    Send a new message
 * @route   POST /api/messages
 * @access  Private
 */
export const sendMessage = asyncHandler(async (req, res) => {
  const {
    recipientId,
    subject,
    content,
    messageType = 'personal',
    priority = 'normal',
    courseId,
    projectId,
    attachments = []
  } = req.body;

  // Validate required fields
  if (!recipientId || !subject || !content) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Recipient ID, subject, and content are required'
    });
  }

  try {
    const messageData = {
      sender_id: req.user.id,
      recipient_id: recipientId,
      subject,
      content,
      message_type: messageType,
      priority,
      course_id: courseId,
      project_id: projectId,
      attachments
    };

    const message = await messageService.sendMessage(messageData);
    
    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: {
        id: message.id,
        subject: message.subject,
        recipientId: message.recipient_id,
        sentAt: message.created_at
      }
    });
  } catch (error) {
    logger.error('Error sending message:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send message'
    });
  }
});

/**
 * @desc    Reply to a message
 * @route   POST /api/messages/:id/reply
 * @access  Private
 */
export const replyToMessage = asyncHandler(async (req, res) => {
  const { id: parentMessageId } = req.params;
  const { subject, content, attachments = [] } = req.body;

  // Validate required fields
  if (!subject || !content) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Subject and content are required'
    });
  }

  try {
    const replyData = {
      subject,
      content,
      attachments
    };

    const reply = await messageService.replyToMessage(parentMessageId, replyData, req.user.id);
    
    res.status(201).json({
      success: true,
      message: 'Reply sent successfully',
      data: {
        id: reply.id,
        subject: reply.subject,
        parentMessageId: reply.parent_message_id,
        sentAt: reply.created_at
      }
    });
  } catch (error) {
    if (error.message === 'Parent message not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Parent message not found'
      });
    }
    
    logger.error('Error sending reply:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send reply'
    });
  }
});

/**
 * @desc    Get user's inbox
 * @route   GET /api/messages/inbox
 * @access  Private
 */
export const getInbox = asyncHandler(async (req, res) => {
  const {
    status = 'all',
    messageType,
    priority,
    isRead,
    page = 1,
    limit = 20,
    courseId,
    projectId
  } = req.query;

  try {
    const result = await messageService.getInbox(req.user.id, {
      status,
      messageType,
      priority,
      isRead: isRead === 'true',
      page: parseInt(page),
      limit: parseInt(limit),
      courseId,
      projectId
    });
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error getting inbox:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve inbox'
    });
  }
});

/**
 * @desc    Get user's sent messages
 * @route   GET /api/messages/sent
 * @access  Private
 */
export const getSentMessages = asyncHandler(async (req, res) => {
  const {
    messageType,
    priority,
    page = 1,
    limit = 20,
    courseId,
    projectId
  } = req.query;

  try {
    const result = await messageService.getSentMessages(req.user.id, {
      messageType,
      priority,
      page: parseInt(page),
      limit: parseInt(limit),
      courseId,
      projectId
    });
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error getting sent messages:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve sent messages'
    });
  }
});

/**
 * @desc    Get conversation thread
 * @route   GET /api/messages/thread/:threadId
 * @access  Private
 */
export const getConversationThread = asyncHandler(async (req, res) => {
  const { threadId } = req.params;

  try {
    const messages = await messageService.getConversationThread(threadId, req.user.id);
    
    res.json({
      success: true,
      messages
    });
  } catch (error) {
    if (error.message === 'Access denied: You are not part of this conversation') {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }
    
    logger.error('Error getting conversation thread:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve conversation thread'
    });
  }
});

/**
 * @desc    Mark message as read
 * @route   POST /api/messages/:id/read
 * @access  Private
 */
export const markAsRead = asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const message = await messageService.markAsRead(id, req.user.id);
    
    res.json({
      success: true,
      message: 'Message marked as read',
      data: {
        id: message.id,
        isRead: message.is_read,
        readAt: message.read_at
      }
    });
  } catch (error) {
    if (error.message === 'Message not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Message not found'
      });
    }
    
    if (error.message.includes('Access denied')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }
    
    logger.error('Error marking message as read:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to mark message as read'
    });
  }
});

/**
 * @desc    Mark multiple messages as read
 * @route   POST /api/messages/mark-read
 * @access  Private
 */
export const markMultipleAsRead = asyncHandler(async (req, res) => {
  const { messageIds } = req.body;

  if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Message IDs array is required'
    });
  }

  try {
    const result = await messageService.markMultipleAsRead(messageIds, req.user.id);
    
    res.json({
      success: true,
      message: `${result.count} messages marked as read`,
      data: result
    });
  } catch (error) {
    logger.error('Error marking multiple messages as read:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to mark messages as read'
    });
  }
});

/**
 * @desc    Get message by ID
 * @route   GET /api/messages/:id
 * @access  Private
 */
export const getMessageById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const message = await messageService.getMessageById(id, req.user.id);
    
    res.json({
      success: true,
      data: message
    });
  } catch (error) {
    if (error.message === 'Message not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Message not found'
      });
    }
    
    if (error.message.includes('Access denied')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }
    
    logger.error('Error getting message by ID:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve message'
    });
  }
});

/**
 * @desc    Update message
 * @route   PUT /api/messages/:id
 * @access  Private (Sender only)
 */
export const updateMessage = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  try {
    const message = await messageService.updateMessage(id, updateData, req.user.id);
    
    res.json({
      success: true,
      message: 'Message updated successfully',
      data: message
    });
  } catch (error) {
    if (error.message === 'Message not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Message not found'
      });
    }
    
    if (error.message.includes('Access denied')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }
    
    logger.error('Error updating message:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update message'
    });
  }
});

/**
 * @desc    Delete message
 * @route   DELETE /api/messages/:id
 * @access  Private (Sender only)
 */
export const deleteMessage = asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const result = await messageService.deleteMessage(id, req.user.id);
    
    res.json({
      success: true,
      message: result.message
    });
  } catch (error) {
    if (error.message === 'Message not found') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Message not found'
      });
    }
    
    if (error.message.includes('Access denied')) {
      return res.status(403).json({
        error: 'Access Denied',
        message: error.message
      });
    }
    
    logger.error('Error deleting message:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete message'
    });
  }
});

/**
 * @desc    Get unread message count
 * @route   GET /api/messages/unread-count
 * @access  Private
 */
export const getUnreadCount = asyncHandler(async (req, res) => {
  try {
    const count = await messageService.getUnreadCount(req.user.id);
    
    res.json({
      success: true,
      data: { unreadCount: count }
    });
  } catch (error) {
    logger.error('Error getting unread count:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get unread count'
    });
  }
});

/**
 * @desc    Get instructor message statistics
 * @route   GET /api/messages/instructor/stats
 * @access  Private (Instructor/Admin)
 */
export const getInstructorMessageStats = asyncHandler(async (req, res) => {
  try {
    const stats = await messageService.getInstructorMessageStats(req.user.id);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Error getting instructor message stats:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get message statistics'
    });
  }
});

/**
 * @desc    Send course-wide announcement message
 * @route   POST /api/messages/course-announcement
 * @access  Private (Instructor/Admin)
 */
export const sendCourseAnnouncement = asyncHandler(async (req, res) => {
  const {
    courseId,
    subject,
    content,
    priority = 'normal',
    attachments = []
  } = req.body;

  // Validate required fields
  if (!courseId || !subject || !content) {
    return res.status(400).json({
      error: 'Validation Error',
      message: 'Course ID, subject, and content are required'
    });
  }

  try {
    const messageData = {
      subject,
      content,
      priority,
      attachments
    };

    const result = await messageService.sendCourseAnnouncement(courseId, req.user.id, messageData);
    
    res.status(201).json({
      success: true,
      message: `Course announcement sent to ${result.count} students`,
      data: result
    });
  } catch (error) {
    if (error.message === 'No students enrolled in this course') {
      return res.status(400).json({
        error: 'Bad Request',
        message: error.message
      });
    }
    
    logger.error('Error sending course announcement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to send course announcement'
    });
  }
});
