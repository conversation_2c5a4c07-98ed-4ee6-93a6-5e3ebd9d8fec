import { createProxyMiddleware } from 'http-proxy-middleware';
import config from '../config/database.config.js';
import logger from '../config/logger.config.js';
import jupyterService from '../services/jupyterhub.service.js';
import NodeCache from 'node-cache';

const tokenCache = new NodeCache({ stdTTL: 5 * 60 });

/**
 * Express middleware to ensure a valid JupyterHub user token is attached to the request.
 * It uses an in-memory cache to avoid regenerating tokens on every request.
 * If the token is expired or not in the cache, it regenerates a new one.
 */
export const ensureJupyterToken = async (req, res, next) => {
  const username = req.user?.jupiterUserName;

  if (!username) {
    return res.status(401).send('User identifier not found in request.');
  }

  try {
    // Attempt to retrieve the token from the cache.
    // If the token is older than the stdTTL, this will return `undefined`.
    let userToken = tokenCache.get(username);

    // If the token is not in the cache (i.e., it's the first request or it has expired),
    // we must generate a new one.
    if (!userToken) {
      logger.info(
        `Token for '${username}' not in cache or expired. Regenerating...`
      );
      const tokenData = await jupyterService.getUserToken(username);
      userToken = tokenData.token;

      // Store the newly generated token in the cache, resetting the TTL.
      tokenCache.set(username, userToken);
      logger.info(
        `Successfully generated and cached new token for '${username}'.`
      );
    } else {
      logger.info(`Using cached token for '${username}'.`);
    }

    console.log('User token acquired:', userToken);

    // Attach the valid token to the request object for the proxy middleware to use.
    req.jupyterToken = userToken;

    // Pass control to the next middleware in the chain (the proxy).
    next();
  } catch (err) {
    logger.error(`Fatal error during token generation for '${username}':`, err);
    res
      .status(503)
      .send('Failed to acquire authentication token for Jupyter service.');
  }
};

export const jupyterProxy = createProxyMiddleware({
  target: config.jupyterhub.url,
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying
  followRedirects: true,

  pathRewrite: (path, req) => {
    if (!req.user || !req.user.jupiterUserName) {
      // This error should ideally be caught by an error handler
      throw new Error('User not found on request object for proxying.');
    }
    const username = req.user.jupiterUserName;

    // The 'path' variable is everything AFTER '/api/jupyter' (e.g., '/api/contents').
    // We need to prepend the user's specific path prefix to it.
    const newPath = `/user/${username}/${path}`;

    logger.info(`Rewriting path for '${username}': '${path}' --> '${newPath}'`);

    return newPath;
  },

  onProxyReq: (proxyReq, req) => {
    const username = req.user?.jupiterUserName;
    const userToken = req.user?.jupyterToken;
    logger.info(
      `--> PROXYING request for user: ${username} to ${config.jupyterhub.url}`
    );
    // Use admin token for now to avoid 404 token creation issues
    proxyReq.setHeader('Authorization', `token ${userToken}`);
  },
  onProxyRes: () => {
    // no-op for now
  },
  onProxyReqWs: (proxyReq, _req, _socket, _options, _head) => {
    const userToken = _req.user?.jupyterToken;
    // Use admin token for WebSocket authentication
    proxyReq.setHeader('Authorization', `token ${userToken}`);
  },
  onError: (err, _req, res) => {
    logger.error('PROXY ERROR:', err);
    if (!res.headersSent) {
      res.status(504).send('Proxy Gateway Timeout.');
    }
  }
});
