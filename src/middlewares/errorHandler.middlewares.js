import logger from '../config/logger.config.js';
import { getErrMsgApiRes } from '../utils/helpers.utils.js';

export const errorHandler = async (error, req, res, next) => {
  // Log the error
  logger.error('API Error:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    user: req.user?.email || 'anonymous'
  });

  // Default error response
  let statusCode = 500;
  let data = {
    isSuccess: false,
    message: 'Internal server error'
  };
  let details = null;

  // Handle different types of errors
  if (error.name === 'ValidationError') {
    // Sequelize validation errors
    statusCode = 400;
    data.message = 'Validation error';
    details = error.errors?.map(err => ({
      field: err.path,
      message: err.message,
      value: err.value
    }));
  } else if (error.name === 'SequelizeUniqueConstraintError') {
    // Unique constraint violations
    statusCode = 409;
    data.message = 'Resource already exists';
    details = error.errors?.map(err => ({
      field: err.path,
      message: `${err.path} must be unique`,
      value: err.value
    }));
  } else if (error.name === 'SequelizeForeignKeyConstraintError') {
    // Foreign key constraint violations
    statusCode = 400;
    data.message = 'Invalid reference to related resource';
  } else if (error.name === 'SequelizeConnectionError') {
    // Database connection errors
    statusCode = 503;
    data.message = 'Service temporarily unavailable';
    details = 'Database connection error';
  } else if (error.name === 'JsonWebTokenError') {
    // JWT errors
    statusCode = 401;
    data.message = 'Invalid authentication token';
  } else if (error.name === 'TokenExpiredError') {
    // JWT expiration
    statusCode = 401;
    data.message = 'Authentication token expired';
  } else if (error.name === 'MulterError') {
    // File upload errors
    statusCode = 400;
    if (error.code === 'LIMIT_FILE_SIZE') {
      data.message = 'File size too large';
    } else if (error.code === 'LIMIT_FILE_COUNT') {
      data.message = 'Too many files';
    } else {
      data.message = 'File upload error';
    }
  } else if (error.name === 'BadRequestError') {
    statusCode = error.statusCode || 400;
    data.message = error.message;
    details = error.details || null;
    // Get first error's field and data
    if (details && details.length > 0) {
      const firstError = details[0];
      data.message = ` ${firstError.field} - ${firstError.message}`;
    }
    details = null; // Avoid sending details in response
  } else if (error.statusCode || error.status) {
    const ErrorResponseData = await getErrMsgApiRes(
      req,
      error,
      error.meta.auditComponent,
      error.meta.component
    );
    // Custom errors with status codes
    statusCode = ErrorResponseData.status;
    data = ErrorResponseData.response;
    /* statusCode = error.statusCode || error.status;
    data.message = error.message; */
  } else if (error.message) {
    // Other errors with messages
    data.message = error.message;
  }

  // Don't leak sensitive information in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  const errorResponse = {
    data: data,
    status: statusCode,
    timestamp: new Date().toISOString(),
    path: req.url,
    method: req.method
  };

  // Add additional details in development or for specific error types
  if (isDevelopment || statusCode < 500) {
    if (details) {
      errorResponse.details = details;
    }

    if (isDevelopment && error.stack) {
      errorResponse.stack = error.stack;
    }
  }

  // Send error response
  res.status(statusCode).json(errorResponse);
};

// Custom error classes
export class ValidationError extends Error {
  constructor(message, details = null) {
    super(message);
    this.name = 'ValidationError';
    this.statusCode = 400;
    this.details = details;
  }
}

export class NotFoundError extends Error {
  constructor(message = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
    this.statusCode = 404;
  }
}

export class UnauthorizedError extends Error {
  constructor(message = 'Unauthorized access') {
    super(message);
    this.name = 'UnauthorizedError';
    this.statusCode = 401;
  }
}

export class ForbiddenError extends Error {
  constructor(message = 'Access forbidden') {
    super(message);
    this.name = 'ForbiddenError';
    this.statusCode = 403;
  }
}

export class ConflictError extends Error {
  constructor(message = 'Resource conflict') {
    super(message);
    this.name = 'ConflictError';
    this.statusCode = 409;
  }
}

export class BadRequestError extends Error {
  constructor(message = 'Bad request', details = null) {
    super(message);
    this.name = 'BadRequestError';
    this.statusCode = 400;
    this.details = details;
  }
}

// Express async error wrapper
/* export const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}; */

// utils/asyncHandler.js
export const asyncHandler =
  (fn, { component, auditComponent } = {}) =>
  (req, res, next) =>
    Promise.resolve(fn(req, res, next)).catch(err => {
      // attach context so the error handler can log rich details
      err.status =
        err.status || err.httpStatus || err.code || err.statusCode || 500;
      err.meta = {
        ...(err.meta || {}),
        component,
        auditComponent
      };
      next(err);
    });
