import { validationResult } from 'express-validator';
import { BadRequestError } from './errorHandler.middlewares.js';

/**
 * Middleware to handle validation errors from express-validator
 */
export const validate = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));
    
    throw new BadRequestError('Validation failed', errorMessages);
  }
  
  next();
};

/**
 * Common validation rules
 */
export const validationRules = {
  // User validation
  email: () => body('email').isEmail().normalizeEmail(),
  password: () => body('password').isLength({ min: 6 }),
  name: () => body('name').isLength({ min: 2, max: 100 }).trim(),
  
  // Course validation
  courseName: () => body('name').isLength({ min: 2, max: 200 }).trim(),
  courseCode: () => body('code').optional().isLength({ min: 2, max: 20 }).trim(),
  
  // Project validation
  projectTitle: () => body('title').isLength({ min: 2, max: 200 }).trim(),
  projectDescription: () => body('description').optional().isLength({ max: 5000 }).trim(),
  dueDate: () => body('dueDate').optional().isISO8601().toDate(),
  
  // Pagination validation
  page: () => query('page').optional().isInt({ min: 1 }).toInt(),
  limit: () => query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  
  // UUID validation
  uuid: (field) => param(field).isUUID(),
  
  // Status validation
  status: (allowedValues) => body('status').isIn(allowedValues),
  
  // File validation
  fileType: (allowedTypes) => body('fileType').isIn(allowedTypes),
  
  // Grade validation
  score: () => body('score').isFloat({ min: 0 }),
  percentage: () => body('percentage').isFloat({ min: 0, max: 100 })
};