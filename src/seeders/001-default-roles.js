'use strict';
const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const roles = [
      {
        id: uuidv4(),
        name: 'super_admin',
        description: 'Super Administrator with full system access',
        is_system_role: true,
        priority: 100,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'admin',
        description: 'Administrator with administrative privileges',
        lms_role_reference: 'admin',
        is_system_role: true,
        priority: 90,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'instructor',
        description: 'Course instructor with teaching privileges',
        lms_role_reference: 'instructor',
        is_system_role: true,
        priority: 70,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'ta',
        description: 'Teaching Assistant with limited instructor privileges',
        lms_role_reference: 'ta',
        is_system_role: true,
        priority: 60,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'student',
        description: 'Student with learning access',
        lms_role_reference: 'student',
        is_system_role: true,
        priority: 10,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('roles', roles);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('roles', {
      name: ['super_admin', 'admin', 'instructor', 'ta', 'student']
    });
  }
};