'use strict';

const crypto = require('crypto');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Generate RSA key pair for default platform
    const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });

    // Default D2L Brightspace platform configuration
    await queryInterface.bulkInsert('lti_platforms', [
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        platform_id: 'https://brightspace.bits.edu',
        platform_name: 'BITS Pilani Brightspace',
        client_id: 'bits-datascience-platform',
        auth_login_url: 'https://brightspace.bits.edu/d2l/lti/authenticate',
        auth_token_url: 'https://brightspace.bits.edu/d2l/lti/token',
        key_set_url: 'https://brightspace.bits.edu/d2l/lti/keys',
        private_key: privateKey,
        public_key: publicKey,
        key_id: 'bits-brightspace-key-1',
        is_active: true,
        settings: {
          description: 'Default D2L Brightspace platform for BITS Pilani',
          supportedScopes: [
            'openid',
            'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem',
            'https://purl.imsglobal.org/spec/lti-ags/scope/score',
            'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly'
          ],
          customProperties: {
            institution: 'BITS Pilani',
            environment: 'production',
            timezone: 'Asia/Kolkata'
          }
        },
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Default deployment for the platform
    await queryInterface.bulkInsert('lti_deployments', [
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        platform_id: '550e8400-e29b-41d4-a716-446655440001',
        deployment_id: 'bits-production-deployment',
        deployment_name: 'BITS Pilani Production Deployment',
        is_active: true,
        settings: {
          description: 'Main production deployment for BITS Pilani courses',
          features: [
            'grade_passback',
            'deep_linking',
            'member_provisioning'
          ]
        },
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('lti_deployments', {
      deployment_id: 'bits-production-deployment'
    });
    
    await queryInterface.bulkDelete('lti_platforms', {
      platform_id: 'https://brightspace.bits.edu'
    });
  }
};