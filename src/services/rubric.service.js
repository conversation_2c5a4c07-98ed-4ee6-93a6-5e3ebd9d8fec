import { Project, ProjectTemplate, ProjectAssignment, User, Course, Rubric } from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';
import enhancedProjectUtils from '../utils/enhancedProject.utils.js';
import httpStatus from 'http-status';
import ApiError from '../utils/ApiError.utils.js';
import { LoggerInfo } from '../utils/helpers.utils.js';

class rubricService {
    /**
   * Create a new rubric based project with assignments
   */
    async crationOfRubric(req) {
        try {
            const {
                project_id,
                title,
                description,
                criteria,
                total_points = 100,
                grading_scale,
                is_template = false,
                template_name,
                checkpoint_mapping = {},
                metadata = {}
            } = req.body;

            const created_by = req.user.id;

            // Validate that the project exists if not a template
            if (project_id && !is_template) {
                const project = await enhancedProjectUtils.checkProjectExist(project_id);
                if (!project) throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
            }

            // Validate criteria structure
            if (!Array.isArray(criteria) || criteria.length === 0) throw new ApiError(httpStatus.BAD_REQUEST, 'Criteria must be a non-empty array');

            // Validate each criterion
            for (const criterion of criteria) {
                if (!criterion.name || !criterion.description || !criterion.points) throw new ApiError(httpStatus.BAD_REQUEST, 'Each criterion must have name, description, and points');
                if (criterion.points <= 0) throw new ApiError(httpStatus.BAD_REQUEST, 'Criterion points must be positive');
            }

            // Calculate total points from criteria
            const calculatedTotalPoints = criteria.reduce((sum, criterion) => sum + criterion.points, 0);

            // Use calculated total if not provided or if it differs
            const finalTotalPoints = total_points || calculatedTotalPoints;

            if (Math.abs(calculatedTotalPoints - finalTotalPoints) > 0.01) {
                logger.warn(`Total points mismatch: provided ${total_points}, calculated ${calculatedTotalPoints}`);
            }

            // Create the rubric
            const rubric = await Rubric.create({
                project_id: is_template ? null : project_id,
                title,
                description,
                criteria,
                total_points: finalTotalPoints,
                grading_scale: grading_scale || {
                    'A': { min: 90, max: 100 },
                    'B': { min: 80, max: 89.99 },
                    'C': { min: 70, max: 79.99 },
                    'D': { min: 60, max: 69.99 },
                    'F': { min: 0, max: 59.99 }
                },
                is_template,
                template_name: is_template ? template_name : null,
                checkpoint_mapping,
                created_by,
                metadata
            });

            // Fetch the created rubric with related data
            const createdRubric = await Rubric.findByPk(rubric.id, {
                include: [
                    {
                        model: Project,
                        as: 'project',
                        attributes: ['id', 'title', 'description', 'status']
                    },
                    /* {
                        model: User,
                        as: 'creator',
                        attributes: ['id', 'name', 'email']
                    } */
                ]
            });
            LoggerInfo(req, `Rubric created: ${title} by user ${created_by} (${is_template ? 'template' : 'project-specific'})`, 'creationOfRubric');
            return createdRubric;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Get rubrics with filtering, pagination, and sorting
     */
    async getRubrics(req) {
        try {
            const {
                project_id,
                created_by,
                is_template,
                template_name,
                page = 1,
                limit = 20,
                sort_by = 'created_at',
                sort_order = 'DESC'
            } = req.query;

            // Build where clause
            const whereClause = {};
            if (project_id) whereClause.project_id = project_id;
            if (created_by) whereClause.created_by = created_by;
            if (is_template !== undefined) whereClause.is_template = is_template === 'true';
            if (template_name) whereClause.template_name = { [Rubric.sequelize.Op.iLike]: `%${template_name}%` };

            // Pagination
            const offset = (page - 1) * limit;

            // Get rubrics with pagination
            const { count, rows: rubrics } = await Rubric.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Project,
                        as: 'project',
                        attributes: ['id', 'title', 'description', 'status']
                    },
                    {
                        model: User,
                        as: 'creator',
                        attributes: ['id', 'name', 'email']
                    }
                ],
                order: [[sort_by, sort_order.toUpperCase()]],
                limit: parseInt(limit),
                offset: parseInt(offset)
            });

            const totalPages = Math.ceil(count / limit);

            if (rubrics.length === 0) {
                throw new ApiError(httpStatus.NOT_FOUND, 'No rubrics found matching the criteria');
            }

            return {
                rubrics,
                pagination: {
                    current_page: parseInt(page),
                    total_pages: totalPages,
                    total_items: count,
                    items_per_page: parseInt(limit)
                }
            }
        } catch (error) {
            throw error;
        }
    }

    /**
     * fetch rubric by ID
    */
    async getRubricById(req) {
        try {
            const { id } = req.params;

            const rubric = await Rubric.findByPk(id, {
                include: [
                    {
                        model: Project,
                        as: 'project',
                        attributes: ['id', 'title', 'description', 'status', 'course_id']
                    },
                    {
                        model: User,
                        as: 'creator',
                        attributes: ['id', 'name', 'email']
                    }
                ]
            });

            if (!rubric) throw new ApiError(httpStatus.NOT_FOUND, 'Rubric not found');

            return rubric;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Update a rubric by ID
     */
    async updateRubric(req) {
        try {
            const { id } = req.params;
            const {
                title,
                description,
                criteria,
                total_points,
                grading_scale,
                is_template,
                template_name,
                checkpoint_mapping,
                metadata
            } = req.body;

            const rubric = await Rubric.findByPk(id);
            if (!rubric) throw new ApiError(httpStatus.NOT_FOUND, 'Rubric not found');

            // Check permissions (only creator can update)
            if (rubric.created_by !== req.user.id) throw new ApiError(httpStatus.FORBIDDEN, 'Permission denied: Only creator can update rubric');


            // Validate criteria if provided
            if (criteria) {
                if (!Array.isArray(criteria) || criteria.length === 0) throw new ApiError(httpStatus.BAD_REQUEST, 'Criteria must be a non-empty array');

                for (const criterion of criteria) {
                    if (!criterion.name || !criterion.description || !criterion.points) throw new ApiError(httpStatus.BAD_REQUEST, 'Each criterion must have name, description, and points');
                    if (criterion.points <= 0) throw new ApiError(httpStatus.BAD_REQUEST, 'Criterion points must be positive');
                }
            }

            // Update the rubric
            const updateData = {};
            if (title !== undefined) updateData.title = title;
            if (description !== undefined) updateData.description = description;
            if (criteria !== undefined) updateData.criteria = criteria;
            if (total_points !== undefined) updateData.total_points = total_points;
            if (grading_scale !== undefined) updateData.grading_scale = grading_scale;
            if (is_template !== undefined) updateData.is_template = is_template;
            if (template_name !== undefined) updateData.template_name = is_template ? template_name : null;
            if (checkpoint_mapping !== undefined) updateData.checkpoint_mapping = checkpoint_mapping;
            if (metadata !== undefined) updateData.metadata = metadata;

            await rubric.update(updateData);

            // Fetch updated rubric with related data
            const updatedRubric = await Rubric.findByPk(id, {
                include: [
                    {
                        model: Project,
                        as: 'project',
                        attributes: ['id', 'title', 'description', 'status']
                    },
                    {
                        model: User,
                        as: 'creator',
                        attributes: ['id', 'name', 'email']
                    }
                ]
            });

            LoggerInfo(req, `Rubric updated: ${id} by user ${req.user.id}`, 'updateRubric');

            return updatedRubric;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Delete a rubric by ID
     */
    async deleteRubric(req) {
        try {
            const { id } = req.params;

            const rubric = await Rubric.findByPk(id);
            if (!rubric) throw new ApiError(httpStatus.NOT_FOUND, 'Rubric not found');

            // Check permissions (only creator can delete)
            if (rubric.created_by !== req.user.id) throw new ApiError(httpStatus.FORBIDDEN, 'Permission denied: Only creator can delete rubric');

            // Check if rubric is being used by any projects
            if (rubric.project_id) {
                const project = await enhancedProjectUtils.checkProjectExist(rubric.project_id);
                if (project) throw new ApiError(httpStatus.BAD_REQUEST, 'Cannot delete rubric that is currently assigned to a project');
            }

            await rubric.destroy();

            LoggerInfo(req, `Rubric deleted: ${id} by user ${req.user.id}`, 'deleteRubric');

            return true;
        } catch (error) {
            throw error;
        }
    }
}

export default new rubricService();
