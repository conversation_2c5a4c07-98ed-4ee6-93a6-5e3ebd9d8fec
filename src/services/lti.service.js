import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import {
  LtiPlatform,
  LtiDeployment,
  LtiContext,
  LtiResourceLink,
  LtiLineItem,
  LtiLaunchSession
} from '../models/ltiAssociations.models.js';
import { User, Course, Project } from '../models/associations.js';
import logger from '../config/logger.config.js';

class LtiService {
  constructor() {
    this.toolUrl = process.env.LTI_TOOL_URL || 'https://your-tool-domain.com';
    this.keyId = process.env.LTI_KEY_ID || 'bits-lti-key-1';
    this.privateKey = process.env.LTI_PRIVATE_KEY;
    this.publicKey = process.env.LTI_PUBLIC_KEY;
    
    // Generate keys if not provided
    if (!this.privateKey || !this.publicKey) {
      this.generateKeyPair();
    }
  }

  /**
   * Generate RSA key pair for JWT signing
   */
  generateKeyPair() {
    const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });

    this.privateKey = privateKey;
    this.publicKey = publicKey;

    logger.info('Generated new RSA key pair for LTI');
  }

  /**
   * Get JWKS (JSON Web Key Set) for the tool
   */
  getJWKS() {
    const keyObject = crypto.createPublicKey(this.publicKey);
    const keyDetails = keyObject.asymmetricKeyDetails;
    
    return {
      keys: [{
        kty: 'RSA',
        use: 'sig',
        kid: this.keyId,
        alg: 'RS256',
        n: keyDetails.mgf.toString('base64url'),
        e: keyDetails.publicExponent.toString('base64url')
      }]
    };
  }

  /**
   * Verify JWT token from platform
   */
  async verifyJWT(token, platform) {
    try {
      // Get platform's public keys
      const platformKeys = await this.getPlatformKeys(platform.keySetUrl);
      
      // Verify and decode token
      const decoded = jwt.verify(token, platformKeys, {
        algorithms: ['RS256'],
        issuer: platform.platformId,
        audience: platform.clientId
      });

      return decoded;
    } catch (error) {
      logger.error('JWT verification failed:', error);
      throw new Error('Invalid JWT token');
    }
  }

  /**
   * Get platform's public keys from JWKS endpoint
   */
  async getPlatformKeys(keySetUrl) {
    try {
      const response = await axios.get(keySetUrl);
      const jwks = response.data;
      
      // Convert JWKS to PEM format for verification
      // This is a simplified implementation - in production, use a proper JWKS library
      const key = jwks.keys[0]; // Assuming first key
      return this.jwkToPem(key);
    } catch (error) {
      logger.error('Failed to fetch platform keys:', error);
      throw new Error('Could not fetch platform keys');
    }
  }

  /**
   * Convert JWK to PEM format
   */
  jwkToPem(jwk) {
    // This is a simplified implementation
    // In production, use the node-jose or jwk-to-pem library
    const modulus = Buffer.from(jwk.n, 'base64url');
    const exponent = Buffer.from(jwk.e, 'base64url');
    
    // Create public key from modulus and exponent
    const keyObject = crypto.createPublicKey({
      key: {
        kty: 'RSA',
        n: modulus,
        e: exponent
      },
      format: 'jwk'
    });

    return keyObject.export({ format: 'pem', type: 'spki' });
  }

  /**
   * Generate authentication request for LTI launch
   */
  generateAuthRequest(platform, targetLinkUri, loginHint, ltiMessageHint) {
    const state = this.generateState();
    const nonce = this.generateNonce();
    
    const authParams = new URLSearchParams({
      response_type: 'id_token',
      response_mode: 'form_post',
      scope: 'openid',
      client_id: platform.clientId,
      redirect_uri: `${this.toolUrl}/lti/launch`,
      login_hint: loginHint,
      state: state,
      nonce: nonce,
      prompt: 'none'
    });

    if (ltiMessageHint) {
      authParams.append('lti_message_hint', ltiMessageHint);
    }

    // Store session data
    this.storeLaunchSession({
      platformId: platform.id,
      state,
      nonce,
      targetLinkUri,
      loginHint,
      ltiMessageHint
    });

    return `${platform.authLoginUrl}?${authParams.toString()}`;
  }

  /**
   * Store launch session data
   */
  async storeLaunchSession(sessionData) {
    const sessionId = uuidv4();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await LtiLaunchSession.create({
      platformId: sessionData.platformId,
      sessionId,
      state: sessionData.state,
      nonce: sessionData.nonce,
      launchData: {
        targetLinkUri: sessionData.targetLinkUri,
        loginHint: sessionData.loginHint,
        ltiMessageHint: sessionData.ltiMessageHint
      },
      expiresAt
    });

    return sessionId;
  }

  /**
   * Process LTI launch request
   */
  async processLaunch(idToken, state) {
    try {
      // Find launch session
      const session = await LtiLaunchSession.findOne({
        where: { state, isUsed: false },
        include: [{ model: LtiPlatform, as: 'platform' }]
      });

      if (!session || new Date() > session.expiresAt) {
        throw new Error('Invalid or expired launch session');
      }

      // Verify JWT
      const launchData = await this.verifyJWT(idToken, session.platform);
      
      // Validate nonce
      if (launchData.nonce !== session.nonce) {
        throw new Error('Nonce mismatch');
      }

      // Mark session as used
      await session.update({ 
        isUsed: true, 
        idToken,
        launchData: { ...session.launchData, ...launchData }
      });

      // Process launch data
      const processedData = await this.processLaunchData(launchData, session.platform);
      
      return {
        user: processedData.user,
        context: processedData.context,
        resourceLink: processedData.resourceLink,
        launchData: launchData
      };

    } catch (error) {
      logger.error('LTI launch processing failed:', error);
      throw error;
    }
  }

  /**
   * Process launch data and sync with local database
   */
  async processLaunchData(launchData, platform) {
    // Extract user information
    const user = await this.syncUser(launchData, platform);
    
    // Extract context information
    const context = await this.syncContext(launchData, platform);
    
    // Extract resource link information
    const resourceLink = await this.syncResourceLink(launchData, platform, context);
    
    return { user, context, resourceLink };
  }

  /**
   * Sync user from LTI launch data
   */
  async syncUser(launchData, platform) {
    const userData = {
      lmsUserId: launchData.sub,
      email: launchData.email,
      name: launchData.name || `${launchData.given_name} ${launchData.family_name}`,
      firstName: launchData.given_name,
      lastName: launchData.family_name,
      role: this.mapLtiRole(launchData['https://purl.imsglobal.org/spec/lti/claim/roles'])
    };

    const [user, created] = await User.findOrCreate({
      where: { lmsUserId: userData.lmsUserId },
      defaults: {
        name: userData.name,
        email: userData.email,
        lmsUserId: userData.lmsUserId,
        status: 'active',
        profileData: {
          firstName: userData.firstName,
          lastName: userData.lastName,
          ltiRoles: launchData['https://purl.imsglobal.org/spec/lti/claim/roles']
        }
      }
    });

    if (!created) {
      // Update existing user
      await user.update({
        name: userData.name,
        email: userData.email,
        profileData: {
          ...user.profileData,
          firstName: userData.firstName,
          lastName: userData.lastName,
          ltiRoles: launchData['https://purl.imsglobal.org/spec/lti/claim/roles']
        }
      });
    }

    return user;
  }

  /**
   * Sync context from LTI launch data
   */
  async syncContext(launchData, platform) {
    const contextClaim = launchData['https://purl.imsglobal.org/spec/lti/claim/context'];
    const deploymentId = launchData['https://purl.imsglobal.org/spec/lti/claim/deployment_id'];
    
    if (!contextClaim) {
      return null;
    }

    // Find or create deployment
    const [deployment] = await LtiDeployment.findOrCreate({
      where: { 
        platformId: platform.id,
        deploymentId: deploymentId
      },
      defaults: {
        platformId: platform.id,
        deploymentId: deploymentId,
        deploymentName: `Deployment ${deploymentId}`
      }
    });

    // Find or create context
    const [context] = await LtiContext.findOrCreate({
      where: {
        platformId: platform.id,
        contextId: contextClaim.id
      },
      defaults: {
        platformId: platform.id,
        deploymentId: deployment.id,
        contextId: contextClaim.id,
        contextType: contextClaim.type?.[0] || 'Course',
        contextTitle: contextClaim.title,
        contextLabel: contextClaim.label
      }
    });

    // Try to match with existing course
    if (!context.courseId && contextClaim.title) {
      const course = await Course.findOne({
        where: { name: contextClaim.title }
      });
      
      if (course) {
        await context.update({ courseId: course.id });
      }
    }

    return context;
  }

  /**
   * Sync resource link from LTI launch data
   */
  async syncResourceLink(launchData, platform, context) {
    const resourceLinkClaim = launchData['https://purl.imsglobal.org/spec/lti/claim/resource_link'];
    
    if (!resourceLinkClaim || !context) {
      return null;
    }

    const [resourceLink] = await LtiResourceLink.findOrCreate({
      where: {
        platformId: platform.id,
        resourceLinkId: resourceLinkClaim.id
      },
      defaults: {
        platformId: platform.id,
        contextId: context.id,
        resourceLinkId: resourceLinkClaim.id,
        resourceLinkTitle: resourceLinkClaim.title,
        resourceLinkDescription: resourceLinkClaim.description
      }
    });

    return resourceLink;
  }

  /**
   * Map LTI roles to internal roles
   */
  mapLtiRole(ltiRoles) {
    if (!ltiRoles || !Array.isArray(ltiRoles)) {
      return 'student';
    }

    for (const role of ltiRoles) {
      if (role.includes('Instructor') || role.includes('TeachingAssistant')) {
        return 'instructor';
      }
      if (role.includes('Administrator')) {
        return 'admin';
      }
    }

    return 'student';
  }

  /**
   * Create or update line item for grade passback
   */
  async createLineItem(resourceLink, project, scoreMaximum = 100) {
    const [lineItem] = await LtiLineItem.findOrCreate({
      where: {
        resourceLinkId: resourceLink.id,
        projectId: project.id
      },
      defaults: {
        platformId: resourceLink.platformId,
        contextId: resourceLink.contextId,
        resourceLinkId: resourceLink.id,
        projectId: project.id,
        lineItemId: uuidv4(),
        scoreMaximum: scoreMaximum,
        label: project.title,
        tag: 'score',
        resourceId: project.id
      }
    });

    return lineItem;
  }

  /**
   * Send grade to platform via AGS
   */
  async sendGrade(userId, lineItem, score, platform) {
    try {
      // Get access token for AGS
      const accessToken = await this.getAGSAccessToken(platform);
      
      // Prepare score data
      const scoreData = {
        userId: userId,
        scoreGiven: score,
        scoreMaximum: lineItem.scoreMaximum,
        activityProgress: 'Completed',
        gradingProgress: 'FullyGraded',
        timestamp: new Date().toISOString()
      };

      // Send to platform AGS endpoint
      const agsUrl = `${platform.platformId}/api/lti/ags/scores`;
      
      await axios.post(agsUrl, scoreData, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/vnd.ims.lis.v1.score+json'
        }
      });

      logger.info(`Grade sent to platform: ${score}/${lineItem.scoreMaximum} for user ${userId}`);
      
    } catch (error) {
      logger.error('Failed to send grade to platform:', error);
      throw error;
    }
  }

  /**
   * Get access token for AGS
   */
  async getAGSAccessToken(platform) {
    try {
      const clientAssertion = this.createClientAssertion(platform);
      
      const tokenRequest = {
        grant_type: 'client_credentials',
        client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
        client_assertion: clientAssertion,
        scope: 'https://purl.imsglobal.org/spec/lti-ags/scope/score'
      };

      const response = await axios.post(platform.authTokenUrl, tokenRequest, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      return response.data.access_token;
    } catch (error) {
      logger.error('Failed to get AGS access token:', error);
      throw error;
    }
  }

  /**
   * Create client assertion JWT for service authentication
   */
  createClientAssertion(platform) {
    const now = Math.floor(Date.now() / 1000);
    
    const payload = {
      iss: platform.clientId,
      sub: platform.clientId,
      aud: platform.authTokenUrl,
      exp: now + 300, // 5 minutes
      iat: now,
      jti: uuidv4()
    };

    return jwt.sign(payload, this.privateKey, {
      algorithm: 'RS256',
      keyid: this.keyId
    });
  }

  /**
   * Generate secure random state
   */
  generateState() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Generate secure random nonce
   */
  generateNonce() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Deep linking service - create content items
   */
  createDeepLinkingResponse(projects, deepLinkingClaim, platform) {
    const contentItems = projects.map(project => ({
      type: 'ltiResourceLink',
      title: project.title,
      text: project.description,
      url: `${this.toolUrl}/lti/launch?project=${project.id}`,
      custom: {
        project_id: project.id,
        project_type: project.difficultyLevel
      }
    }));

    const payload = {
      iss: platform.clientId,
      aud: platform.platformId,
      exp: Math.floor(Date.now() / 1000) + 600, // 10 minutes
      iat: Math.floor(Date.now() / 1000),
      nonce: this.generateNonce(),
      'https://purl.imsglobal.org/spec/lti-dl/claim/content_items': contentItems,
      'https://purl.imsglobal.org/spec/lti-dl/claim/data': deepLinkingClaim.data
    };

    return jwt.sign(payload, this.privateKey, {
      algorithm: 'RS256',
      keyid: this.keyId
    });
  }

  /**
   * Get line items for a context
   */
  async getLineItems(platform, contextId) {
    try {
      const accessToken = await this.getAGSAccessToken(platform);
      
      const response = await axios.get(`${platform.lineItemsUrl}?contextId=${contextId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/vnd.ims.lis.v2.lineitemcontainer+json'
        }
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to get line items:', error);
      throw error;
    }
  }

  /**
   * Get scores for a line item
   */
  async getScores(platform, lineItemId) {
    try {
      const accessToken = await this.getAGSAccessToken(platform);
      
      const response = await axios.get(`${platform.scoresUrl}?lineItemId=${lineItemId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/vnd.ims.lis.v2.scorecontainer+json'
        }
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to get scores:', error);
      throw error;
    }
  }
}

export default new LtiService();

// Individual function exports for testing
export const generateClientAssertion = async (config) => {
  const service = new LtiService();
  return service.createClientAssertion({
    clientId: config.clientId,
    authTokenUrl: config.tokenUrl,
    privateKey: config.privateKey
  });
};

export const getAccessToken = async (config) => {
  const service = new LtiService();
  return service.getAGSAccessToken({
    authTokenUrl: config.tokenUrl,
    clientId: config.clientId,
    clientAssertion: config.clientAssertion
  });
};

export const createLineItem = async (platform, lineItem) => {
  const service = new LtiService();
  return service.createLineItem(platform, lineItem);
};

export const getLineItems = async (platform, contextId) => {
  const service = new LtiService();
  return service.getLineItems(platform, contextId);
};

export const createScore = async (platform, lineItemId, score) => {
  const service = new LtiService();
  return service.sendGrade(score.userId, { id: lineItemId }, score.scoreGiven, platform);
};

export const getScores = async (platform, lineItemId) => {
  const service = new LtiService();
  return service.getScores(platform, lineItemId);
};

export const validateLtiToken = async (token, platform) => {
  const service = new LtiService();
  return service.verifyJWT(token, platform);
};

export const parseLtiRequest = (request) => {
  const body = request.body || {};
  
  return {
    messageType: body['https://purl.imsglobal.org/spec/lti/claim/message_type'],
    version: body['https://purl.imsglobal.org/spec/lti/claim/version'],
    resourceLinkId: body['https://purl.imsglobal.org/spec/lti/claim/resource_link']?.id,
    contextId: body['https://purl.imsglobal.org/spec/lti/claim/context']?.id,
    contextLabel: body['https://purl.imsglobal.org/spec/lti/claim/context']?.label,
    contextTitle: body['https://purl.imsglobal.org/spec/lti/claim/context']?.title,
    returnUrl: body['https://purl.imsglobal.org/spec/lti/claim/launch_presentation']?.return_url,
    roles: body['https://purl.imsglobal.org/spec/lti/claim/roles'],
    userId: body.sub,
    userName: body.name,
    userEmail: body.email
  };
};