import axios from 'axios';
import logger from '../config/logger.config.js';

// Configuration for JupyterHub API
const JUPYTERHUB_URL = process.env.JUPYTERHUB_URL || 'http://localhost:8000';
const JUPYTERHUB_TOKEN = process.env.JUPYTERHUB_TOKEN;
const API_BASE_URL = `${JUPYTERHUB_URL}/hub/api`;

/**
 * Create a new user in JupyterHub
 */
export const createUser = async (userData) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/users`, userData, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('User creation failed:', error);
    throw new Error('User creation failed');
  }
};

/**
 * Delete a user from JupyterHub
 */
export const deleteUser = async (username) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/users/${username}`, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('User deletion failed:', error);
    throw new Error('User deletion failed');
  }
};

/**
 * Get user information from JupyterHub
 */
export const getUser = async (username) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/users/${username}`, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('User retrieval failed:', error);
    throw new Error('User retrieval failed');
  }
};

/**
 * List all users
 */
export const listUsers = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/users`, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('User listing failed:', error);
    throw new Error('User listing failed');
  }
};

/**
 * Start a user's server
 */
export const startServer = async (username, serverName = '', serverOptions = {}) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/users/${username}/servers/${serverName}`, serverOptions, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Server start failed:', error);
    throw new Error('Server start failed');
  }
};

/**
 * Stop a user's server
 */
export const stopServer = async (username, serverName = '') => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/users/${username}/servers/${serverName}`, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Server stop failed:', error);
    throw new Error('Server stop failed');
  }
};

/**
 * Get server status
 */
export const getServerStatus = async (username, serverName = '') => {
  try {
    const response = await axios.get(`${API_BASE_URL}/users/${username}/servers/${serverName}`, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Server status retrieval failed:', error);
    throw new Error('Server status retrieval failed');
  }
};

/**
 * Get user's servers
 */
export const getUserServers = async (username) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/users/${username}/servers`, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('User servers retrieval failed:', error);
    throw new Error('User servers retrieval failed');
  }
};

/**
 * Add user to group
 */
export const addUserToGroup = async (username, groupName) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/groups/${groupName}/users`, {
      users: [username]
    }, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Add user to group failed:', error);
    throw new Error('Add user to group failed');
  }
};

/**
 * Remove user from group
 */
export const removeUserFromGroup = async (username, groupName) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/groups/${groupName}/users/${username}`, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Remove user from group failed:', error);
    throw new Error('Remove user from group failed');
  }
};

/**
 * Create a group
 */
export const createGroup = async (groupData) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/groups`, groupData, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Group creation failed:', error);
    throw new Error('Group creation failed');
  }
};

/**
 * Delete a group
 */
export const deleteGroup = async (groupName) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/groups/${groupName}`, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Group deletion failed:', error);
    throw new Error('Group deletion failed');
  }
};

/**
 * List all groups
 */
export const listGroups = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/groups`, {
      headers: {
        'Authorization': `token ${JUPYTERHUB_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Group listing failed:', error);
    throw new Error('Group listing failed');
  }
};
