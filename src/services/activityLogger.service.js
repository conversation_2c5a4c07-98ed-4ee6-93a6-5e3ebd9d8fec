import { Activity } from '../models/associations.js';
import logger from '../config/logger.config.js';

class ActivityLoggerService {
  
  /**
   * Log a generic activity
   */
  async logActivity(activityData) {
    try {
      const activity = await Activity.create({
        ...activityData,
        created_at: new Date()
      });
      
      logger.info(`Activity logged: ${activityData.activity_type} - ${activityData.description}`, {
        activityId: activity.id,
        userId: activityData.user_id,
        projectId: activityData.project_id,
        courseId: activityData.course_id
      });
      
      return activity;
    } catch (error) {
      logger.error('Error logging activity:', error);
      // Don't throw error to prevent breaking main functionality
      return null;
    }
  }

  /**
   * Log project-related activities
   */
  async logProjectCreated(projectId, courseId, userId, metadata = {}) {
    return await this.logActivity({
      user_id: userId,
      project_id: projectId,
      course_id: courseId,
      activity_type: 'project_created',
      description: 'Created new project',
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString()
      }
    });
  }

  async logProjectPublished(projectId, courseId, userId, metadata = {}) {
    return await this.logActivity({
      user_id: userId,
      project_id: projectId,
      course_id: courseId,
      activity_type: 'project_published',
      description: 'Published project',
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString()
      }
    });
  }

  async logProjectArchived(projectId, courseId, userId, metadata = {}) {
    return await this.logActivity({
      user_id: userId,
      project_id: projectId,
      course_id: courseId,
      activity_type: 'project_archived',
      description: 'Archived project',
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Log checkpoint-related activities
   */
  async logCheckpointCreated(checkpointId, projectId, courseId, userId, metadata = {}) {
    return await this.logActivity({
      user_id: userId,
      project_id: projectId,
      course_id: courseId,
      activity_type: 'checkpoint_created',
      description: 'Created new checkpoint',
      metadata: {
        ...metadata,
        checkpointId,
        timestamp: new Date().toISOString()
      }
    });
  }

  async logCheckpointPublished(checkpointId, projectId, courseId, userId, metadata = {}) {
    return await this.logActivity({
      user_id: userId,
      project_id: projectId,
      course_id: courseId,
      activity_type: 'checkpoint_published',
      description: 'Published checkpoint',
      metadata: {
        ...metadata,
        checkpointId,
        timestamp: new Date().toISOString()
      }
    });
  }

  async logCheckpointSubmitted(checkpointId, projectId, courseId, userId, metadata = {}) {
    return await this.logActivity({
      user_id: userId,
      project_id: projectId,
      course_id: courseId,
      activity_type: 'checkpoint_submitted',
      description: 'Submitted checkpoint for review',
      metadata: {
        ...metadata,
        checkpointId,
        timestamp: new Date().toISOString()
      }
    });
  }

  async logCheckpointGraded(checkpointId, projectId, courseId, evaluatorId, studentId, metadata = {}) {
    return await this.logActivity({
      user_id: evaluatorId,
      project_id: projectId,
      course_id: courseId,
      activity_type: 'checkpoint_graded',
      description: 'Graded checkpoint submission',
      metadata: {
        ...metadata,
        checkpointId,
        studentId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Log grading activities
   */
  async logGradeAssigned(submissionId, projectId, courseId, evaluatorId, studentId, metadata = {}) {
    return await this.logActivity({
      user_id: evaluatorId,
      project_id: projectId,
      course_id: courseId,
      activity_type: 'grade_assigned',
      description: 'Assigned grade to submission',
      metadata: {
        ...metadata,
        submissionId,
        studentId,
        timestamp: new Date().toISOString()
      }
    });
  }

  async logFeedbackGiven(submissionId, projectId, courseId, evaluatorId, studentId, metadata = {}) {
    return await this.logActivity({
      user_id: evaluatorId,
      project_id: projectId,
      course_id: courseId,
      activity_type: 'feedback_given',
      description: 'Provided feedback on submission',
      metadata: {
        ...metadata,
        submissionId,
        studentId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Log student-related activities
   */
  async logStudentEnrolled(courseId, studentId, instructorId, metadata = {}) {
    return await this.logActivity({
      user_id: instructorId,
      course_id: courseId,
      activity_type: 'student_enrolled',
      description: 'Student enrolled in course',
      metadata: {
        ...metadata,
        studentId,
        timestamp: new Date().toISOString()
      }
    });
  }

  async logStudentSubmitted(submissionId, projectId, courseId, studentId, metadata = {}) {
    return await this.logActivity({
      user_id: studentId,
      project_id: projectId,
      course_id: courseId,
      activity_type: 'student_submitted',
      description: 'Student submitted project',
      metadata: {
        ...metadata,
        submissionId,
        timestamp: new Date().toISOString()
      }
    });
  }

  async logStudentGraded(submissionId, projectId, courseId, studentId, metadata = {}) {
    return await this.logActivity({
      user_id: studentId,
      project_id: projectId,
      course_id: courseId,
      activity_type: 'student_graded',
      description: 'Student received grade',
      metadata: {
        ...metadata,
        submissionId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Log course-related activities
   */
  async logCourseCreated(courseId, instructorId, metadata = {}) {
    return await this.logActivity({
      user_id: instructorId,
      course_id: courseId,
      activity_type: 'course_created',
      description: 'Created new course',
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString()
      }
    });
  }

  async logCourseUpdated(courseId, instructorId, metadata = {}) {
    return await this.logActivity({
      user_id: instructorId,
      course_id: courseId,
      activity_type: 'course_updated',
      description: 'Updated course information',
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Log custom activities
   */
  async logCustomActivity(userId, projectId, courseId, activityType, description, metadata = {}) {
    return await this.logActivity({
      user_id: userId,
      project_id: projectId,
      course_id: courseId,
      activity_type: activityType,
      description,
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Get activity summary for a user
   */
  async getUserActivitySummary(userId, limit = 50) {
    try {
      const activities = await Activity.findAll({
        where: { user_id: userId },
        order: [['created_at', 'DESC']],
        limit,
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title']
          },
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'name', 'code']
          }
        ]
      });

      return activities.map(activity => ({
        id: activity.id,
        type: activity.activity_type,
        description: activity.description,
        project: activity.project,
        course: activity.course,
        timestamp: activity.created_at,
        metadata: activity.metadata
      }));
    } catch (error) {
      logger.error('Error getting user activity summary:', error);
      return [];
    }
  }

  /**
   * Get activity summary for a project
   */
  async getProjectActivitySummary(projectId, limit = 50) {
    try {
      const activities = await Activity.findAll({
        where: { project_id: projectId },
        order: [['created_at', 'DESC']],
        limit,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email']
          },
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'name', 'code']
          }
        ]
      });

      return activities.map(activity => ({
        id: activity.id,
        type: activity.activity_type,
        description: activity.description,
        user: activity.user,
        course: activity.course,
        timestamp: activity.created_at,
        metadata: activity.metadata
      }));
    } catch (error) {
      logger.error('Error getting project activity summary:', error);
      return [];
    }
  }

  /**
   * Clean up old activities (for maintenance)
   */
  async cleanupOldActivities(daysToKeep = 365) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const deletedCount = await Activity.destroy({
        where: {
          created_at: {
            [require('sequelize').Op.lt]: cutoffDate
          }
        }
      });

      logger.info(`Cleaned up ${deletedCount} old activities older than ${daysToKeep} days`);
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up old activities:', error);
      return 0;
    }
  }
}

export default new ActivityLoggerService();
