import { S3Client, PutO<PERSON>Command, GetO<PERSON><PERSON>ommand, DeleteObjectCommand, ListObjectsV2Command, CopyObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import logger from '../config/logger.config.js';

class S3WorkspaceService {
  constructor() {
    this.client = new S3Client({
      region: process.env.AWS_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      }
    });
    
    this.bucket = process.env.AWS_S3_BUCKET || 'bits-dataScience-platform';
    this.baseUrl = `https://${this.bucket}.s3.${process.env.AWS_REGION || 'us-east-1'}.amazonaws.com`;
  }

  /**
   * Create a new workspace for a user
   */
  async createWorkspace(userId, workspaceName, options = {}) {
    try {
      const { courseId = null, projectId = null, template = null } = options;
      
      const workspaceId = uuidv4();
      const workspacePath = this.generateWorkspacePath(userId, workspaceId, courseId, projectId);
      
      // Create workspace metadata
      const metadata = {
        workspaceId: workspaceId,
        userId: userId,
        workspaceName: workspaceName,
        courseId: courseId,
        projectId: projectId,
        createdAt: new Date().toISOString(),
        status: 'active',
        template: template
      };

      // Create workspace root directory
      const rootKey = `${workspacePath}/.workspace`;
      await this.client.send(new PutObjectCommand({
        Bucket: this.bucket,
        Key: rootKey,
        Body: JSON.stringify(metadata),
        ContentType: 'application/json',
        Metadata: {
          'workspace-id': workspaceId,
          'user-id': userId,
          'workspace-name': workspaceName
        }
      }));

      // If template is provided, copy template files
      if (template) {
        await this.copyTemplateToWorkspace(template, workspacePath);
      }

      logger.info(`Workspace created: ${workspaceId} for user: ${userId}`);

      return {
        success: true,
        workspaceId: workspaceId,
        workspacePath: workspacePath,
        metadata: metadata,
        message: `Workspace ${workspaceName} created successfully`
      };

    } catch (error) {
      logger.error('S3 workspace creation error:', error);
      throw new Error(`Failed to create workspace: ${error.message}`);
    }
  }

  /**
   * Delete a workspace
   */
  async deleteWorkspace(workspaceId, userId) {
    try {
      const workspacePath = await this.getWorkspacePath(workspaceId);
      
      if (!workspacePath) {
        throw new Error(`Workspace not found: ${workspaceId}`);
      }

      // List all objects in the workspace
      const objects = await this.listWorkspaceFiles(workspaceId);
      
      // Delete all files in the workspace
      for (const object of objects) {
        await this.client.send(new DeleteObjectCommand({
          Bucket: this.bucket,
          Key: object.Key
        }));
      }

      logger.info(`Workspace deleted: ${workspaceId} for user: ${userId}`);

      return {
        success: true,
        workspaceId: workspaceId,
        message: `Workspace ${workspaceId} deleted successfully`
      };

    } catch (error) {
      logger.error('S3 workspace deletion error:', error);
      throw new Error(`Failed to delete workspace: ${error.message}`);
    }
  }

  /**
   * Get workspace information
   */
  async getWorkspace(workspaceId) {
    try {
      const workspacePath = await this.getWorkspacePath(workspaceId);
      
      if (!workspacePath) {
        return {
          success: false,
          message: `Workspace not found: ${workspaceId}`
        };
      }

      const metadataKey = `${workspacePath}/.workspace`;
      
      const result = await this.client.send(new GetObjectCommand({
        Bucket: this.bucket,
        Key: metadataKey
      }));

      const metadata = JSON.parse(await result.Body.transformToString());
      const files = await this.listWorkspaceFiles(workspaceId);

      return {
        success: true,
        workspace: {
          ...metadata,
          files: files,
          fileCount: files.length
        }
      };

    } catch (error) {
      logger.error('S3 workspace retrieval error:', error);
      throw new Error(`Failed to get workspace: ${error.message}`);
    }
  }

  /**
   * List all workspaces for a user
   */
  async listUserWorkspaces(userId) {
    try {
      const prefix = `workspaces/${userId}/`;
      
      const result = await this.client.send(new ListObjectsV2Command({
        Bucket: this.bucket,
        Prefix: prefix,
        Delimiter: '/'
      }));

      const workspaces = [];

      for (const object of result.Contents || []) {
        if (object.Key.endsWith('/.workspace')) {
          const workspaceId = object.Key.split('/')[2]; // workspaces/userId/workspaceId/.workspace
          
          try {
            const workspace = await this.getWorkspace(workspaceId);
            if (workspace.success) {
              workspaces.push(workspace.workspace);
            }
          } catch (error) {
            logger.warn(`Failed to get workspace ${workspaceId}:`, error);
          }
        }
      }

      return {
        success: true,
        workspaces: workspaces
      };

    } catch (error) {
      logger.error('S3 list user workspaces error:', error);
      throw new Error(`Failed to list user workspaces: ${error.message}`);
    }
  }

  /**
   * Upload file to workspace
   */
  async uploadFile(workspaceId, filePath, fileContent, options = {}) {
    try {
      const { contentType = 'application/octet-stream', metadata = {} } = options;
      
      const workspacePath = await this.getWorkspacePath(workspaceId);
      
      if (!workspacePath) {
        throw new Error(`Workspace not found: ${workspaceId}`);
      }

      const fullPath = `${workspacePath}/${filePath}`;
      
      await this.client.send(new PutObjectCommand({
        Bucket: this.bucket,
        Key: fullPath,
        Body: fileContent,
        ContentType: contentType,
        Metadata: {
          'workspace-id': workspaceId,
          'file-path': filePath,
          'uploaded-at': new Date().toISOString(),
          ...metadata
        }
      }));

      logger.info(`File uploaded to workspace ${workspaceId}: ${filePath}`);

      return {
        success: true,
        filePath: filePath,
        fullPath: fullPath,
        url: `${this.baseUrl}/${fullPath}`,
        message: `File ${filePath} uploaded successfully`
      };

    } catch (error) {
      logger.error('S3 workspace file upload error:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Download file from workspace
   */
  async downloadFile(workspaceId, filePath) {
    try {
      const workspacePath = await this.getWorkspacePath(workspaceId);
      
      if (!workspacePath) {
        throw new Error(`Workspace not found: ${workspaceId}`);
      }

      const fullPath = `${workspacePath}/${filePath}`;
      
      const result = await this.client.send(new GetObjectCommand({
        Bucket: this.bucket,
        Key: fullPath
      }));

      const content = await result.Body.transformToString();

      return {
        success: true,
        content: content,
        contentType: result.ContentType,
        metadata: result.Metadata,
        size: result.ContentLength
      };

    } catch (error) {
      logger.error('S3 workspace file download error:', error);
      throw new Error(`Failed to download file: ${error.message}`);
    }
  }

  /**
   * Delete file from workspace
   */
  async deleteFile(workspaceId, filePath) {
    try {
      const workspacePath = await this.getWorkspacePath(workspaceId);
      
      if (!workspacePath) {
        throw new Error(`Workspace not found: ${workspaceId}`);
      }

      const fullPath = `${workspacePath}/${filePath}`;
      
      await this.client.send(new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: fullPath
      }));

      logger.info(`File deleted from workspace ${workspaceId}: ${filePath}`);

      return {
        success: true,
        filePath: filePath,
        message: `File ${filePath} deleted successfully`
      };

    } catch (error) {
      logger.error('S3 workspace file deletion error:', error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  /**
   * List files in workspace
   */
  async listWorkspaceFiles(workspaceId) {
    try {
      const workspacePath = await this.getWorkspacePath(workspaceId);
      
      if (!workspacePath) {
        return [];
      }

      const result = await this.client.send(new ListObjectsV2Command({
        Bucket: this.bucket,
        Prefix: workspacePath
      }));

      return (result.Contents || []).filter(object => 
        !object.Key.endsWith('/.workspace') && object.Key !== workspacePath
      );

    } catch (error) {
      logger.error('S3 list workspace files error:', error);
      throw new Error(`Failed to list workspace files: ${error.message}`);
    }
  }

  /**
   * Generate presigned URL for file access
   */
  async generateFileUrl(workspaceId, filePath, operation = 'getObject', expiresIn = 3600) {
    try {
      const workspacePath = await this.getWorkspacePath(workspaceId);
      
      if (!workspacePath) {
        throw new Error(`Workspace not found: ${workspaceId}`);
      }

      const fullPath = `${workspacePath}/${filePath}`;
      
      const command = operation === 'putObject' 
        ? new PutObjectCommand({ Bucket: this.bucket, Key: fullPath })
        : new GetObjectCommand({ Bucket: this.bucket, Key: fullPath });

      const url = await getSignedUrl(this.client, command, { expiresIn });

      return {
        success: true,
        url: url,
        operation: operation,
        expiresIn: expiresIn
      };

    } catch (error) {
      logger.error('S3 generate file URL error:', error);
      throw new Error(`Failed to generate file URL: ${error.message}`);
    }
  }

  /**
   * Copy workspace to new location
   */
  async copyWorkspace(sourceWorkspaceId, targetUserId, newWorkspaceName) {
    try {
      const sourceWorkspace = await this.getWorkspace(sourceWorkspaceId);
      
      if (!sourceWorkspace.success) {
        throw new Error(`Source workspace not found: ${sourceWorkspaceId}`);
      }

      // Create new workspace
      const newWorkspace = await this.createWorkspace(targetUserId, newWorkspaceName, {
        courseId: sourceWorkspace.workspace.courseId,
        projectId: sourceWorkspace.workspace.projectId
      });

      // Copy all files
      for (const file of sourceWorkspace.workspace.files) {
        const fileName = file.Key.split('/').pop();
        
        await this.client.send(new CopyObjectCommand({
          Bucket: this.bucket,
          CopySource: `${this.bucket}/${file.Key}`,
          Key: `${newWorkspace.workspacePath}/${fileName}`
        }));
      }

      logger.info(`Workspace copied: ${sourceWorkspaceId} -> ${newWorkspace.workspaceId}`);

      return {
        success: true,
        sourceWorkspaceId: sourceWorkspaceId,
        targetWorkspaceId: newWorkspace.workspaceId,
        message: `Workspace copied successfully`
      };

    } catch (error) {
      logger.error('S3 workspace copy error:', error);
      throw new Error(`Failed to copy workspace: ${error.message}`);
    }
  }

  /**
   * Generate workspace path
   */
  generateWorkspacePath(userId, workspaceId, courseId = null, projectId = null) {
    let basePath = `workspaces/${userId}/${workspaceId}`;
    
    if (courseId && projectId) {
      basePath = `workspaces/${userId}/courses/${courseId}/projects/${projectId}/${workspaceId}`;
    } else if (courseId) {
      basePath = `workspaces/${userId}/courses/${courseId}/${workspaceId}`;
    }
    
    return basePath;
  }

  /**
   * Get workspace path from workspace ID
   */
  async getWorkspacePath(workspaceId) {
    try {
      // Search for workspace metadata file
      const result = await this.client.send(new ListObjectsV2Command({
        Bucket: this.bucket,
        Prefix: `workspaces/`,
        MaxKeys: 1000
      }));

      for (const object of result.Contents || []) {
        if (object.Key.includes(`/${workspaceId}/.workspace`)) {
          return object.Key.replace('/.workspace', '');
        }
      }

      return null;

    } catch (error) {
      logger.error('S3 get workspace path error:', error);
      return null;
    }
  }

  /**
   * Copy template files to workspace
   */
  async copyTemplateToWorkspace(templateName, workspacePath) {
    try {
      const templatePrefix = `templates/${templateName}/`;
      
      const result = await this.client.send(new ListObjectsV2Command({
        Bucket: this.bucket,
        Prefix: templatePrefix
      }));

      for (const object of result.Contents || []) {
        const fileName = object.Key.replace(templatePrefix, '');
        
        if (fileName) {
          await this.client.send(new CopyObjectCommand({
            Bucket: this.bucket,
            CopySource: `${this.bucket}/${object.Key}`,
            Key: `${workspacePath}/${fileName}`
          }));
        }
      }

      logger.info(`Template ${templateName} copied to workspace: ${workspacePath}`);

    } catch (error) {
      logger.error('S3 copy template error:', error);
      throw new Error(`Failed to copy template: ${error.message}`);
    }
  }

  /**
   * Archive workspace
   */
  async archiveWorkspace(workspaceId) {
    try {
      const workspace = await this.getWorkspace(workspaceId);
      
      if (!workspace.success) {
        throw new Error(`Workspace not found: ${workspaceId}`);
      }

      const archivePath = `archives/${workspaceId}_${Date.now()}`;
      
      // Copy all files to archive location
      for (const file of workspace.workspace.files) {
        await this.client.send(new CopyObjectCommand({
          Bucket: this.bucket,
          CopySource: `${this.bucket}/${file.Key}`,
          Key: `${archivePath}/${file.Key.split('/').pop()}`
        }));
      }

      // Archive metadata
      await this.client.send(new PutObjectCommand({
        Bucket: this.bucket,
        Key: `${archivePath}/.archive`,
        Body: JSON.stringify({
          ...workspace.workspace,
          archivedAt: new Date().toISOString(),
          originalWorkspaceId: workspaceId
        }),
        ContentType: 'application/json'
      }));

      logger.info(`Workspace archived: ${workspaceId} -> ${archivePath}`);

      return {
        success: true,
        archivePath: archivePath,
        message: `Workspace ${workspaceId} archived successfully`
      };

    } catch (error) {
      logger.error('S3 workspace archive error:', error);
      throw new Error(`Failed to archive workspace: ${error.message}`);
    }
  }
}

export default new S3WorkspaceService();
