import { Project, ProjectTemplate, ProjectAssignment, User, Course, Rubric } from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';
import courseRoleService from './courseRole.service.js';
import { checkAccessRole } from '../utils/helpers.utils.js';
import ApiError from '../utils/ApiError.utils.js';
import httpStatus from 'http-status';

class EnhancedProjectService {

  /**
   * Create a new project with enhanced fields
   */
  async createProject(projectData, assignments = []) {
    try {
      const project = await Project.create(projectData);

      // Create project assignments if provided
      if (assignments.length > 0) {
        const assignmentPromises = assignments.map(assignment =>
          ProjectAssignment.create({
            ...assignment,
            project_id: project.id,
            assigned_by: projectData.created_by
          })
        );
        await Promise.all(assignmentPromises);
      }

      // Create template if requested
      if (projectData.is_template) {
        await this.createProjectTemplate(project.id, projectData, projectData.created_by);
      }

      logger.info(`Enhanced project created: ${project.title} by user ${projectData.created_by}`);

      return project;
    } catch (error) {
      logger.error('Error creating enhanced project:', error);
      throw error;
    }
  }


  /**
   * update a project with enhanced fields
   */
  async updateProject(projectData, assignments = []) {
    try {
      const project = await Project.findByPk(projectData.id, {
        include: [
          {
            model: Course,
            as: 'course'
          }
        ]
      });

      if (!project) {
        return res.status(404).json({
          error: 'Not Found',
          message: 'Project not found'
        });
      }

      await project.update(projectData);

      logger.info(`Project updated: ${projectData.title} by user ${projectData.created_by}`);

      // Create project assignments if provided
      /*  if (assignments.length > 0) {
         const assignmentPromises = assignments.map(assignment => 
           ProjectAssignment.create({
             ...assignment,
             project_id: project.id,
             assigned_by: projectData.created_by
           })
         );
         await Promise.all(assignmentPromises);
       }
       
       // Create template if requested
       if (projectData.is_template) {
         await this.createProjectTemplate(projectData.id, projectData, projectData.created_by);
       } */

      return project;
    } catch (error) {
      logger.error('Error creating enhanced project:', error);
      throw error;
    }
  }

  /**
   * Create project template
   */
  async createProjectTemplate(projectId, templateData, createdBy) {
    try {
      const template = await ProjectTemplate.create({
        project_id: projectId,
        template_name: templateData.title,
        template_description: templateData.description,
        category: templateData.template_category || 'general',
        subcategory: templateData.template_subcategory,
        difficulty_level: templateData.difficulty_level,
        estimated_hours: templateData.estimated_hours,
        total_points: templateData.total_points,
        learning_objectives: templateData.learning_objectives,
        prerequisites: templateData.prerequisites,
        skills_covered: templateData.skills_covered,
        technologies_used: templateData.technologies_used,
        tags: templateData.tags,
        created_by: createdBy
      });

      logger.info(`Project template created: ${template.template_name}`);
      return template;
    } catch (error) {
      logger.error('Error creating project template:', error);
      throw error;
    }
  }

  /**
   * Update project template
   */
  async updateProjectTemplate(templateId, updateData, userId) {
    try {
      const template = await ProjectTemplate.findByPk(templateId);

      if (!template) {
        throw new Error('Template not found');
      }

      // Check permissions
      if (template.created_by !== userId) {
        throw new Error('Permission denied: Only creator can update template');
      }

      await template.update(updateData);

      logger.info(`Project template updated: ${template.template_name} by user ${userId}`);
      return template;
    } catch (error) {
      logger.error('Error updating project template:', error);
      throw error;
    }
  }

  /**
   * Get project templates with filtering
   */
  async getProjectTemplates(options = {}) {
    try {
      const {
        category,
        subcategory,
        difficultyLevel,
        isFeatured,
        isPublic = true,
        page = 1,
        limit = 20,
        search
      } = options;

      const whereClause = {};

      if (category) {
        whereClause.category = category;
      }

      if (subcategory) {
        whereClause.subcategory = subcategory;
      }

      if (difficultyLevel) {
        whereClause.difficulty_level = difficultyLevel;
      }

      if (isFeatured !== undefined) {
        whereClause.is_featured = isFeatured;
      }

      if (isPublic !== undefined) {
        whereClause.is_public = isPublic;
      }

      const offset = (page - 1) * limit;

      const { count, rows: templates } = await ProjectTemplate.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'description', 'status'],
            where: search ? {
              [Op.or]: [
                { title: { [Op.iLike]: `%${search}%` } },
                { description: { [Op.iLike]: `%${search}%` } }
              ]
            } : undefined
          },
          {
            model: User,
            as: 'createdBy',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [
          ['is_featured', 'DESC'],
          ['rating', 'DESC'],
          ['usage_count', 'DESC'],
          ['created_at', 'DESC']
        ],
        limit,
        offset
      });

      return {
        templates,
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting project templates:', error);
      throw error;
    }
  }

  /**
   * Assign users to project
   */
  async assignUsersToProject(projectId, assignments, assignedBy) {
    try {
      const assignmentPromises = assignments.map(assignment =>
        ProjectAssignment.create({
          project_id: projectId,
          user_id: assignment.userId,
          role: assignment.role,
          assignment_type: assignment.assignmentType || 'primary',
          permissions: assignment.permissions || {},
          assigned_by: assignedBy,
          start_date: assignment.startDate,
          end_date: assignment.endDate,
          notes: assignment.notes
        })
      );

      const createdAssignments = await Promise.all(assignmentPromises);

      logger.info(`Users assigned to project ${projectId}: ${assignments.length} assignments`);
      return createdAssignments;
    } catch (error) {
      logger.error('Error assigning users to project:', error);
      throw error;
    }
  }

  /**
   * Remove user assignment from project
   */
  async removeUserAssignment(projectId, userId) {
    try {
      const assignment = await ProjectAssignment.findOne({
        where: { project_id: projectId, user_id: userId }
      });

      if (!assignment) {
        throw new Error('Assignment not found');
      }

      await assignment.update({ is_active: false });

      logger.info(`User assignment removed: project ${projectId}, user ${userId}`);
      return { success: true };
    } catch (error) {
      logger.error('Error removing user assignment:', error);
      throw error;
    }
  }

  /**
   * Get project assignments
   */
  async getProjectAssignments(projectId) {
    try {
      const assignments = await ProjectAssignment.findAll({
        where: { project_id: projectId, is_active: true },
        include: [
          {
            model: User,
            as: 'assignedUser',
            attributes: ['id', 'name', 'email', 'role']
          }
        ],
        order: [['role', 'ASC'], ['assigned_at', 'ASC']]
      });

      return assignments;
    } catch (error) {
      logger.error('Error getting project assignments:', error);
      throw error;
    }
  }

  /**
   * Publish project
   */
  async publishProject(projectId, publisherId) {
    try {
      const project = await Project.findByPk(projectId);

      if (!project) {
        throw new Error('Project not found');
      }

      await project.update({
        status: 'published',
        published_at: new Date(),
        published_by: publisherId
      });

      logger.info(`Project published: ${project.title} by user ${publisherId}`);
      return project;
    } catch (error) {
      logger.error('Error publishing project:', error);
      throw error;
    }
  }

  /**
   * Unpublish project
   */
  async unpublishProject(projectId, userId) {
    try {
      const project = await Project.findByPk(projectId);

      if (!project) {
        throw new Error('Project not found');
      }

      // Check permissions
      if (project.created_by !== userId && project.published_by !== userId) {
        throw new Error('Permission denied: Only creator or publisher can unpublish');
      }

      await project.update({
        status: 'draft',
        published_at: null,
        published_by: null
      });

      logger.info(`Project unpublished: ${project.title} by user ${userId}`);
      return project;
    } catch (error) {
      logger.error('Error unpublishing project:', error);
      throw error;
    }
  }

  /**
   * Save project as draft
   */
  async saveProjectAsDraft(projectId, userId) {
    try {
      const project = await Project.findByPk(projectId);

      if (!project) {
        throw new Error('Project not found');
      }

      // Check permissions
      if (project.created_by !== userId) {
        throw new Error('Permission denied: Only creator can save project');
      }

      await project.update({
        status: 'draft',
        published_at: null,
        published_by: null
      });

      logger.info(`Project saved as draft: ${project.title} by user ${userId}`);
      return project;
    } catch (error) {
      logger.error('Error saving project as draft:', error);
      throw error;
    }
  }

  /**
   * Get project with full details including assignments and template
   */
  async getProjectWithDetails(projectId, userId = null) {
    try {
      const project = await Project.findByPk(projectId, {
        include: [
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'name', 'code', 'term']
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'name', 'email']
          },
          {
            model: User,
            as: 'publisher',
            attributes: ['id', 'name', 'email'],
            required: false
          },
          {
            model: Rubric,
            as: 'rubrics',
            attributes: ['id', 'name', 'description', 'criteria', 'max_score', 'weight']
          },
          {
            model: ProjectTemplate,
            as: 'template',
            required: false
          },
          {
            model: ProjectAssignment,
            as: 'assignments',
            include: [
              {
                model: User,
                as: 'assignedUser',
                attributes: ['id', 'name', 'email', 'role']
              }
            ]
          }
        ]
      });

      if (!project) {
        throw new Error('Project not found');
      }

      // Get assignment statistics
      const assignmentStats = await this.getProjectAssignmentStats(projectId);

      // Add assignment statistics to project
      project.dataValues.assignmentStats = assignmentStats;

      return project;
    } catch (error) {
      logger.error('Error getting project with details:', error);
      throw error;
    }
  }

  /**
   * Get project assignment statistics
   */
  async getProjectAssignmentStats(projectId) {
    try {
      const stats = await ProjectAssignment.findAll({
        where: { project_id: projectId, is_active: true },
        attributes: [
          'role',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['role']
      });

      const totalAssignments = await ProjectAssignment.count({
        where: { project_id: projectId, is_active: true }
      });

      return {
        totalAssignments,
        byRole: stats.reduce((acc, stat) => {
          acc[stat.role] = parseInt(stat.dataValues.count);
          return acc;
        }, {}),
        roles: stats.map(stat => stat.role)
      };
    } catch (error) {
      logger.error('Error getting project assignment stats:', error);
      return {
        totalAssignments: 0,
        byRole: {},
        roles: []
      };
    }
  }

  /**
   * Get projects by user assignment
   */
  async getProjectsByUserAssignment(userId, role = null) {
    try {
      const whereClause = {
        user_id: userId,
        is_active: true
      };

      if (role) {
        whereClause.role = role;
      }

      const assignments = await ProjectAssignment.findAll({
        where: whereClause,
        include: [
          {
            model: Project,
            as: 'project',
            include: [
              {
                model: Course,
                as: 'course',
                attributes: ['id', 'name', 'code']
              },
              {
                model: User,
                as: 'creator',
                attributes: ['id', 'name', 'email']
              }
            ]
          }
        ],
        order: [['assigned_at', 'DESC']]
      });

      return assignments.map(assignment => ({
        assignmentId: assignment.id,
        role: assignment.role,
        assignmentType: assignment.assignment_type,
        assignedAt: assignment.assigned_at,
        startDate: assignment.start_date,
        endDate: assignment.end_date,
        project: assignment.project
      }));
    } catch (error) {
      logger.error('Error getting projects by user assignment:', error);
      throw error;
    }
  }

  /**
   * Get user's project workload
   */
  async getUserProjectWorkload(userId) {
    try {
      const assignments = await ProjectAssignment.findAll({
        where: {
          user_id: userId,
          is_active: true
        },
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'status', 'due_date', 'estimated_hours']
          }
        ]
      });

      const workload = {
        totalProjects: assignments.length,
        activeProjects: assignments.filter(a => a.project.status === 'published').length,
        draftProjects: assignments.filter(a => a.project.status === 'draft').length,
        totalEstimatedHours: assignments.reduce((sum, a) => sum + (a.project.estimated_hours || 0), 0),
        byRole: {},
        upcomingDeadlines: []
      };

      // Group by role
      assignments.forEach(assignment => {
        const role = assignment.role;
        if (!workload.byRole[role]) {
          workload.byRole[role] = {
            count: 0,
            projects: []
          };
        }
        workload.byRole[role].count++;
        workload.byRole[role].projects.push(assignment.project);
      });

      // Get upcoming deadlines (next 30 days)
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

      workload.upcomingDeadlines = assignments
        .filter(a => a.project.due_date && a.project.due_date <= thirtyDaysFromNow)
        .map(a => ({
          projectId: a.project.id,
          projectTitle: a.project.title,
          dueDate: a.project.due_date,
          daysUntilDue: Math.ceil((a.project.due_date - new Date()) / (1000 * 60 * 60 * 24))
        }))
        .sort((a, b) => a.daysUntilDue - b.daysUntilDue);

      return workload;
    } catch (error) {
      logger.error('Error getting user project workload:', error);
      throw error;
    }
  }

  /**
   * Duplicate project from template
   */
  async duplicateProjectFromTemplate(templateId, newProjectData, userId) {
    try {
      const template = await ProjectTemplate.findByPk(templateId, {
        include: [
          {
            model: Project,
            as: 'project',
            include: ['rubrics']
          }
        ]
      });

      if (!template) {
        throw new Error('Template not found');
      }

      const originalProject = template.project;

      // Create new project with template data
      const newProject = await Project.create({
        ...newProjectData,
        title: newProjectData.title || `${originalProject.title} (Copy)`,
        description: newProjectData.description || originalProject.description,
        instructions: newProjectData.instructions || originalProject.instructions,
        difficulty_level: newProjectData.difficulty_level || originalProject.difficulty_level,
        estimated_hours: newProjectData.estimated_hours || originalProject.estimated_hours,
        learning_objectives: newProjectData.learning_objectives || originalProject.learning_objectives,
        prerequisites: newProjectData.prerequisites || originalProject.prerequisites,
        tags: newProjectData.tags || originalProject.tags,
        created_by: userId,
        status: 'draft'
      });

      // Copy rubrics if they exist
      if (originalProject.rubrics && originalProject.rubrics.length > 0) {
        const rubricPromises = originalProject.rubrics.map(rubric =>
          Rubric.create({
            project_id: newProject.id,
            name: rubric.name,
            description: rubric.description,
            criteria: rubric.criteria,
            max_score: rubric.max_score,
            weight: rubric.weight
          })
        );
        await Promise.all(rubricPromises);
      }

      // Update template usage count
      await template.update({
        usage_count: template.usage_count + 1,
        last_used: new Date()
      });

      logger.info(`Project duplicated from template: ${template.template_name} by user ${userId}`);
      return newProject;
    } catch (error) {
      logger.error('Error duplicating project from template:', error);
      throw error;
    }
  }

  /**
   * Rate project template
   */
  async rateProjectTemplate(templateId, rating, userId) {
    try {
      const template = await ProjectTemplate.findByPk(templateId);

      if (!template) {
        throw new Error('Template not found');
      }

      // Calculate new average rating
      const newRatingCount = template.rating_count + 1;
      const newRating = ((template.rating * template.rating_count) + rating) / newRatingCount;

      await template.update({
        rating: newRating,
        rating_count: newRatingCount
      });

      logger.info(`Template rated: ${template.template_name} with rating ${rating} by user ${userId}`);
      return template;
    } catch (error) {
      logger.error('Error rating project template:', error);
      throw error;
    }
  }

  /**
   * Approve or reject a project template
   */
  async approveProjectTemplate(templateId, approved, reviewerId, reviewNotes = null) {
    try {
      const template = await ProjectTemplate.findByPk(templateId);

      if (!template) {
        throw new Error('Template not found');
      }

      const reviewStatus = approved ? 'approved' : 'rejected';

      await template.update({
        review_status: reviewStatus,
        reviewed_by: reviewerId,
        review_notes: reviewNotes,
        review_date: new Date()
      });

      return template;
    } catch (error) {
      logger.error('Error approving project template:', error);
      throw error;
    }
  }

  /**
   * Feature or unfeature a project template
   */
  async featureProjectTemplate(templateId, featured, userId) {
    try {
      const template = await ProjectTemplate.findByPk(templateId);

      if (!template) {
        throw new Error('Template not found');
      }

      await template.update({
        is_featured: featured
      });

      return template;
    } catch (error) {
      logger.error('Error featuring project template:', error);
      throw error;
    }
  }

  /**
   * Archive a project template
   */
  async archiveProjectTemplate(templateId, userId, archiveReason = null) {
    try {
      const template = await ProjectTemplate.findByPk(templateId);

      if (!template) {
        throw new Error('Template not found');
      }

      await template.update({
        review_status: 'archived',
        review_notes: archiveReason || 'Template archived by user'
      });

      return template;
    } catch (error) {
      logger.error('Error archiving project template:', error);
      throw error;
    }
  }

  /**
   * Duplicate a project template
   */
  async duplicateProjectTemplate(templateId, newTemplateData, userId) {
    try {
      const originalTemplate = await ProjectTemplate.findByPk(templateId, {
        include: [
          {
            model: Project,
            as: 'project'
          }
        ]
      });

      if (!originalTemplate) {
        throw new Error('Template not found');
      }

      // Create new project from template
      const newProjectData = {
        ...originalTemplate.project.toJSON(),
        title: newTemplateData.title || `${originalTemplate.project.title} (Copy)`,
        description: newTemplateData.description || originalTemplate.project.description,
        course_id: newTemplateData.courseId,
        created_by: userId,
        is_template: false,
        status: 'draft'
      };

      // Remove template-specific fields
      delete newProjectData.id;
      delete newProjectData.created_at;
      delete newProjectData.updated_at;

      const newProject = await Project.create(newProjectData);

      // Create new template entry
      const newTemplate = await ProjectTemplate.create({
        project_id: newProject.id,
        template_name: newTemplateData.templateName || `${originalTemplate.template_name} (Copy)`,
        template_description: newTemplateData.templateDescription || originalTemplate.template_description,
        category: newTemplateData.category || originalTemplate.category,
        subcategory: newTemplateData.subcategory || originalTemplate.subcategory,
        difficulty_level: newTemplateData.difficultyLevel || originalTemplate.difficulty_level,
        estimated_hours: newTemplateData.estimatedHours || originalTemplate.estimated_hours,
        total_points: newTemplateData.totalPoints || originalTemplate.total_points,
        learning_objectives: newTemplateData.learningObjectives || originalTemplate.learning_objectives,
        prerequisites: newTemplateData.prerequisites || originalTemplate.prerequisites,
        skills_covered: newTemplateData.skillsCovered || originalTemplate.skills_covered,
        technologies_used: newTemplateData.technologiesUsed || originalTemplate.technologies_used,
        tags: newTemplateData.tags || originalTemplate.tags,
        is_featured: false,
        is_public: newTemplateData.isPublic !== undefined ? newTemplateData.isPublic : originalTemplate.is_public,
        rating: 0,
        rating_count: 0,
        usage_count: 0,
        download_count: 0,
        version: '1.0.0',
        changelog: [{
          version: '1.0.0',
          date: new Date().toISOString(),
          changes: ['Template duplicated from original'],
          author: userId
        }],
        metadata: {
          duplicated_from: templateId,
          original_author: originalTemplate.created_by,
          duplication_date: new Date().toISOString()
        },
        created_by: userId,
        review_status: 'pending'
      });

      return {
        project: newProject,
        template: newTemplate
      };
    } catch (error) {
      logger.error('Error duplicating project template:', error);
      throw error;
    }
  }

  async creationOfProject(req) {
    const {
      title,
      description,
      courseId,
      projectType = 'individual',
      difficultyLevel = 'beginner',
      estimatedHours,
      totalPoints = 100,
      dueDate,
      startDate,
      instructions,
      projectOverview,
      learningObjectives,
      prerequisites,
      skillsCovered = [],
      technologiesUsed = [],
      tags = [],
      isTemplate = false,
      templateCategory = 'general',
      templateSubcategory,
      assignments = [],
      rubrics = [],
      isScreen,
      status = 'draft',
      id,
      categoryId,
      instructorIds = [],
      teachingAssId = [],
      maxSubmissions,
      lateSubmissionsAllowed = false,
    } = req.body;

    // Validate required fields
    if (!title || !description || !courseId) throw new ApiError(httpStatus.BAD_REQUEST, 'Title, description, and course ID are required');
      
    // Validate user can create projects in this course

    if (!await checkAccessRole(req.primaryRole, req.userRoles, 'adminOnly')) {
      try {
        const permission = await courseRoleService.canCreateProjects(req.user.id, courseId);
        if (!permission.canCreate) throw new ApiError(httpStatus.FORBIDDEN, permission.reason);
      } catch (error) {
        throw error;
      }
    }

    try {
      /* const projectData = {
        title,
        description,
        course_id: courseId,
        project_type: projectType,
        difficulty_level: difficultyLevel,
        estimated_hours: estimatedHours,
        total_points: totalPoints,
        due_date: dueDate,
        start_date: startDate,
        instructions,
        project_overview: projectOverview,
        learning_objectives: learningObjectives,
        prerequisites,
        requirements,
        tags,
        is_template: isTemplate,
        template_category: templateCategory,
        template_subcategory: templateSubcategory,
        created_by: req.user.id,
        status: 'draft'
      }; */

      // Update project
      const projectData = {};
      if (title !== undefined) projectData.title = title;
      if (categoryId) projectData.category_id = categoryId;
      if (courseId) projectData.course_id = courseId;
      if (description !== undefined) projectData.description = description;
      if (instructorIds) projectData.instructor_id = instructorIds;
      if (teachingAssId) projectData.teaching_ass_id = teachingAssId;
      if (projectType) projectData.type = projectType;
      if (difficultyLevel !== undefined) projectData.difficulty_level = difficultyLevel;
      if (totalPoints) projectData.total_points = totalPoints;
      if (estimatedHours !== undefined) projectData.estimated_hours = estimatedHours;
      if (tags) projectData.tags = tags;
      if (projectOverview !== undefined) projectData.project_overview = projectOverview;
      if (learningObjectives !== undefined) projectData.learning_objectives = learningObjectives;
      if (prerequisites !== undefined) projectData.prerequisites = prerequisites;
      if (instructions !== undefined) projectData.instructions = instructions;
      // if (resources !== undefined) projectData.resources = resources;
      if (maxSubmissions) projectData.max_attempts = maxSubmissions;
      if (lateSubmissionsAllowed) projectData.late_submission_allowed = lateSubmissionsAllowed;
      if (dueDate !== undefined) projectData.due_date = dueDate;
      if (status !== undefined) projectData.status = status;
      if (isScreen !== undefined) projectData.isScreen = isScreen;
      if (startDate !== undefined) projectData.start_date = startDate;
      if (isTemplate !== undefined) projectData.is_template = isTemplate;
      if (templateCategory !== undefined) projectData.template_category = templateCategory;
      if (templateSubcategory !== undefined) projectData.template_subcategory = templateSubcategory

      // if (settings !== undefined) projectData.settings = settings;

      if (req.user.id) {
        projectData.created_by = req.user.id;
        projectData.creator_id = req.user.id;
      }

      let project;
      if (isScreen > 1) {
        projectData.id = id;
        project = await this.updateProject(projectData, assignments);
      } else {
        project = await this.createProject(projectData, assignments);
      }

      return {
        id: project.id,
        projectId: project.project_code,
        title: project.title,
        status: project.status,
        courseId: project.course_id,
        projectType: project.project_type,
        totalPoints: project.total_points,
        isTemplate: project.is_template,
        createdAt: project.createdAt,
        isScreen: project.isScreen
      }
    } catch (error) {
      throw error;
    }
  }

}

export default new EnhancedProjectService();
