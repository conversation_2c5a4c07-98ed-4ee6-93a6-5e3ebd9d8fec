import axios from 'axios';
import logger from '../config/logger.config.js';

// Configuration for external sandbox API
const SANDBOX_API_BASE_URL = process.env.SANDBOX_API_BASE_URL || 'https://sandbox-api.example.com';
const SANDBOX_API_TOKEN = process.env.SANDBOX_API_TOKEN;

/**
 * Create a sandbox environment
 */
export const createSandbox = async (sandboxConfig) => {
  try {
    const response = await axios.post(`${SANDBOX_API_BASE_URL}/sandboxes`, sandboxConfig, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SANDBOX_API_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Sandbox creation failed:', error);
    throw new Error('Sandbox creation failed');
  }
};

/**
 * Delete a sandbox environment
 */
export const deleteSandbox = async (sandboxId) => {
  try {
    const response = await axios.delete(`${SANDBOX_API_BASE_URL}/sandboxes/${sandboxId}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SANDBOX_API_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Sandbox deletion failed:', error);
    throw new Error('Sandbox deletion failed');
  }
};

/**
 * Get sandbox status
 */
export const getSandboxStatus = async (sandboxId) => {
  try {
    const response = await axios.get(`${SANDBOX_API_BASE_URL}/sandboxes/${sandboxId}/status`, {
      headers: {
        'Authorization': `Bearer ${SANDBOX_API_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Status retrieval failed:', error);
    throw new Error('Status retrieval failed');
  }
};

/**
 * Update sandbox resources
 */
export const updateSandboxResources = async (sandboxId, resourceUpdate) => {
  try {
    // Validate resource limits
    if (resourceUpdate.cpu && (resourceUpdate.cpu < 0.1 || resourceUpdate.cpu > 8)) {
      throw new Error('CPU limit must be between 0.1 and 8 cores');
    }
    
    if (resourceUpdate.memory && (resourceUpdate.memory < '128Mi' || resourceUpdate.memory > '32Gi')) {
      throw new Error('Memory limit must be between 128Mi and 32Gi');
    }

    const response = await axios.patch(`${SANDBOX_API_BASE_URL}/sandboxes/${sandboxId}/resources`, resourceUpdate, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SANDBOX_API_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Resource update failed:', error);
    throw new Error('Resource update failed');
  }
};

/**
 * List sandboxes with optional filtering
 */
export const listSandboxes = async (filters = {}) => {
  try {
    const params = new URLSearchParams();
    
    if (filters.page) params.append('page', filters.page);
    if (filters.limit) params.append('limit', filters.limit);
    if (filters.status) params.append('status', filters.status);
    if (filters.userId) params.append('userId', filters.userId);

    const response = await axios.get(`${SANDBOX_API_BASE_URL}/sandboxes?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${SANDBOX_API_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Listing failed:', error);
    throw new Error('Listing failed');
  }
};

/**
 * Get sandbox logs
 */
export const getSandboxLogs = async (sandboxId, options = {}) => {
  try {
    const params = new URLSearchParams();
    
    if (options.level) params.append('level', options.level);
    if (options.limit) params.append('limit', options.limit);
    if (options.since) params.append('since', options.since);

    const response = await axios.get(`${SANDBOX_API_BASE_URL}/sandboxes/${sandboxId}/logs?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${SANDBOX_API_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Log retrieval failed:', error);
    throw new Error('Log retrieval failed');
  }
};

/**
 * Restart a sandbox
 */
export const restartSandbox = async (sandboxId, options = {}) => {
  try {
    const response = await axios.post(`${SANDBOX_API_BASE_URL}/sandboxes/${sandboxId}/restart`, options, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SANDBOX_API_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Restart failed:', error);
    throw new Error('Restart failed');
  }
};

/**
 * Scale a sandbox
 */
export const scaleSandbox = async (sandboxId, scaleConfig) => {
  try {
    // Validate scaling limits
    if (scaleConfig.replicas && (scaleConfig.replicas < 1 || scaleConfig.replicas > 10)) {
      throw new Error('Replicas must be between 1 and 10');
    }

    const response = await axios.post(`${SANDBOX_API_BASE_URL}/sandboxes/${sandboxId}/scale`, scaleConfig, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SANDBOX_API_TOKEN}`
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Scaling failed:', error);
    throw new Error('Scaling failed');
  }
};
