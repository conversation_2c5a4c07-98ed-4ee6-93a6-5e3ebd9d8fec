import axios from 'axios';
import logger from '../config/logger.js';

/**
 * JupyterServerApiService - Comprehensive service for interacting with Jupyter Server REST APIs
 * through JupyterHub proxy for multi-user environments
 */
class JupyterServerApiService {
  constructor() {
    this.hubUrl = process.env.JUPYTERHUB_URL || 'http://localhost:8001';
    this.adminToken = process.env.JUPYTERHUB_ADMIN_TOKEN;
    this.userServerPrefix = process.env.JUPYTER_USER_SERVER_PREFIX || '/user';
    this.projectPrefix = process.env.JUPYTER_PROJECT_PREFIX || 'project_';
    this.defaultKernel = process.env.JUPYTER_DEFAULT_KERNEL || 'python3';
    this.apiUrl = `${this.hubUrl}/hub/api`;

    // Create axios instance with default configuration
    this.hubApi = axios.create({
      baseURL: this.apiUrl,
      timeout: 30000,
      headers: {
        Authorization: `token ${this.adminToken}`,
        'Content-Type': 'application/json'
      }
    });

    // Add request/response interceptors for logging
    this.hubApi.interceptors.request.use(
      config => {
        logger.info(
          `JupyterHub API Request: ${config.method?.toUpperCase()} ${config.url}`
        );
        return config;
      },
      error => {
        logger.error('JupyterHub API Request Error:', error);
        return Promise.reject(error);
      }
    );

    this.hubApi.interceptors.response.use(
      response => {
        logger.info(
          `JupyterHub API Response: ${response.status} ${response.config.url}`
        );
        return response;
      },
      error => {
        logger.error('JupyterHub API Response Error:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          url: error.config?.url
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Create axios instance for user-specific Jupyter server API calls
   */
  createUserApiInstance(username) {
    const baseURL = `${this.hubUrl}${this.userServerPrefix}/${username}/api`;

    logger.info(`createUserApiInstance -- base URL: ${baseURL}`);
    return axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        Authorization: `token ${this.adminToken}`,
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Create or ensure user exists in JupyterHub
   */
  async ensureUser(username, email, options = {}) {
    try {
      // First try to get the user
      const existingUser = await this.getUser(username);
      if (existingUser.success) {
        logger.info(`JupyterHub user already exists: ${username}`);
        return {
          success: true,
          user: existingUser.user,
          message: `User ${username} already exists`,
          // Use hubUrl (baseUrl property doesn't exist; previous code returned undefined URL)
          userUrl: `${this.hubUrl}${this.userServerPrefix}/${username}`,
          created: false
        };
      }

      // User doesn't exist, create them
      const result = await this.createUser(username, email, options);
      return { ...result, created: true };
    } catch (error) {
      logger.error('JupyterHub ensure user error:', error);
      throw new Error(`Failed to ensure JupyterHub user: ${error.message}`);
    }
  }

  /**
   * Create a new user in JupyterHub
   */
  async createUser(username, email, options = {}) {
    try {
      const { admin = false, serverName = null, groups = [] } = options;
      // JupyterHub user creation API expects either:
      //  - POST /users/{username}
      //  - POST /users with {"usernames": ["name1", ...]}
      // Previous implementation sent { name: username } to /users causing 400.
      const payload = {}; // keep minimal; some Authenticators reject unknown fields

      logger.info(
        `Creating JupyterHub user via single-user endpoint POST /users/${username}`
      );
      let response;
      try {
        response = await this.hubApi.post(
          `/users/${encodeURIComponent(username)}`,
          payload
        );
      } catch (singleErr) {
        const s = singleErr.response?.status;
        // If single endpoint fails with 400, attempt bulk format
        if (s === 400) {
          logger.warn(
            `Single create returned 400 for ${username}; trying bulk creation POST /users`
          );
          await this.hubApi.post('/users', { usernames: [username], admin });
          const confirm = await this.getUser(username);
          response = { status: 201, data: confirm.user };
        } else if (s === 409) {
          logger.info(`User ${username} already exists (409)`);
          return {
            success: true,
            user: { name: username },
            message: `User ${username} already exists`,
            userUrl: `${this.hubUrl}${this.userServerPrefix}/${username}`
          };
        } else {
          throw singleErr;
        }
      }
      logger.info(
        `JupyterHub user created (status ${response.status}): ${username}`
      );

      // Add user to groups if specified
      if (groups.length > 0) {
        for (const group of groups) {
          try {
            await this.addUserToGroup(username, group);
          } catch (groupError) {
            logger.warn(
              `Failed to add user ${username} to group ${group}:`,
              groupError.message
            );
          }
        }
      }

      return {
        success: true,
        user: response.data,
        message: `User ${username} created successfully`,
        userUrl: `${this.hubUrl}${this.userServerPrefix}/${username}`
      };
    } catch (error) {
      const status = error.response?.status;
      logger.error('JupyterHub user creation error:', {
        status,
        data: error.response?.data,
        message: error.message,
        username
      });
      if (status === 409) {
        return {
          success: true,
          user: { name: username },
          message: `User ${username} already exists`,
          userUrl: `${this.hubUrl}${this.userServerPrefix}/${username}`
        };
      }
      if (status === 400) {
        throw new Error(
          `Hub rejected user creation (400). Check authenticator config; some authenticators create users only on first interactive login.`
        );
      }
      throw new Error(
        `Failed to create JupyterHub user: ${error.response?.data?.message || error.message}`
      );
    }
  }

  /**
   * Add user to a group
   */
  async addUserToGroup(username, groupName) {
    try {
      const response = await axios.post(
        `${this.apiUrl}/groups/${groupName}/users`,
        {
          users: [username]
        },
        {
          headers: {
            Authorization: `token ${this.adminToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      logger.info(`User ${username} added to group ${groupName}`);

      return {
        success: true,
        message: `User ${username} added to group ${groupName}`
      };
    } catch (error) {
      logger.error('JupyterHub add user to group error:', error);
      throw new Error(`Failed to add user to group: ${error.message}`);
    }
  }

  /**
   * Get user information from JupyterHub
   */
  async getUser(username) {
    try {
      const response = await this.hubApi.get(`/users/${username}`);

      return {
        success: true,
        user: response.data
      };
    } catch (error) {
      if (error.response && error.response.status === 404) {
        return {
          success: false,
          message: `User ${username} not found`
        };
      }

      logger.error('JupyterHub user retrieval error:', error);
      throw new Error(`Failed to get JupyterHub user: ${error.message}`);
    }
  }

  /**
   * Get all users from JupyterHub
   */
  async getAllUsers() {
    try {
      const response = await axios.get(`${this.apiUrl}/users`, {
        headers: {
          Authorization: `token ${this.adminToken}`
        }
      });

      return {
        success: true,
        users: response.data
      };
    } catch (error) {
      logger.error('JupyterHub users retrieval error:', error);
      throw new Error(`Failed to get JupyterHub users: ${error.message}`);
    }
  }

  /**
   * Start a user's server
   */
  async startServer(username, serverName = '') {
    // 1) read user model
    const u = await this.hubApi.get(`/users/${encodeURIComponent(username)}`);
    const servers = u.data.servers || {};
    const key = serverName || ''; // default server key is ''
    const existing = servers[key];

    logger.info(
      `startServer -- existing server: ${JSON.stringify(existing)}, servers: ${JSON.stringify(servers)}`
    );
    // 2) if not present, start
    if (!existing) {
      const path = serverName
        ? `/users/${username}/servers/${serverName}`
        : `/users/${username}/server`;
      try {
        await this.hubApi.post(path, {}); // 201/202 on success, 400 if exists/pending
      } catch (e) {
        if (e.response?.status !== 400) throw e;
      }
    }

    // 3) poll progress until ready
    const progressUrl = `/users/${username}${serverName ? `/servers/${serverName}` : ''}/progress`;
    const deadline = Date.now() + 120000; // 2m
    while (Date.now() < deadline) {
      const uu = await this.hubApi.get(
        `/users/${encodeURIComponent(username)}`
      );
      const sv = (uu.data.servers || {})[key] || null;
      logger.info(`startServer polling -- server: ${JSON.stringify(sv)}`);
      if (sv?.ready)
        return {
          success: true,
          url: `${this.hubUrl}${this.userServerPrefix}/${username}`
        };
      await new Promise(r => setTimeout(r, 1500));
    }
    throw new Error('Server did not become ready in time');
  }

  //  return {
  //           success: true,
  //           server: retryResp.data,
  //           message: `Server started for user ${username}`
  //         };

  /**
   * Stop a user's server
   */
  async stopServer(username, serverName = null) {
    try {
      const path = serverName
        ? `${this.apiUrl}/users/${username}/servers/${serverName}`
        : `${this.apiUrl}/users/${username}/server`;
      await axios.delete(path, {
        headers: { Authorization: `token ${this.adminToken}` }
      });

      logger.info(`JupyterHub server stopped for user: ${username}`);

      return {
        success: true,
        message: `Server stopped for user ${username}`
      };
    } catch (error) {
      logger.error('JupyterHub server stop error:', error);
      throw new Error(`Failed to stop JupyterHub server: ${error.message}`);
    }
  }

  /**
   * Get server status for a user
   */
  async getServerStatus(username, serverName = null) {
    try {
      // Use user model to inspect server(s)
      const userResp = await this.hubApi.get(`/users/${username}`);
      if (serverName) {
        const named = userResp.data.servers?.[serverName];
        if (named) {
          return { success: true, status: named };
        }
        return { success: false, status: 'not_found' };
      }
      // Default server: in Hub >= 2, stored under servers[''] or legacy 'server'
      const defaultServer =
        userResp.data.servers?.[''] || userResp.data.server || null;
      if (defaultServer) {
        return { success: true, status: defaultServer };
      }
      return { success: false, status: 'not_found' };
    } catch (error) {
      if (error.response && error.response.status === 404) {
        return {
          success: false,
          status: 'not_found',
          message: `Server not found for user ${username}`
        };
      }

      logger.error('JupyterHub server status error:', error);
      throw new Error(
        `Failed to get JupyterHub server status: ${error.message}`
      );
    }
  }

  /**
   * Create a new notebook for a user in a specific project
   */
  async createNotebookForUser(username, projectId, notebookName, options = {}) {
    try {
      const api = this.createUserApiInstance(username);
      const notebookPath = `${this.projectPrefix}${projectId}/${notebookName}`;

      const payload = {
        type: 'notebook',
        content: options.content || null // Creates an empty notebook if null
      };

      logger.info(
        `Creating notebook for user '${username}' at: ${notebookPath}`
      );
      const response = await api.post(`/contents/${notebookPath}`, payload);

      logger.info(
        `Successfully created notebook for '${username}' at '${notebookPath}'`
      );
      return {
        success: true,
        notebook: response.data,
        path: notebookPath,
        url: `${this.hubUrl}${this.userServerPrefix}/${username}/notebooks/${notebookPath}`
      };
    } catch (error) {
      logger.error(
        `Error creating notebook for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to create notebook: ${error.message}`);
    }
  }

  /**
   * Get notebook content for a user
   */
  async getNotebook(username, notebookPath) {
    try {
      const api = this.createUserApiInstance(username);
      const response = await api.get(`/contents/${notebookPath}`);

      return {
        success: true,
        notebook: response.data
      };
    } catch (error) {
      logger.error(
        `Error getting notebook for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to get notebook: ${error.message}`);
    }
  }

  /**
   * Save notebook content for a user
   */
  async saveNotebook(username, notebookPath, content) {
    try {
      const api = this.createUserApiInstance(username);
      const response = await api.put(`/contents/${notebookPath}`, content);

      return {
        success: true,
        notebook: response.data
      };
    } catch (error) {
      logger.error(
        `Error saving notebook for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to save notebook: ${error.message}`);
    }
  }

  /**
   * Delete notebook for a user
   */
  async deleteNotebook(username, notebookPath) {
    try {
      const api = this.createUserApiInstance(username);
      await api.delete(`/contents/${notebookPath}`);

      return {
        success: true,
        message: `Notebook ${notebookPath} deleted successfully`
      };
    } catch (error) {
      logger.error(
        `Error deleting notebook for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to delete notebook: ${error.message}`);
    }
  }

  /**
   * List contents in a directory for a user
   */
  async listContents(username, path = '') {
    try {
      const api = this.createUserApiInstance(username);
      const response = await api.get(`/contents/${path}`);

      return {
        success: true,
        contents: response.data
      };
    } catch (error) {
      logger.error(
        `Error listing contents for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to list contents: ${error.message}`);
    }
  }

  /**
   * Create a directory for a user
   */
  async createDirectory(username, dirPath) {
    try {
      const api = this.createUserApiInstance(username);
      const payload = {
        type: 'directory'
      };

      const response = await api.post(`/contents/${dirPath}`, payload);

      return {
        success: true,
        directory: response.data
      };
    } catch (error) {
      logger.error(
        `Error creating directory for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to create directory: ${error.message}`);
    }
  }

  /**
   * Create a kernel for a user
   */
  async createKernel(username, kernelName = null) {
    try {
      const api = this.createUserApiInstance(username);
      const payload = {
        name: kernelName || this.defaultKernel
      };

      const response = await api.post('/kernels', payload);

      return {
        success: true,
        kernel: response.data
      };
    } catch (error) {
      logger.error(
        `Error creating kernel for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to create kernel: ${error.message}`);
    }
  }

  /**
   * Get kernel information for a user
   */
  async getKernel(username, kernelId) {
    try {
      const api = this.createUserApiInstance(username);
      const response = await api.get(`/kernels/${kernelId}`);

      return {
        success: true,
        kernel: response.data
      };
    } catch (error) {
      logger.error(
        `Error getting kernel for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to get kernel: ${error.message}`);
    }
  }

  /**
   * List all kernels for a user
   */
  async listKernels(username) {
    try {
      const api = this.createUserApiInstance(username);
      const response = await api.get('/kernels');

      return {
        success: true,
        kernels: response.data
      };
    } catch (error) {
      logger.error(
        `Error listing kernels for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to list kernels: ${error.message}`);
    }
  }

  /**
   * Delete a kernel for a user
   */
  async deleteKernel(username, kernelId) {
    try {
      const api = this.createUserApiInstance(username);
      await api.delete(`/kernels/${kernelId}`);

      return {
        success: true,
        message: `Kernel ${kernelId} deleted successfully`
      };
    } catch (error) {
      logger.error(
        `Error deleting kernel for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to delete kernel: ${error.message}`);
    }
  }

  /**
   * Interrupt a kernel for a user
   */
  async interruptKernel(username, kernelId) {
    try {
      const api = this.createUserApiInstance(username);
      await api.post(`/kernels/${kernelId}/interrupt`);

      return {
        success: true,
        message: `Kernel ${kernelId} interrupted successfully`
      };
    } catch (error) {
      logger.error(
        `Error interrupting kernel for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to interrupt kernel: ${error.message}`);
    }
  }

  /**
   * Restart a kernel for a user
   */
  async restartKernel(username, kernelId) {
    try {
      const api = this.createUserApiInstance(username);
      const response = await api.post(`/kernels/${kernelId}/restart`);

      return {
        success: true,
        kernel: response.data
      };
    } catch (error) {
      logger.error(
        `Error restarting kernel for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to restart kernel: ${error.message}`);
    }
  }

  /**
   * Create a session for a user
   */
  async createSession(username, path, kernelName = null, type = 'notebook') {
    try {
      const api = this.createUserApiInstance(username);
      const payload = {
        path,
        type,
        kernel: { name: kernelName || this.defaultKernel }
      };

      const response = await api.post('/sessions', payload);

      return {
        success: true,
        session: response.data
      };
    } catch (error) {
      logger.error(
        `Error creating session for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to create session: ${error.message}`);
    }
  }

  /**
   * Get session information for a user
   */
  async getSession(username, sessionId) {
    try {
      const api = this.createUserApiInstance(username);
      const response = await api.get(`/sessions/${sessionId}`);

      return {
        success: true,
        session: response.data
      };
    } catch (error) {
      logger.error(
        `Error getting session for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to get session: ${error.message}`);
    }
  }

  /**
   * List all sessions for a user
   */
  async listSessions(username) {
    try {
      const api = this.createUserApiInstance(username);
      const response = await api.get('/sessions');

      return {
        success: true,
        sessions: response.data
      };
    } catch (error) {
      logger.error(
        `Error listing sessions for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to list sessions: ${error.message}`);
    }
  }

  /**
   * Delete a session for a user
   */
  async deleteSession(username, sessionId) {
    try {
      const api = this.createUserApiInstance(username);
      await api.delete(`/sessions/${sessionId}`);

      return {
        success: true,
        message: `Session ${sessionId} deleted successfully`
      };
    } catch (error) {
      logger.error(
        `Error deleting session for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to delete session: ${error.message}`);
    }
  }

  /**
   * Get kernel specifications for a user
   */
  async getKernelSpecs(username) {
    try {
      const api = this.createUserApiInstance(username);
      const response = await api.get('/kernelspecs');

      return {
        success: true,
        kernelspecs: response.data
      };
    } catch (error) {
      logger.error(
        `Error getting kernel specs for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to get kernel specs: ${error.message}`);
    }
  }

  /**
   * Get server info for a user
   */
  async getServerInfo(username) {
    try {
      const api = this.createUserApiInstance(username);
      const response = await api.get('/');

      return {
        success: true,
        info: response.data
      };
    } catch (error) {
      logger.error(
        `Error getting server info for '${username}':`,
        error.response ? error.response.data : error.message
      );
      throw new Error(`Failed to get server info: ${error.message}`);
    }
  }

  /**
   * Create a complete workspace for a user with project structure
   */
  async createUserWorkspace(username, projectId, options = {}) {
    try {
      const { kernelName = this.defaultKernel, createSampleNotebook = true } =
        options;

      // Create project directory
      const projectPath = `${this.projectPrefix}${projectId}`;
      await this.mkdirp(username, projectPath);
      await this.createDirectory(username, projectPath);

      // Create session for the workspace
      const sessionPath = `${projectPath}/workspace.ipynb`;
      const session = await this.createSession(
        username,
        sessionPath,
        kernelName
      );

      let notebook = null;
      if (createSampleNotebook) {
        // Create a sample notebook
        const notebookContent = {
          type: 'notebook',
          content: {
            cells: [
              {
                cell_type: 'markdown',
                metadata: {},
                source: [
                  `# Project ${projectId} Workspace\n`,
                  `Welcome to your Jupyter workspace for project ${projectId}!\n`,
                  `\n`,
                  `This is your personal sandbox environment where you can:\n`,
                  `- Write and execute Python code\n`,
                  `- Create data visualizations\n`,
                  `- Experiment with machine learning models\n`,
                  `- Collaborate on data science projects\n`
                ]
              },
              {
                cell_type: 'code',
                execution_count: null,
                metadata: {},
                outputs: [],
                source: [
                  '# Welcome to your workspace!\n',
                  'import pandas as pd\n',
                  'import numpy as np\n',
                  'import matplotlib.pyplot as plt\n',
                  '\n',
                  'print("Hello from your Jupyter workspace!")\n',
                  'print(f"Project ID: {projectId}")'
                ]
              }
            ],
            metadata: {
              kernelspec: {
                display_name: 'Python 3',
                language: 'python',
                name: 'python3'
              },
              language_info: {
                name: 'python',
                version: '3.8.0'
              }
            },
            nbformat: 4,
            nbformat_minor: 4
          }
        };

        notebook = await this.saveNotebook(
          username,
          sessionPath,
          notebookContent
        );
      }

      return {
        success: true,
        workspace: {
          projectId,
          projectPath,
          session: session.session,
          notebook: notebook?.notebook,
          workspaceUrl: `${this.hubUrl}${this.userServerPrefix}/${username}/notebooks/${sessionPath}`
        }
      };
    } catch (error) {
      logger.error(
        `Error creating workspace for '${username}':`,
        error.message
      );
      throw new Error(`Failed to create workspace: ${error.message}`);
    }
  }

  async mkdirp(username, dirPath) {
    const api = this.createUserApiInstance(username);
    // normalize and split
    const parts = dirPath.split('/').filter(Boolean);
    let built = '';
    for (const part of parts) {
      built = built ? `${built}/${part}` : part;
      try {
        await api.post(`/contents/${encodeURIComponent(built)}`, {
          type: 'directory'
        });
      } catch (e) {
        const s = e.response?.status;
        const msg = e.response?.data?.message || '';
        // 409 Already exists is fine; 404 means parent missing (shouldn't happen due to loop)
        if (!(s === 409 || msg.includes('already exists'))) {
          throw e;
        }
      }
    }
    return { success: true, path: dirPath };
  }

  /**
   * Get user's workspace information
   */
  async getUserWorkspaces(username) {
    try {
      // List all sessions for the user
      const sessionsResult = await this.listSessions(username);

      // Filter sessions that belong to projects
      const projectSessions = sessionsResult.sessions.filter(session =>
        session.path.startsWith(this.projectPrefix)
      );

      // Get project information from session paths
      const workspaces = projectSessions.map(session => {
        const pathParts = session.path.split('/');
        const projectDir = pathParts[0];
        const projectId = projectDir.replace(this.projectPrefix, '');

        return {
          projectId,
          projectPath: projectDir,
          session,
          workspaceUrl: `${this.hubUrl}${this.userServerPrefix}/${username}/notebooks/${session.path}`
        };
      });

      return {
        success: true,
        workspaces,
        count: workspaces.length
      };
    } catch (error) {
      logger.error(
        `Error getting workspaces for '${username}':`,
        error.message
      );
      throw new Error(`Failed to get workspaces: ${error.message}`);
    }
  }

  /**
   * Check if user's Jupyter server is running
   */
  async checkUserServerStatus(username) {
    try {
      const info = await this.getServerInfo(username); // hits /user/:name/api/
      return { success: true, status: 'running', info: info.info };
    } catch (error) {
      return {
        success: false,
        status: 'not_running',
        error: error.message
      };
    }
  }

  /**
   * Wait until the user's single-user server is running (polling)
   */
  async waitForServer(username, timeoutMs = 60000, intervalMs = 2000) {
    const start = Date.now();
    while (Date.now() - start < timeoutMs) {
      const status = await this.checkUserServerStatus(username);
      if (status.status === 'running') return true;
      await new Promise(r => setTimeout(r, intervalMs));
    }
    throw new Error('Timed out waiting for user server to start');
  }
}

export default new JupyterServerApiService();
