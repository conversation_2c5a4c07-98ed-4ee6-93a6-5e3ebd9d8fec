import { Project } from '../models/associations.js';
import logger from '../config/logger.config.js';

/**
 * update a project's dataset S3 URL
 * @param {string} s3Url - The S3 URL of the dataset
 * @param {string} projectId - The ID of the project to update
 * @returns {Promise<void>}
 */
export const updateDataSetS3Url = async (s3Url, projectId) => {
  try {
    const project = await Project.findByPk(projectId);
    if (!project) {
      throw new Error('Project not found');
    }
    project.dataset_s3_url = s3Url;
    await project.save();
  } catch (error) {
    logger.error('Failed to update project dataset S3 URL:', error);
    throw new Error('Failed to update project dataset S3 URL');
  }
};

export const checkCourseProjectId = async (courseId, projectId) => {
  try {
    const project = await Project.findOne({
      where: { id: projectId, course_id: courseId }
    });
    if (!project) {
      logger.error(
        `Project ${projectId} does not belong to course ${courseId}`
      );
      throw new Error('Project does not belong to the specified course');
    }
    return true;
  } catch (error) {
    logger.error('Error checking course and project ID:', error);
    throw new Error('Error checking course and project ID');
  }
};
