// services/jupyterService.js
import config from '../config/database.config.js';
import axios from 'axios';
import logger from '../config/logger.config.js';

// ------------------ Axios Client ------------------
const jupyterhubApi = axios.create({
  baseURL: `${config.jupyterhub.url}/hub/api`,
  timeout: 60000,
  headers: {
    Authorization: `token ${config.jupyterhub.apiToken}`
  }
});

const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

// ------------------ User Management ------------------
const getUser = async username => {
  try {
    const response = await jupyterhubApi.get(`/users/${username}`);
    return response.data;
  } catch (error) {
    if (error.response?.status === 404) {
      return null; // User not found
    }
    throw error;
  }
};

const createUser = async username => {
  try {
    const response = await jupyterhubApi.post(`/users/${username}`, {
      name: username
    });
    logger.info(`User '${username}' created successfully.`);
    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(`Failed to create user ${username}: ${errorMessage}`);
    throw new Error('Could not create Jupyter user.');
  }
};

// ------------------ Server Management ------------------
const getServerStatus = async username => {
  const user = await getUser(username);
  return user?.servers?.[''] || null;
};

const startServer = async (username, serverName = '') => {
  try {
    const response = await jupyterhubApi.post(
      `/users/${username}/servers/${serverName}`,
      { name: serverName }
    );
    logger.info(`JupyterHub server start initiated for user: ${username}`);
    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(
      `Failed to start JupyterHub server for ${username}: ${errorMessage}`
    );
    throw new Error('Could not start Jupyter server.');
  }
};

const stopServer = async user => {
  try {
    const username = user.jupiterUserName;
    const servername = user.id;
    const response = await jupyterhubApi.delete(
      `/users/${username}/servers/${servername}`
    );
    logger.info(`JupyterHub server stopped for user: ${username}`);
    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(
      `Failed to stop JupyterHub server for ${username}: ${errorMessage}`
    );
    throw new Error('Could not stop Jupyter server.');
  }
};

const getUserToken = async username => {
  try {
    const response = await jupyterhubApi.post(`/users/${username}/tokens`, {
      note: `User token for ${username}`
    });
    logger.info(
      `Token generated for user: ${username} -- ${JSON.stringify(response.data, null, 2)}`
    );
    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(`Failed to generate token for ${username}: ${errorMessage}`);
    throw new Error('Could not generate user token.');
  }
};

// ------------------ Ensure Running Server ------------------
const ensureServerIsRunning = async user => {
  const POLL_INTERVAL = 2000; // 2 sec
  const START_TIMEOUT = 120000; // 2 min
  const startTime = Date.now();

  const username = user.jupiterUserName;
  logger.info(`Ensuring Jupyter server is running for user: ${username}`);

  try {
    // 1. Ensure user exists
    let user = await getUser(username);
    if (!user) {
      user = await createUser(username);
    }

    // 2. Poll until server is ready
    while (Date.now() - startTime < START_TIMEOUT) {
      const serverStatus = await getServerStatus(username);

      if (serverStatus?.ready) {
        logger.info(`Server for '${username}' is ready.`);
        return serverStatus; // ✅ Always return final server object
      }

      if (!serverStatus) {
        logger.info(`Server not found for '${username}'. Requesting start...`);
        await startServer(username);
      } else {
        logger.info(`Server for '${username}' is pending. Waiting...`);
      }

      await delay(POLL_INTERVAL);
    }

    throw new Error(`Server for ${username} did not become ready in time.`);
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message;
    logger.error(`JupyterHub API error for ${username}: ${errorMessage}`);
    throw new Error('Could not ensure Jupyter server is running.');
  }
};

// ------------------ Exports ------------------
const jupyterService = {
  jupyterhubApi,
  ensureServerIsRunning,
  getUser,
  createUser,
  getServerStatus,
  startServer,
  stopServer,
  getUserToken
};

export default jupyterService;
