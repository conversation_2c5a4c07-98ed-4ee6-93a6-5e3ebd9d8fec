import { User, Role, Permission, UserRole, RolePermission } from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';

class PermissionFeedbackService {
  
  /**
   * Get user's permissions with detailed information
   */
  async getUserPermissions(userId) {
    try {
      const user = await User.findByPk(userId, {
        include: [
          {
            model: Role,
            as: 'roles',
            include: [
              {
                model: Permission,
                as: 'permissions'
              }
            ]
          }
        ]
      });

      if (!user) {
        throw new Error('User not found');
      }

      const permissions = [];
      const roles = [];

      user.roles?.forEach(role => {
        roles.push({
          id: role.id,
          name: role.name,
          description: role.description,
          priority: role.priority,
          isSystemRole: role.is_system_role
        });

        role.permissions?.forEach(permission => {
          const existingPermission = permissions.find(p => p.key === permission.key);
          
          if (!existingPermission) {
            permissions.push({
              key: permission.key,
              name: permission.name,
              description: permission.description,
              category: permission.category,
              isSystemPermission: permission.is_system_permission,
              grantedBy: [role.name],
              roleIds: [role.id]
            });
          } else {
            existingPermission.grantedBy.push(role.name);
            existingPermission.roleIds.push(role.id);
          }
        });
      });

      return {
        userId: user.id,
        email: user.email,
        roles,
        permissions,
        totalPermissions: permissions.length,
        totalRoles: roles.length
      };
    } catch (error) {
      logger.error('Error getting user permissions:', error);
      throw error;
    }
  }

  /**
   * Get user's permissions for specific resource
   */
  async getUserResourcePermissions(userId, resourceType, resourceId = null) {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      
      // Filter permissions by resource type
      const resourcePermissions = userPermissions.permissions.filter(
        permission => permission.category === resourceType
      );

      // Get additional context-specific permissions
      let contextPermissions = [];
      
      if (resourceId) {
        contextPermissions = await this.getContextSpecificPermissions(userId, resourceType, resourceId);
      }

      return {
        userId,
        resourceType,
        resourceId,
        permissions: resourcePermissions,
        contextPermissions,
        hasAccess: resourcePermissions.length > 0 || contextPermissions.length > 0,
        accessLevel: this.determineAccessLevel(resourcePermissions, contextPermissions)
      };
    } catch (error) {
      logger.error('Error getting user resource permissions:', error);
      throw error;
    }
  }

  /**
   * Get context-specific permissions (e.g., project-specific permissions)
   */
  async getContextSpecificPermissions(userId, resourceType, resourceId) {
    try {
      let contextPermissions = [];

      if (resourceType === 'project') {
        const { Project, ProjectAssignment } = await import('../models/associations.js');
        
        const project = await Project.findByPk(resourceId, {
          include: [
            {
              model: ProjectAssignment,
              as: 'assignments',
              where: { user_id: userId, is_active: true },
              required: false
            }
          ]
        });

        if (project) {
          // Check if user is creator
          if (project.created_by === userId) {
            contextPermissions.push({
              key: 'project:full_access',
              name: 'Full Project Access',
              description: 'Creator has full access to this project',
              category: 'project',
              context: 'creator',
              scope: 'project'
            });
          }

          // Check project assignments
          if (project.assignments && project.assignments.length > 0) {
            project.assignments.forEach(assignment => {
              if (assignment.role === 'instructor') {
                contextPermissions.push({
                  key: 'project:instructor_access',
                  name: 'Instructor Access',
                  description: 'Instructor access to this project',
                  category: 'project',
                  context: 'instructor',
                  scope: 'project'
                });
              } else if (assignment.role === 'ta') {
                contextPermissions.push({
                  key: 'project:ta_access',
                  name: 'TA Access',
                  description: 'Teaching Assistant access to this project',
                  category: 'project',
                  context: 'ta',
                  scope: 'project'
                });
              }
            });
          }
        }
      }

      return contextPermissions;
    } catch (error) {
      logger.error('Error getting context-specific permissions:', error);
      return [];
    }
  }

  /**
   * Determine user's access level based on permissions
   */
  determineAccessLevel(permissions, contextPermissions = []) {
    const allPermissions = [...permissions, ...contextPermissions];
    
    if (allPermissions.some(p => p.key.includes('full_access') || p.key.includes('admin'))) {
      return 'full';
    } else if (allPermissions.some(p => p.key.includes('create') || p.key.includes('manage'))) {
      return 'manage';
    } else if (allPermissions.some(p => p.key.includes('read') || p.key.includes('view'))) {
      return 'read';
    } else {
      return 'none';
    }
  }

  /**
   * Get available actions for user based on permissions
   */
  async getAvailableActions(userId, resourceType, resourceId = null) {
    try {
      const resourcePermissions = await this.getUserResourcePermissions(userId, resourceType, resourceId);
      
      const actions = [];

      // Map permissions to actions
      resourcePermissions.permissions.forEach(permission => {
        switch (permission.key) {
          case 'project:create':
            actions.push({
              action: 'create_project',
              label: 'Create Project',
              description: 'Create a new project',
              icon: 'plus',
              category: 'project_management'
            });
            break;
          case 'project:read':
            actions.push({
              action: 'view_project',
              label: 'View Project',
              description: 'View project details',
              icon: 'eye',
              category: 'project_viewing'
            });
            break;
          case 'project:update':
            actions.push({
              action: 'edit_project',
              label: 'Edit Project',
              description: 'Modify project details',
              icon: 'edit',
              category: 'project_management'
            });
            break;
          case 'project:publish':
            actions.push({
              action: 'publish_project',
              label: 'Publish Project',
              description: 'Make project available to students',
              icon: 'publish',
              category: 'project_management'
            });
            break;
          case 'project:assign_users':
            actions.push({
              action: 'assign_users',
              label: 'Assign Users',
              description: 'Assign team members to project',
              icon: 'users',
              category: 'team_management'
            });
            break;
          case 'project_template:create':
            actions.push({
              action: 'create_template',
              label: 'Create Template',
              description: 'Create project template',
              icon: 'template',
              category: 'template_management'
            });
            break;
          case 'project_template:read':
            actions.push({
              action: 'view_templates',
              label: 'View Templates',
              description: 'Browse project templates',
              icon: 'templates',
              category: 'template_viewing'
            });
            break;
        }
      });

      // Add context-specific actions
      resourcePermissions.contextPermissions.forEach(permission => {
        if (permission.key === 'project:full_access') {
          actions.push({
            action: 'delete_project',
            label: 'Delete Project',
            description: 'Permanently remove project',
            icon: 'delete',
            category: 'project_management',
            context: 'creator_only'
          });
        }
      });

      return {
        userId,
        resourceType,
        resourceId,
        actions,
        totalActions: actions.length,
        accessLevel: resourcePermissions.accessLevel,
        hasActions: actions.length > 0
      };
    } catch (error) {
      logger.error('Error getting available actions:', error);
      throw error;
    }
  }

  /**
   * Get permission summary for UI display
   */
  async getPermissionSummary(userId) {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      
      // Group permissions by category
      const permissionsByCategory = {};
      userPermissions.permissions.forEach(permission => {
        if (!permissionsByCategory[permission.category]) {
          permissionsByCategory[permission.category] = [];
        }
        permissionsByCategory[permission.category].push(permission);
      });

      // Calculate summary statistics
      const summary = {
        userId: userPermissions.userId,
        totalPermissions: userPermissions.totalPermissions,
        totalRoles: userPermissions.totalRoles,
        categories: Object.keys(permissionsByCategory),
        permissionsByCategory,
        primaryRole: userPermissions.roles[0]?.name || 'No Role',
        hasAdminAccess: userPermissions.permissions.some(p => p.key.includes('admin')),
        hasProjectAccess: userPermissions.permissions.some(p => p.category === 'project'),
        hasCourseAccess: userPermissions.permissions.some(p => p.category === 'course'),
        hasTemplateAccess: userPermissions.permissions.some(p => p.category === 'project_template')
      };

      return summary;
    } catch (error) {
      logger.error('Error getting permission summary:', error);
      throw error;
    }
  }

  /**
   * Check if user can perform specific action
   */
  async canPerformAction(userId, action, resourceType, resourceId = null) {
    try {
      const availableActions = await this.getAvailableActions(userId, resourceType, resourceId);
      
      const canPerform = availableActions.actions.some(
        availableAction => availableAction.action === action
      );

      return {
        canPerform,
        action,
        resourceType,
        resourceId,
        availableActions: availableActions.actions,
        accessLevel: availableActions.accessLevel
      };
    } catch (error) {
      logger.error('Error checking action permission:', error);
      return {
        canPerform: false,
        action,
        resourceType,
        resourceId,
        error: error.message
      };
    }
  }

  /**
   * Get permission requirements for specific action
   */
  getPermissionRequirements(action) {
    const requirements = {
      create_project: ['project:create'],
      edit_project: ['project:update'],
      delete_project: ['project:delete'],
      publish_project: ['project:publish'],
      assign_users: ['project:assign_users'],
      create_template: ['project_template:create'],
      view_templates: ['project_template:read'],
      edit_template: ['project_template:update'],
      delete_template: ['project_template:delete'],
      rate_template: ['project_template:rate']
    };

    return requirements[action] || [];
  }
}

export default new PermissionFeedbackService();
