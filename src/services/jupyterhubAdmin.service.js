import axios from 'axios';
import logger from '../config/logger.config.js';

class JupyterhubAdminService {
  constructor() {
    this.baseUrl = process.env.JUPYTERHUB_URL || 'http://localhost:8000';
    this.adminToken = process.env.JUPYTERHUB_TOKEN;
    this.apiUrl = `${this.baseUrl}/hub/api`;
  }

  /**
   * Create a new user in JupyterHub
   */
  async createUser(username, email, options = {}) {
    try {
      const { admin = false, serverName = null } = options;
      
      const userData = {
        name: username,
        admin: admin
      };

      if (email) {
        userData.email = email;
      }

      const response = await axios.post(`${this.apiUrl}/users`, userData, {
        headers: {
          'Authorization': `token ${this.adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      logger.info(`JupyterHub user created: ${username}`);
      
      return {
        success: true,
        user: response.data,
        message: `User ${username} created successfully`
      };

    } catch (error) {
      logger.error('JupyterHub user creation error:', error);
      throw new Error(`Failed to create JupyterHub user: ${error.message}`);
    }
  }

  /**
   * Delete a user from JupyterHub
   */
  async deleteUser(username) {
    try {
      await axios.delete(`${this.apiUrl}/users/${username}`, {
        headers: {
          'Authorization': `token ${this.adminToken}`
        }
      });

      logger.info(`JupyterHub user deleted: ${username}`);
      
      return {
        success: true,
        message: `User ${username} deleted successfully`
      };

    } catch (error) {
      logger.error('JupyterHub user deletion error:', error);
      throw new Error(`Failed to delete JupyterHub user: ${error.message}`);
    }
  }

  /**
   * Get user information from JupyterHub
   */
  async getUser(username) {
    try {
      const response = await axios.get(`${this.apiUrl}/users/${username}`, {
        headers: {
          'Authorization': `token ${this.adminToken}`
        }
      });

      return {
        success: true,
        user: response.data
      };

    } catch (error) {
      if (error.response && error.response.status === 404) {
        return {
          success: false,
          message: `User ${username} not found`
        };
      }
      
      logger.error('JupyterHub user retrieval error:', error);
      throw new Error(`Failed to get JupyterHub user: ${error.message}`);
    }
  }

  /**
   * Get all users from JupyterHub
   */
  async getAllUsers() {
    try {
      const response = await axios.get(`${this.apiUrl}/users`, {
        headers: {
          'Authorization': `token ${this.adminToken}`
        }
      });

      return {
        success: true,
        users: response.data
      };

    } catch (error) {
      logger.error('JupyterHub users retrieval error:', error);
      throw new Error(`Failed to get JupyterHub users: ${error.message}`);
    }
  }

  /**
   * Start a user's server
   */
  async startServer(username, serverName = null) {
    try {
      const serverPath = serverName ? `/${serverName}` : '';
      const response = await axios.post(`${this.apiUrl}/users/${username}/servers${serverPath}`, {}, {
        headers: {
          'Authorization': `token ${this.adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      logger.info(`JupyterHub server started for user: ${username}`);
      
      return {
        success: true,
        server: response.data,
        message: `Server started for user ${username}`
      };

    } catch (error) {
      logger.error('JupyterHub server start error:', error);
      throw new Error(`Failed to start JupyterHub server: ${error.message}`);
    }
  }

  /**
   * Stop a user's server
   */
  async stopServer(username, serverName = null) {
    try {
      const serverPath = serverName ? `/${serverName}` : '';
      await axios.delete(`${this.apiUrl}/users/${username}/servers${serverPath}`, {
        headers: {
          'Authorization': `token ${this.adminToken}`
        }
      });

      logger.info(`JupyterHub server stopped for user: ${username}`);
      
      return {
        success: true,
        message: `Server stopped for user ${username}`
      };

    } catch (error) {
      logger.error('JupyterHub server stop error:', error);
      throw new Error(`Failed to stop JupyterHub server: ${error.message}`);
    }
  }

  /**
   * Get server status for a user
   */
  async getServerStatus(username, serverName = null) {
    try {
      const serverPath = serverName ? `/${serverName}` : '';
      const response = await axios.get(`${this.apiUrl}/users/${username}/servers${serverPath}`, {
        headers: {
          'Authorization': `token ${this.adminToken}`
        }
      });

      return {
        success: true,
        status: response.data
      };

    } catch (error) {
      if (error.response && error.response.status === 404) {
        return {
          success: false,
          status: 'not_found',
          message: `Server not found for user ${username}`
        };
      }
      
      logger.error('JupyterHub server status error:', error);
      throw new Error(`Failed to get JupyterHub server status: ${error.message}`);
    }
  }

  /**
   * Add user to a group
   */
  async addUserToGroup(username, groupName) {
    try {
      const response = await axios.post(`${this.apiUrl}/groups/${groupName}/users`, {
        users: [username]
      }, {
        headers: {
          'Authorization': `token ${this.adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      logger.info(`User ${username} added to group ${groupName}`);
      
      return {
        success: true,
        message: `User ${username} added to group ${groupName}`
      };

    } catch (error) {
      logger.error('JupyterHub add user to group error:', error);
      throw new Error(`Failed to add user to group: ${error.message}`);
    }
  }

  /**
   * Remove user from a group
   */
  async removeUserFromGroup(username, groupName) {
    try {
      await axios.delete(`${this.apiUrl}/groups/${groupName}/users/${username}`, {
        headers: {
          'Authorization': `token ${this.adminToken}`
        }
      });

      logger.info(`User ${username} removed from group ${groupName}`);
      
      return {
        success: true,
        message: `User ${username} removed from group ${groupName}`
      };

    } catch (error) {
      logger.error('JupyterHub remove user from group error:', error);
      throw new Error(`Failed to remove user from group: ${error.message}`);
    }
  }

  /**
   * Create a new group
   */
  async createGroup(groupName, options = {}) {
    try {
      const { users = [] } = options;
      
      const groupData = {
        name: groupName,
        users: users
      };

      const response = await axios.post(`${this.apiUrl}/groups`, groupData, {
        headers: {
          'Authorization': `token ${this.adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      logger.info(`JupyterHub group created: ${groupName}`);
      
      return {
        success: true,
        group: response.data,
        message: `Group ${groupName} created successfully`
      };

    } catch (error) {
      logger.error('JupyterHub group creation error:', error);
      throw new Error(`Failed to create JupyterHub group: ${error.message}`);
    }
  }

  /**
   * Delete a group
   */
  async deleteGroup(groupName) {
    try {
      await axios.delete(`${this.apiUrl}/groups/${groupName}`, {
        headers: {
          'Authorization': `token ${this.adminToken}`
        }
      });

      logger.info(`JupyterHub group deleted: ${groupName}`);
      
      return {
        success: true,
        message: `Group ${groupName} deleted successfully`
      };

    } catch (error) {
      logger.error('JupyterHub group deletion error:', error);
      throw new Error(`Failed to delete JupyterHub group: ${error.message}`);
    }
  }

  /**
   * Get all groups
   */
  async getAllGroups() {
    try {
      const response = await axios.get(`${this.apiUrl}/groups`, {
        headers: {
          'Authorization': `token ${this.adminToken}`
        }
      });

      return {
        success: true,
        groups: response.data
      };

    } catch (error) {
      logger.error('JupyterHub groups retrieval error:', error);
      throw new Error(`Failed to get JupyterHub groups: ${error.message}`);
    }
  }

  /**
   * Check if JupyterHub is accessible
   */
  async healthCheck() {
    try {
      const response = await axios.get(`${this.apiUrl}/info`, {
        headers: {
          'Authorization': `token ${this.adminToken}`
        },
        timeout: 5000
      });

      return {
        success: true,
        status: 'healthy',
        info: response.data
      };

    } catch (error) {
      logger.error('JupyterHub health check failed:', error);
      return {
        success: false,
        status: 'unhealthy',
        error: error.message
      };
    }
  }
}

export default new JupyterhubAdminService();
