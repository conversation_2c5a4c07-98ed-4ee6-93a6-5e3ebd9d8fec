import { Course, CourseEnrollment, User, Role } from '../models/associations.js';
import logger from '../config/logger.config.js';
import { Op, Sequelize } from "sequelize";
import ApiError from '../utils/ApiError.utils.js';
import httpStatus from 'http-status';

class CourseRoleService {

  /**
   * Get user's role in a specific course
   */
  async getUserCourseRole(userId, courseId) {
    try {
      const enrollment = await CourseEnrollment.findOne({
        where: {
          user_id: userId,
          course_id: courseId,
          enrollment_status: 'active'
        },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email']
          }
        ]
      });

      if (!enrollment) {
        return null;
      }

      return {
        role: enrollment.role_in_course,
        status: enrollment.enrollment_status,
        enrolledAt: enrollment.enrolled_at,
        user: enrollment.user
      };
    } catch (error) {
      logger.error('Error getting user course role:', error);
      throw error;
    }
  }

  /**
   * Check if user has instructor/TA role in a course
   */
  async hasTeachingRole(userId, courseId) {
    try {
      const role = await this.getUserCourseRole(userId, courseId);

      if (!role) {
        return false;
      }

      // Check if user has teaching role (instructor or TA)
      return ['instructor', 'ta'].includes(role.role);
    } catch (error) {
      logger.error('Error checking teaching role:', error);
      throw error;
    }
  }

  /**
   * Check if user is the primary instructor of a course
   */
  async isPrimaryInstructor(userId, courseId) {
    try {
      const course = await Course.findByPk(courseId);

      if (!course) {
        return false;
      }

      return course.instructor_id === userId;
    } catch (error) {
      logger.error('Error checking primary instructor:', error);
      throw error;
    }
  }

  /**
   * Get all users with teaching roles in a course
   */
  async getCourseTeachingStaff(courseId) {
    try {
      const enrollments = await CourseEnrollment.findAll({
        where: {
          course_id: courseId,
          enrollment_status: 'active',
          role_in_course: {
            [Op.in]: ['instructor', 'ta']
          }
        },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'profile_picture']
          }
        ],
        order: [
          // instructor first, then TA, then by enrolled_at
          [Sequelize.literal(`CASE WHEN role_in_course = 'instructor' THEN 0 ELSE 1 END`), 'ASC'],
          ['enrolled_at', 'ASC']
        ]
      });

      if(!enrollments || enrollments.length === 0) throw new ApiError(httpStatus.NOT_FOUND, 'No teaching staff found for this course');

      const result = { instructors: [], tas: [] };

      for (const e of enrollments) {
        const dto = {
          id: e.user.id,
          name: e.user.name,
          email: e.user.email,
          profilePicture: e.user.profile_picture,
          role: e.role_in_course,
          enrolledAt: e.enrolled_at
        };

        const role = String(e.role_in_course || '').toLowerCase();
        if (role === 'instructor') result.instructors.push(dto);
        else result.tas.push(dto); // treat anything else in the filter as TA
      }

      // (optional) extra safety: ensure each group is sorted by enrolledAt
      result.instructors.sort((a, b) => new Date(a.enrolledAt) - new Date(b.enrolledAt));
      result.tas.sort((a, b) => new Date(a.enrolledAt) - new Date(b.enrolledAt));

      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Validate user can create projects in a course
   */
  async canCreateProjects(userId, courseId) {
    try {
      // Check if user has teaching role
      const hasTeachingRole = await this.hasTeachingRole(userId, courseId);

      if (!hasTeachingRole) {
        return {
          canCreate: false,
          reason: 'User does not have teaching role in this course',
          userRole: null
        };
      }

      // Get user's specific role
      const userRole = await this.getUserCourseRole(userId, courseId);

      return {
        canCreate: true,
        reason: 'User has teaching role in this course',
        userRole: userRole.role
      };
    } catch (error) {
      logger.error('Error validating project creation permission:', error);
      throw error;
    }
  }

  /**
   * Get courses where user has teaching role
   */
  async getTeachingCourses(userId) {
    try {
      const enrollments = await CourseEnrollment.findAll({
        where: {
          user_id: userId,
          enrollment_status: 'active',
          role_in_course: {
            [Op.in]: ['instructor', 'ta']
          }
        },
        include: [
          {
            model: Course,
            as: 'course',
            where: {
              status: 'active'
            },
            attributes: ['id', 'name', 'code', 'term', 'academic_year', 'start_date', 'end_date']
          }
        ],
        order: [
          [{ model: Course, as: 'course' }, 'start_date', 'DESC']
        ]
      });

      return enrollments.map(enrollment => ({
        id: enrollment.course.id,
        name: enrollment.course.name,
        code: enrollment.course.code,
        term: enrollment.course.term,
        academicYear: enrollment.course.academic_year,
        startDate: enrollment.course.start_date,
        endDate: enrollment.course.end_date,
        userRole: enrollment.role_in_course,
        isPrimaryInstructor: enrollment.course.instructor_id === userId
      }));
    } catch (error) {
      logger.error('Error getting teaching courses:', error);
      throw error;
    }
  }

  /**
   * Get courses where user is primary instructor
   */
  async getPrimaryInstructorCourses(userId) {
    try {
      const courses = await Course.findAll({
        where: {
          instructor_id: userId,
          status: 'active'
        },
        attributes: ['id', 'name', 'code', 'term', 'academic_year', 'start_date', 'end_date'],
        order: [['start_date', 'DESC']]
      });

      return courses.map(course => ({
        id: course.id,
        name: course.name,
        code: course.code,
        term: course.term,
        academicYear: course.academic_year,
        startDate: course.start_date,
        endDate: course.end_date,
        userRole: 'instructor',
        isPrimaryInstructor: true
      }));
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all courses where user can create projects
   */
  async getProjectCreationCourses(userId) {
    try {
      // Get courses where user is primary instructor
      const primaryInstructorCourses = await this.getPrimaryInstructorCourses(userId);

      // Get courses where user has TA role
      const taCourses = await this.getTeachingCourses(userId);

      // Combine and deduplicate
      const allCourses = [...primaryInstructorCourses];

      taCourses.forEach(taCourse => {
        const exists = allCourses.find(course => course.id === taCourse.id);
        if (!exists) {
          allCourses.push(taCourse);
        }
      });

      // Sort by start date (most recent first)
      return allCourses.sort((a, b) => new Date(b.startDate) - new Date(a.startDate));
    } catch (error) {
      throw error;
    }
  }

  /**
   * Validate user can manage a specific project
   */
  async canManageProject(userId, projectId) {
    try {
      const { Project } = await import('../models/associations.js');

      const project = await Project.findByPk(projectId, {
        include: [
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'instructor_id']
          }
        ]
      });

      if (!project) {
        return {
          canManage: false,
          reason: 'Project not found'
        };
      }

      // Check if user is primary instructor
      const isPrimaryInstructor = project.course.instructor_id === userId;

      if (isPrimaryInstructor) {
        return {
          canManage: true,
          reason: 'User is primary instructor of the course',
          permissionLevel: 'full'
        };
      }

      // Check if user has TA role
      const hasTeachingRole = await this.hasTeachingRole(userId, project.course.id);

      if (hasTeachingRole) {
        return {
          canManage: true,
          reason: 'User has teaching role in the course',
          permissionLevel: 'limited'
        };
      }

      return {
        canManage: false,
        reason: 'User does not have teaching role in this course'
      };
    } catch (error) {
      logger.error('Error validating project management permission:', error);
      throw error;
    }
  }
}

export default new CourseRoleService();
