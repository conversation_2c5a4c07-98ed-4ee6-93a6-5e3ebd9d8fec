import { Message, User, Course, Project, CourseEnrollment } from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';

class MessageService {
  
  /**
   * Send a new message
   */
  async sendMessage(messageData) {
    try {
      const message = await Message.create(messageData);
      
      logger.info(`Message sent: ${message.subject} from ${messageData.sender_id} to ${messageData.recipient_id}`);
      
      return message;
    } catch (error) {
      logger.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Reply to a message
   */
  async replyToMessage(parentMessageId, replyData, senderId) {
    try {
      const parentMessage = await Message.findByPk(parentMessageId);
      
      if (!parentMessage) {
        throw new Error('Parent message not found');
      }

      // Generate thread ID if not exists
      const threadId = parentMessage.thread_id || parentMessage.id;

      const reply = await Message.create({
        ...replyData,
        sender_id: senderId,
        parent_message_id: parentMessageId,
        thread_id: threadId,
        recipient_id: parentMessage.sender_id // Reply to sender
      });
      
      logger.info(`Reply sent: ${reply.subject} to message ${parentMessageId} by user ${senderId}`);
      
      return reply;
    } catch (error) {
      logger.error('Error sending reply:', error);
      throw error;
    }
  }

  /**
   * Get user's inbox (received messages)
   */
  async getInbox(userId, options = {}) {
    try {
      const {
        status = 'all',
        messageType,
        priority,
        isRead,
        page = 1,
        limit = 20,
        courseId = null,
        projectId = null
      } = options;

      const whereClause = { recipient_id: userId };
      
      if (status !== 'all') {
        whereClause.status = status;
      }
      
      if (messageType) {
        whereClause.message_type = messageType;
      }
      
      if (priority) {
        whereClause.priority = priority;
      }
      
      if (isRead !== undefined) {
        whereClause.is_read = isRead;
      }

      if (courseId) {
        whereClause.course_id = courseId;
      }

      if (projectId) {
        whereClause.project_id = projectId;
      }

      const offset = (page - 1) * limit;
      
      const { count, rows: messages } = await Message.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'sender',
            attributes: ['id', 'name', 'email']
          },
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'name', 'code'],
            required: false
          },
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title'],
            required: false
          }
        ],
        order: [['created_at', 'DESC']],
        limit,
        offset
      });

      return {
        messages,
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting inbox:', error);
      throw error;
    }
  }

  /**
   * Get user's sent messages
   */
  async getSentMessages(userId, options = {}) {
    try {
      const {
        messageType,
        priority,
        page = 1,
        limit = 20,
        courseId = null,
        projectId = null
      } = options;

      const whereClause = { sender_id: userId };
      
      if (messageType) {
        whereClause.message_type = messageType;
      }
      
      if (priority) {
        whereClause.priority = priority;
      }

      if (courseId) {
        whereClause.course_id = courseId;
      }

      if (projectId) {
        whereClause.project_id = projectId;
      }

      const offset = (page - 1) * limit;
      
      const { count, rows: messages } = await Message.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'recipient',
            attributes: ['id', 'name', 'email']
          },
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'name', 'code'],
            required: false
          },
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title'],
            required: false
          }
        ],
        order: [['created_at', 'DESC']],
        limit,
        offset
      });

      return {
        messages,
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting sent messages:', error);
      throw error;
    }
  }

  /**
   * Get conversation thread
   */
  async getConversationThread(threadId, userId) {
    try {
      const messages = await Message.findAll({
        where: { thread_id: threadId },
        include: [
          {
            model: User,
            as: 'sender',
            attributes: ['id', 'name', 'email']
          },
          {
            model: User,
            as: 'recipient',
            attributes: ['id', 'name', 'email']
          },
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'name', 'code'],
            required: false
          },
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title'],
            required: false
          }
        ],
        order: [['created_at', 'ASC']]
      });

      // Check if user is part of this conversation
      const isParticipant = messages.some(msg => 
        msg.sender_id === userId || msg.recipient_id === userId
      );

      if (!isParticipant) {
        throw new Error('Access denied: You are not part of this conversation');
      }

      return messages;
    } catch (error) {
      logger.error('Error getting conversation thread:', error);
      throw error;
    }
  }

  /**
   * Mark message as read
   */
  async markAsRead(messageId, userId) {
    try {
      const message = await Message.findByPk(messageId);
      
      if (!message) {
        throw new Error('Message not found');
      }

      // Check if user is the recipient
      if (message.recipient_id !== userId) {
        throw new Error('Access denied: You can only mark your own messages as read');
      }

      await message.update({
        is_read: true,
        read_at: new Date()
      });
      
      logger.info(`Message marked as read: ${messageId} by user ${userId}`);
      
      return message;
    } catch (error) {
      logger.error('Error marking message as read:', error);
      throw error;
    }
  }

  /**
   * Mark multiple messages as read
   */
  async markMultipleAsRead(messageIds, userId) {
    try {
      const messages = await Message.findAll({
        where: {
          id: { [Op.in]: messageIds },
          recipient_id: userId
        }
      });

      const updatePromises = messages.map(message =>
        message.update({
          is_read: true,
          read_at: new Date()
        })
      );

      await Promise.all(updatePromises);
      
      logger.info(`Marked ${messages.length} messages as read by user ${userId}`);
      
      return { success: true, count: messages.length };
    } catch (error) {
      logger.error('Error marking multiple messages as read:', error);
      throw error;
    }
  }

  /**
   * Get message by ID
   */
  async getMessageById(messageId, userId) {
    try {
      const message = await Message.findByPk(messageId, {
        include: [
          {
            model: User,
            as: 'sender',
            attributes: ['id', 'name', 'email']
          },
          {
            model: User,
            as: 'recipient',
            attributes: ['id', 'name', 'email']
          },
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'name', 'code'],
            required: false
          },
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title'],
            required: false
          }
        ]
      });

      if (!message) {
        throw new Error('Message not found');
      }

      // Check if user is sender or recipient
      if (message.sender_id !== userId && message.recipient_id !== userId) {
        throw new Error('Access denied: You can only view your own messages');
      }

      // Mark as read if user is recipient
      if (message.recipient_id === userId && !message.is_read) {
        await this.markAsRead(messageId, userId);
      }

      return message;
    } catch (error) {
      logger.error('Error getting message by ID:', error);
      throw error;
    }
  }

  /**
   * Update message
   */
  async updateMessage(messageId, updateData, userId) {
    try {
      const message = await Message.findByPk(messageId);
      
      if (!message) {
        throw new Error('Message not found');
      }

      // Check if user is the sender
      if (message.sender_id !== userId) {
        throw new Error('Access denied: You can only edit your own messages');
      }

      // Only allow editing certain fields
      const allowedUpdates = ['subject', 'content', 'attachments'];
      const filteredUpdates = {};
      
      allowedUpdates.forEach(field => {
        if (updateData[field] !== undefined) {
          filteredUpdates[field] = updateData[field];
        }
      });

      await message.update(filteredUpdates);
      
      logger.info(`Message updated: ${messageId} by user ${userId}`);
      
      return message;
    } catch (error) {
      logger.error('Error updating message:', error);
      throw error;
    }
  }

  /**
   * Delete message
   */
  async deleteMessage(messageId, userId) {
    try {
      const message = await Message.findByPk(messageId);
      
      if (!message) {
        throw new Error('Message not found');
      }

      // Check if user is the sender
      if (message.sender_id !== userId) {
        throw new Error('Access denied: You can only delete your own messages');
      }

      await message.destroy();
      
      logger.info(`Message deleted: ${messageId} by user ${userId}`);
      
      return { success: true, message: 'Message deleted successfully' };
    } catch (error) {
      logger.error('Error deleting message:', error);
      throw error;
    }
  }

  /**
   * Get unread message count for user
   */
  async getUnreadCount(userId) {
    try {
      const count = await Message.count({
        where: {
          recipient_id: userId,
          is_read: false
        }
      });

      return count;
    } catch (error) {
      logger.error('Error getting unread count:', error);
      throw error;
    }
  }

  /**
   * Get message statistics for instructor dashboard
   */
  async getInstructorMessageStats(instructorId) {
    try {
      const stats = {
        totalSent: 0,
        totalReceived: 0,
        unreadCount: 0,
        courseRelated: 0,
        projectRelated: 0
      };

      // Get counts
      stats.totalSent = await Message.count({
        where: { sender_id: instructorId }
      });

      stats.totalReceived = await Message.count({
        where: { recipient_id: instructorId }
      });

      stats.unreadCount = await Message.count({
        where: {
          recipient_id: instructorId,
          is_read: false
        }
      });

      stats.courseRelated = await Message.count({
        where: {
          [Op.or]: [
            { sender_id: instructorId },
            { recipient_id: instructorId }
          ],
          course_id: { [Op.ne]: null }
        }
      });

      stats.projectRelated = await Message.count({
        where: {
          [Op.or]: [
            { sender_id: instructorId },
            { recipient_id: instructorId }
          ],
          project_id: { [Op.ne]: null }
        }
      });

      return stats;
    } catch (error) {
      logger.error('Error getting instructor message stats:', error);
      throw error;
    }
  }

  /**
   * Send course-wide announcement message
   */
  async sendCourseAnnouncement(courseId, senderId, messageData) {
    try {
      // Get all enrolled students in the course
      const enrollments = await CourseEnrollment.findAll({
        where: { course_id: courseId, status: 'active' },
        attributes: ['user_id']
      });

      const studentIds = enrollments.map(e => e.user_id);
      
      if (studentIds.length === 0) {
        throw new Error('No students enrolled in this course');
      }

      // Create messages for all students
      const messagePromises = studentIds.map(studentId =>
        Message.create({
          ...messageData,
          sender_id: senderId,
          recipient_id: studentId,
          message_type: 'course_related',
          course_id: courseId
        })
      );

      const messages = await Promise.all(messagePromises);
      
      logger.info(`Course announcement sent: ${messageData.subject} to ${studentIds.length} students by user ${senderId}`);
      
      return { success: true, count: messages.length };
    } catch (error) {
      logger.error('Error sending course announcement:', error);
      throw error;
    }
  }
}

export default new MessageService();
