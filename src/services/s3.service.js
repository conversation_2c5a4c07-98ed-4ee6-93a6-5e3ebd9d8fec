import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import logger from '../config/logger.config.js';
import { checkCourseProjectId, updateDataSetS3Url } from './project.service.js';


class S3Service {
  constructor() {
    this.client = new S3Client({
      region: process.env.AWS_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      }
    });

    this.bucket = process.env.AWS_S3_BUCKET || 'bits-dataScience-platform';
    this.baseUrl = `https://${this.bucket}.s3.${process.env.AWS_REGION || 'us-east-1'}.amazonaws.com`;
  }

  /**
   * Generate a unique file path for S3
   */
  generateFilePath(type, userId, courseId = null, projectId = null, filename = null) {
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const uuid = uuidv4().split('-')[0]; // Short UUID

    let basePath = '';

    switch (type) {
      case 'project-template':
        basePath = `projects/${courseId}/${projectId}/templates`;
        break;
      case 'project-dataset':
        basePath = `projects/${courseId}/${projectId}/datasets`;
        break;
      case 'submission':
        basePath = `submissions/${courseId}/${projectId}/${userId}/${timestamp}`;
        break;
      case 'user-notebook':
        basePath = `notebooks/${userId}/${timestamp}`;
        break;
      case 'profile-picture':
        basePath = `profiles/${userId}`;
        break;
      case 'course-material':
        basePath = `courses/${courseId}/materials`;
        break;
      default:
        basePath = `misc/${timestamp}`;
    }

    const extension = filename ? path.extname(filename) : '';
    const finalFilename = filename || `${uuid}${extension}`;

    return `${basePath}/${finalFilename}`;
  }

  /**
   * Upload file to S3
   */
  async uploadFile(file, type, userId, options = {}) {
    try {
      const { courseId, projectId, customFilename } = options;

      await checkCourseProjectId(courseId, projectId);

      // Generate file path
      const filePath = this.generateFilePath(
        type,
        userId,
        courseId,
        projectId,
        customFilename || file.originalname
      );

      // Prepare upload parameters
      const uploadParams = {
        Bucket: this.bucket,
        Key: filePath,
        Body: file.buffer,
        ContentType: file.mimetype,
        Metadata: {
          uploadedBy: userId,
          uploadType: type,
          originalName: file.originalname,
          uploadTimestamp: new Date().toISOString()
        }
      };

      // Add additional metadata if provided
      if (courseId) uploadParams.Metadata.courseId = courseId;
      if (projectId) uploadParams.Metadata.projectId = projectId;

      // Execute upload
      const command = new PutObjectCommand(uploadParams);
      const result = await this.client.send(command);

      const fileUrl = `${this.baseUrl}/${filePath}`;

      logger.info(`File uploaded successfully: ${filePath}`);

      await updateDataSetS3Url(filePath, projectId);

      const resultData = {
        url: fileUrl,
        key: filePath,
        bucket: this.bucket,
        size: file.size,
        contentType: file.mimetype,
        etag: result.ETag
      };

      const downloadUrl = await this.generatePresignedDownloadUrl(resultData.key);

      return { downloadUrl };
    } catch (error) {
      logger.error('S3 upload error:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Generate presigned URL for direct upload
   */
  async generatePresignedUploadUrl(type, userId, options = {}) {
    try {
      const { courseId, projectId, filename, contentType, expiresIn = 3600 } = options;

      const filePath = this.generateFilePath(type, userId, courseId, projectId, filename);

      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: filePath,
        ContentType: contentType,
        Metadata: {
          uploadedBy: userId,
          uploadType: type,
          uploadTimestamp: new Date().toISOString(),
          ...(courseId && { courseId }),
          ...(projectId && { projectId })
        }
      });

      const presignedUrl = await getSignedUrl(this.client, command, { expiresIn });

      return {
        uploadUrl: presignedUrl,
        key: filePath,
        finalUrl: `${this.baseUrl}/${filePath}`
      };
      
    } catch (error) {
      logger.error('S3 presigned URL generation error:', error);
      throw new Error(`Failed to generate upload URL: ${error.message}`);
    }
  }

  /**
   * Generate presigned URL for download
   */
  async generatePresignedDownloadUrl(key, expiresIn = 3600) {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key
      });

      const presignedUrl = await getSignedUrl(this.client, command, { expiresIn });

      return presignedUrl;
      
    } catch (error) {
      logger.error('S3 download URL generation error:', error);
      throw new Error(`Failed to generate download URL: ${error.message}`);
    }
  }

  /**
   * Delete file from S3
   */
  async deleteFile(key) {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: key
      });

      await this.client.send(command);

      logger.info(`File deleted successfully: ${key}`);

      return { success: true, key };
    } catch (error) {
      logger.error('S3 delete error:', error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  /**
   * Check if file exists
   */
  async fileExists(key) {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucket,
        Key: key
      });

      await this.client.send(command);
      return true;
    } catch (error) {
      if (error.name === 'NotFound') {
        return false;
      }
      throw error;
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(key) {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucket,
        Key: key
      });

      const result = await this.client.send(command);

      return {
        size: result.ContentLength,
        contentType: result.ContentType,
        lastModified: result.LastModified,
        etag: result.ETag,
        metadata: result.Metadata
      };
    } catch (error) {
      logger.error('S3 metadata retrieval error:', error);
      throw new Error(`Failed to get file metadata: ${error.message}`);
    }
  }

  /**
   * Copy file within S3 (useful for duplicating projects)
   */
  async copyFile(sourceKey, destinationKey) {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: destinationKey,
        CopySource: `${this.bucket}/${sourceKey}`
      });

      await this.client.send(command);

      const newFileUrl = `${this.baseUrl}/${destinationKey}`;

      logger.info(`File copied successfully: ${sourceKey} -> ${destinationKey}`);

      return {
        url: newFileUrl,
        key: destinationKey
      };
    } catch (error) {
      logger.error('S3 copy error:', error);
      throw new Error(`Failed to copy file: ${error.message}`);
    }
  }

  /**
   * Extract S3 key from URL
   */
  extractKeyFromUrl(url) {
    if (!url || typeof url !== 'string') return null;

    // Remove base URL to get the key
    if (url.startsWith(this.baseUrl)) {
      return url.replace(`${this.baseUrl}/`, '');
    }

    // Handle different S3 URL formats
    const s3UrlPatterns = [
      new RegExp(`https://${this.bucket}\\.s3\\..*?\\.amazonaws\\.com/(.+)`),
      new RegExp(`https://s3\\..*?\\.amazonaws\\.com/${this.bucket}/(.+)`)
    ];

    for (const pattern of s3UrlPatterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }

    return null;
  }

  /**
   * Validate file type and size
   */
  validateFile(file, allowedTypes = [], maxSize = null) {
    const errors = [];

    // Check file type
    if (allowedTypes.length > 0) {
      const fileExtension = path.extname(file.originalname).toLowerCase();
      const mimeType = file.mimetype.toLowerCase();

      const isValidExtension = allowedTypes.some(type => 
          fileExtension === `.${type.toLowerCase()}` ||
          mimeType.includes(type.toLowerCase())
      );

      if (!isValidExtension) {
        errors.push(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`);
      }
    }

    // Check file size
    if (maxSize && file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      errors.push(`File size too large. Maximum size: ${maxSizeMB}MB`);
    }

    return errors;
  }
}

export default new S3Service();
