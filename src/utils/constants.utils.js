/**
 * Application Constants
 */

// Environment
export const ENV = {
  DEVELOPMENT: 'development',
  TEST: 'test',
  PRODUCTION: 'production'
};

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
};

// User Roles
export const ROLES = {
  ADMIN: 'admin',
  INSTRUCTOR: 'instructor',
  STUDENT: 'student',
  TA: 'ta'
};

// Permissions
export const PERMISSIONS = {
  // User management
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  
  // Course management
  COURSE_CREATE: 'course:create',
  COURSE_READ: 'course:read',
  COURSE_UPDATE: 'course:update',
  COURSE_DELETE: 'course:delete',
  COURSE_ENROLL: 'course:enroll',
  
  // Project management
  PROJECT_CREATE: 'project:create',
  PROJECT_READ: 'project:read',
  PROJECT_UPDATE: 'project:update',
  PROJECT_DELETE: 'project:delete',
  PROJECT_PUBLISH: 'project:publish',
  PROJECT_ASSIGN_USERS: 'project:assign_users',
  PROJECT_MANAGE_ASSIGNMENTS: 'project:manage_assignments',
  PROJECT_VIEW_ASSIGNMENTS: 'project:view_assignments',
  
  // Project template management
  PROJECT_TEMPLATE_CREATE: 'project_template:create',
  PROJECT_TEMPLATE_READ: 'project_template:read',
  PROJECT_TEMPLATE_UPDATE: 'project_template:update',
  PROJECT_TEMPLATE_DELETE: 'project_template:delete',
  PROJECT_TEMPLATE_RATE: 'project_template:rate',
  PROJECT_TEMPLATE_APPROVE: 'project_template:approve',
  PROJECT_TEMPLATE_FEATURE: 'project_template:feature',
  PROJECT_TEMPLATE_ARCHIVE: 'project_template:archive',
  PROJECT_TEMPLATE_DUPLICATE: 'project_template:duplicate',
  
  // Course role management
  COURSE_ROLE_VIEW: 'course_role:view',
  COURSE_ROLE_MANAGE: 'course_role:manage',
  
  // Submission management
  SUBMISSION_CREATE: 'submission:create',
  SUBMISSION_READ: 'submission:read',
  SUBMISSION_UPDATE: 'submission:update',
  SUBMISSION_DELETE: 'submission:delete',
  SUBMISSION_GRADE: 'submission:grade',
  
  // Grade management
  GRADE_CREATE: 'grade:create',
  GRADE_READ: 'grade:read',
  GRADE_UPDATE: 'grade:update',
  GRADE_DELETE: 'grade:delete',
  
  // Workspace management
  WORKSPACE_CREATE: 'workspace:create',
  WORKSPACE_READ: 'workspace:read',
  WORKSPACE_UPDATE: 'workspace:update',
  WORKSPACE_DELETE: 'workspace:delete',
  
  // Sandbox management
  SANDBOX_SETTINGS_CREATE: 'sandbox_settings:create',
  SANDBOX_SETTINGS_READ: 'sandbox_settings:read',
  SANDBOX_SETTINGS_UPDATE: 'sandbox_settings:update',
  SANDBOX_SETTINGS_DELETE: 'sandbox_settings:delete',
  
  // File management
  FILE_UPLOAD: 'file:upload',
  FILE_DOWNLOAD: 'file:download',
  FILE_DELETE: 'file:delete',
  
  // LTI management
  LTI_READ: 'lti:read',
  LTI_UPDATE: 'lti:update',
  
  // System management
  SYSTEM_READ: 'system:read',
  SYSTEM_UPDATE: 'system:update'
};

// Submission Status
export const SUBMISSION_STATUS = {
  IN_PROGRESS: 'in_progress',
  SUBMITTED: 'submitted',
  GRADING: 'grading',
  GRADED: 'graded',
  RETURNED: 'returned'
};

// Workspace Status
export const WORKSPACE_STATUS = {
  CREATING: 'creating',
  READY: 'ready',
  ERROR: 'error',
  ARCHIVED: 'archived'
};

// Sandbox Modes
export const SANDBOX_MODES = {
  UV: 'uv',
  DOCKER: 'docker',
  KUBERNETES: 'kubernetes',
  LOCAL: 'local'
};

// Network Policies
export const NETWORK_POLICIES = {
  RESTRICTED: 'restricted',
  OPEN: 'open',
  CUSTOM: 'custom'
};

// File Types
export const ALLOWED_FILE_TYPES = {
  TEXT: [
    'text/plain',
    'text/csv',
    'text/markdown',
    'text/x-python',
    'application/x-python-code',
    'text/x-script.python'
  ],
  IMAGE: [
    'image/png',
    'image/jpeg',
    'image/gif',
    'image/svg+xml'
  ],
  DOCUMENT: [
    'application/pdf',
    'application/json',
    'application/xml',
    'text/xml'
  ],
  ARCHIVE: [
    'application/zip',
    'application/x-zip-compressed',
    'application/x-rar-compressed',
    'application/gzip'
  ],
  DATA: [
    'text/csv',
    'application/json',
    'application/xml',
    'text/xml'
  ]
};

// File Size Limits (in bytes)
export const FILE_SIZE_LIMITS = {
  SMALL: 1024 * 1024, // 1MB
  MEDIUM: 10 * 1024 * 1024, // 10MB
  LARGE: 50 * 1024 * 1024, // 50MB
  EXTRA_LARGE: 100 * 1024 * 1024 // 100MB
};

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
  MAX_LIMIT_LARGE: 1000
};

// JWT Configuration
export const JWT_CONFIG = {
  ACCESS_TOKEN_EXPIRY: '15m',
  REFRESH_TOKEN_EXPIRY: '7d',
  ALGORITHM: 'HS256'
};

// Session Configuration
export const SESSION_CONFIG = {
  SECRET: process.env.SESSION_SECRET || 'your-session-secret',
  COOKIE_MAX_AGE: 24 * 60 * 60 * 1000, // 24 hours
  CHECK_EXPIRATION_INTERVAL: 15 * 60 * 1000, // 15 minutes
  EXPIRATION: 24 * 60 * 60 * 1000 // 24 hours
};

// Database Configuration
export const DB_CONFIG = {
  POOL_MAX: 10,
  POOL_MIN: 0,
  POOL_ACQUIRE: 30000,
  POOL_IDLE: 10000,
  SYNC_FORCE: false,
  SYNC_ALTER: true
};

// AWS Configuration
export const AWS_CONFIG = {
  REGION: process.env.AWS_REGION || 'us-east-1',
  S3_BUCKET: process.env.AWS_S3_BUCKET || 'bits-dataScience-platform',
  S3_MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  S3_PRESIGNED_URL_EXPIRY: 3600 // 1 hour
};

// LTI Configuration
export const LTI_CONFIG = {
  VERSION: '1.3.0',
  MESSAGE_TYPES: {
    RESOURCE_LINK: 'LtiResourceLinkRequest',
    DEEP_LINKING: 'LtiDeepLinkingRequest',
    SUBMISSION_REVIEW: 'LtiSubmissionReviewRequest'
  },
  CLAIMS: {
    ISSUER: 'iss',
    AUDIENCE: 'aud',
    EXPIRATION: 'exp',
    ISSUED_AT: 'iat',
    NONCE: 'nonce',
    DEPLOYMENT_ID: 'https://purl.imsglobal.org/spec/lti/claim/deployment_id',
    MESSAGE_TYPE: 'https://purl.imsglobal.org/spec/lti/claim/message_type',
    VERSION: 'https://purl.imsglobal.org/spec/lti/claim/version',
    RESOURCE_LINK: 'https://purl.imsglobal.org/spec/lti/claim/resource_link',
    CONTEXT: 'https://purl.imsglobal.org/spec/lti/claim/context',
    USER: 'https://purl.imsglobal.org/spec/lti/claim/user',
    ROLES: 'https://purl.imsglobal.org/spec/lti/claim/roles',
    CUSTOM: 'https://purl.imsglobal.org/spec/lti/claim/custom'
  }
};

// Error Messages
export const ERROR_MESSAGES = {
  // Authentication
  INVALID_CREDENTIALS: 'Invalid email or password',
  TOKEN_EXPIRED: 'Authentication token expired',
  TOKEN_INVALID: 'Invalid authentication token',
  ACCESS_DENIED: 'Access denied',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions',
  
  // Validation
  VALIDATION_ERROR: 'Validation failed',
  REQUIRED_FIELD: 'This field is required',
  INVALID_EMAIL: 'Invalid email format',
  INVALID_UUID: 'Invalid UUID format',
  INVALID_FILE_TYPE: 'Invalid file type',
  FILE_TOO_LARGE: 'File size too large',
  
  // Database
  RECORD_NOT_FOUND: 'Record not found',
  DUPLICATE_ENTRY: 'Record already exists',
  FOREIGN_KEY_CONSTRAINT: 'Invalid reference to related resource',
  DATABASE_ERROR: 'Database operation failed',
  
  // File Operations
  FILE_UPLOAD_FAILED: 'File upload failed',
  FILE_DELETE_FAILED: 'File deletion failed',
  FILE_NOT_FOUND: 'File not found',
  
  // Workspace
  WORKSPACE_NOT_FOUND: 'Workspace not found',
  WORKSPACE_CREATION_FAILED: 'Workspace creation failed',
  WORKSPACE_ACCESS_DENIED: 'Access to workspace denied',
  
  // Sandbox
  SANDBOX_SETTINGS_NOT_FOUND: 'Sandbox settings not found',
  SANDBOX_CREATION_FAILED: 'Sandbox creation failed',
  
  // LTI
  LTI_INVALID_REQUEST: 'Invalid LTI request',
  LTI_PLATFORM_NOT_FOUND: 'LTI platform not found',
  LTI_LAUNCH_FAILED: 'LTI launch failed',
  
  // General
  INTERNAL_SERVER_ERROR: 'Internal server error',
  SERVICE_UNAVAILABLE: 'Service temporarily unavailable',
  BAD_REQUEST: 'Bad request',
  NOT_FOUND: 'Resource not found',
  CONFLICT: 'Resource conflict'
};

// Success Messages
export const SUCCESS_MESSAGES = {
  // Authentication
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logged out successfully',
  REGISTRATION_SUCCESS: 'Registration successful',
  PASSWORD_RESET_SUCCESS: 'Password reset successful',
  
  // CRUD Operations
  CREATED_SUCCESS: 'Resource created successfully',
  UPDATED_SUCCESS: 'Resource updated successfully',
  DELETED_SUCCESS: 'Resource deleted successfully',
  RETRIEVED_SUCCESS: 'Resource retrieved successfully',
  
  // File Operations
  FILE_UPLOADED_SUCCESS: 'File uploaded successfully',
  FILE_DELETED_SUCCESS: 'File deleted successfully',
  
  // Workspace
  WORKSPACE_CREATED_SUCCESS: 'Workspace created successfully',
  WORKSPACE_FORKED_SUCCESS: 'Project forked successfully',
  
  // Sandbox
  SANDBOX_SETTINGS_CREATED_SUCCESS: 'Sandbox settings created successfully',
  SANDBOX_SETTINGS_UPDATED_SUCCESS: 'Sandbox settings updated successfully',
  
  // LTI
  LTI_LAUNCH_SUCCESS: 'LTI launch successful',
  LTI_PLATFORM_REGISTERED_SUCCESS: 'LTI platform registered successfully'
};

// API Response Status
export const API_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// Log Levels
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
};

// Time Formats
export const TIME_FORMATS = {
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  DATETIME: 'YYYY-MM-DD HH:mm:ss'
};

// Default Values
export const DEFAULTS = {
  USER_AVATAR: '/assets/default-avatar.png',
  PROJECT_THUMBNAIL: '/assets/default-project-thumbnail.png',
  COURSE_THUMBNAIL: '/assets/default-course-thumbnail.png',
  SANDBOX_CPU_LIMIT: '0.5',
  SANDBOX_MEM_LIMIT: '1g',
  SANDBOX_TIMEOUT: 1800,
  SANDBOX_MAX_CONCURRENT_USERS: 10
};

export default {
  ENV,
  HTTP_STATUS,
  ROLES,
  PERMISSIONS,
  SUBMISSION_STATUS,
  WORKSPACE_STATUS,
  SANDBOX_MODES,
  NETWORK_POLICIES,
  ALLOWED_FILE_TYPES,
  FILE_SIZE_LIMITS,
  PAGINATION,
  JWT_CONFIG,
  SESSION_CONFIG,
  DB_CONFIG,
  AWS_CONFIG,
  LTI_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  API_STATUS,
  LOG_LEVELS,
  TIME_FORMATS,
  DEFAULTS
};
