import { Project, ProjectTemplate, ProjectAssignment, User, Course, Rubric } from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';

class enhancedProjectUtils {
    /**
   * check if project is exits or not
   */
  async checkProjectExist(projectId) {
    try {
      const project = await Project.findByPk(projectId);
      if(!project) {
        return false;
      }
      return project;
    } catch (error) {
      logger.error('Error checking project existence', { error });
      throw error;
    }
  }
}

export default new enhancedProjectUtils();