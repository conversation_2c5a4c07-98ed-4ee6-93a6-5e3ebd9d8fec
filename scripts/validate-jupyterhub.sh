#!/bin/bash

# Validation script for BITS DataScience Platform JupyterHub setup
# This script validates that all components are properly configured and running

set -e

echo "🔍 Validating BITS DataScience Platform JupyterHub Setup..."

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
JUPYTERHUB_DIR="${PROJECT_ROOT}/jupyterhub"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Validation functions
check_file() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        echo -e "✅ ${GREEN}$description${NC}: $file"
        return 0
    else
        echo -e "❌ ${RED}$description${NC}: $file (missing)"
        return 1
    fi
}

check_directory() {
    local dir="$1"
    local description="$2"
    
    if [ -d "$dir" ]; then
        echo -e "✅ ${GREEN}$description${NC}: $dir"
        return 0
    else
        echo -e "❌ ${RED}$description${NC}: $dir (missing)"
        return 1
    fi
}

check_executable() {
    local file="$1"
    local description="$2"
    
    if [ -x "$file" ]; then
        echo -e "✅ ${GREEN}$description${NC}: $file"
        return 0
    else
        echo -e "❌ ${RED}$description${NC}: $file (not executable)"
        return 1
    fi
}

check_docker_image() {
    local image="$1"
    local description="$2"
    
    if docker images | grep -q "$image"; then
        echo -e "✅ ${GREEN}$description${NC}: $image"
        return 0
    else
        echo -e "❌ ${RED}$description${NC}: $image (not found)"
        return 1
    fi
}

check_service() {
    local url="$1"
    local description="$2"
    
    if curl -f "$url" > /dev/null 2>&1; then
        echo -e "✅ ${GREEN}$description${NC}: $url"
        return 0
    else
        echo -e "❌ ${RED}$description${NC}: $url (not responding)"
        return 1
    fi
}

# Start validation
echo ""
echo "📁 Checking Directory Structure..."
validation_errors=0

check_directory "$JUPYTERHUB_DIR" "JupyterHub directory" || ((validation_errors++))
check_directory "$JUPYTERHUB_DIR/user-environment" "User environment directory" || ((validation_errors++))
check_directory "$JUPYTERHUB_DIR/templates" "Templates directory" || ((validation_errors++))

echo ""
echo "📄 Checking Configuration Files..."

check_file "$JUPYTERHUB_DIR/Dockerfile" "JupyterHub Dockerfile" || ((validation_errors++))
check_file "$JUPYTERHUB_DIR/jupyterhub_config.py" "JupyterHub configuration" || ((validation_errors++))
check_file "$JUPYTERHUB_DIR/custom_authenticator.py" "Custom authenticator" || ((validation_errors++))
check_file "$JUPYTERHUB_DIR/backend_integration.py" "Backend integration service" || ((validation_errors++))
check_file "$JUPYTERHUB_DIR/user-environment/Dockerfile" "User environment Dockerfile" || ((validation_errors++))
check_file "$JUPYTERHUB_DIR/templates/login.html" "Custom login template" || ((validation_errors++))
check_file "$PROJECT_ROOT/docker-compose.yml" "Docker Compose configuration" || ((validation_errors++))
check_file "$PROJECT_ROOT/.env.example" "Environment example file" || ((validation_errors++))

echo ""
echo "🔧 Checking Executable Scripts..."

check_executable "$PROJECT_ROOT/scripts/setup-jupyterhub.sh" "Setup script" || ((validation_errors++))
check_executable "$JUPYTERHUB_DIR/build-user-image.sh" "User image build script" || ((validation_errors++))
check_executable "$JUPYTERHUB_DIR/start-jupyterhub.sh" "JupyterHub startup script" || ((validation_errors++))
check_executable "$JUPYTERHUB_DIR/user-environment/start-user-server.sh" "User server startup script" || ((validation_errors++))

echo ""
echo "🐳 Checking Docker Setup..."

# Check if Docker is running
if docker info > /dev/null 2>&1; then
    echo -e "✅ ${GREEN}Docker daemon${NC}: running"
else
    echo -e "❌ ${RED}Docker daemon${NC}: not running"
    ((validation_errors++))
fi

# Check if docker-compose is available
if command -v docker-compose > /dev/null 2>&1; then
    echo -e "✅ ${GREEN}Docker Compose${NC}: available"
else
    echo -e "❌ ${RED}Docker Compose${NC}: not available"
    ((validation_errors++))
fi

# Check Docker network
if docker network ls | grep -q "bits_bits-network"; then
    echo -e "✅ ${GREEN}Docker network${NC}: bits_bits-network exists"
else
    echo -e "⚠️  ${YELLOW}Docker network${NC}: bits_bits-network not found (will be created during setup)"
fi

echo ""
echo "🖼️  Checking Docker Images..."

# Check base images
check_docker_image "postgres:15-alpine" "PostgreSQL image" || echo -e "⚠️  ${YELLOW}Will be pulled during setup${NC}"
check_docker_image "redis:7-alpine" "Redis image" || echo -e "⚠️  ${YELLOW}Will be pulled during setup${NC}"
check_docker_image "jupyterhub/jupyterhub:4.0" "JupyterHub base image" || echo -e "⚠️  ${YELLOW}Will be pulled during setup${NC}"

# Check custom images
check_docker_image "bits-jupyter-user" "Custom user environment" || echo -e "⚠️  ${YELLOW}Will be built during setup${NC}"

echo ""
echo "🔌 Checking Running Services..."

# Check if services are running
if docker ps | grep -q "bits-postgres"; then
    echo -e "✅ ${GREEN}PostgreSQL container${NC}: running"
    check_service "postgresql://localhost:5432" "PostgreSQL service" || true
else
    echo -e "⚠️  ${YELLOW}PostgreSQL container${NC}: not running"
fi

if docker ps | grep -q "bits-backend"; then
    echo -e "✅ ${GREEN}Backend container${NC}: running"
    check_service "http://localhost:5000/health" "Backend API" || true
else
    echo -e "⚠️  ${YELLOW}Backend container${NC}: not running"
fi

if docker ps | grep -q "bits-jupyterhub"; then
    echo -e "✅ ${GREEN}JupyterHub container${NC}: running"
    check_service "http://localhost:8001/hub/health" "JupyterHub service" || true
else
    echo -e "⚠️  ${YELLOW}JupyterHub container${NC}: not running"
fi

echo ""
echo "📊 Validation Summary"
echo "===================="

if [ $validation_errors -eq 0 ]; then
    echo -e "🎉 ${GREEN}All validations passed!${NC}"
    echo ""
    echo "✨ Your JupyterHub setup is ready!"
    echo ""
    echo "🚀 Next steps:"
    echo "  1. Run: ./scripts/setup-jupyterhub.sh"
    echo "  2. Access JupyterHub at: http://localhost:8001"
    echo "  3. Login with your BITS platform credentials"
    echo ""
else
    echo -e "⚠️  ${YELLOW}Found $validation_errors validation issues${NC}"
    echo ""
    echo "🔧 To fix issues:"
    echo "  1. Ensure all required files are present"
    echo "  2. Make scripts executable: chmod +x script_name.sh"
    echo "  3. Install Docker and Docker Compose if missing"
    echo "  4. Run the setup script: ./scripts/setup-jupyterhub.sh"
    echo ""
fi

echo "📚 Documentation:"
echo "  - Setup Guide: jupyterhub/README.md"
echo "  - Troubleshooting: Check logs with 'docker-compose logs'"
echo ""

exit $validation_errors
