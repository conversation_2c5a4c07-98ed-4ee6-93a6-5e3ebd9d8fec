#!/bin/bash

# Setup script for BITS DataScience Platform JupyterHub
# This script sets up the complete Docker-based JupyterHub environment

set -e

echo "🚀 Setting up BITS DataScience Platform JupyterHub..."

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
JUPYTERHUB_DIR="${PROJECT_ROOT}/jupyterhub"
USER_ENV_DIR="${JUPYTERHUB_DIR}/user-environment"

echo "Project root: ${PROJECT_ROOT}"
echo "JupyterHub directory: ${JUPYTERHUB_DIR}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

echo "✅ Docker is running"

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Error: docker-compose is not installed. Please install docker-compose and try again."
    exit 1
fi

echo "✅ docker-compose is available"

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p "${JUPYTERHUB_DIR}/logs"
mkdir -p "${JUPYTERHUB_DIR}/data"
mkdir -p "${USER_ENV_DIR}"

# Make scripts executable
echo "🔧 Making scripts executable..."
chmod +x "${JUPYTERHUB_DIR}/start-jupyterhub.sh"
chmod +x "${JUPYTERHUB_DIR}/build-user-image.sh"
chmod +x "${USER_ENV_DIR}/start-user-server.sh"

# Build the custom user environment image
echo "🏗️  Building custom user environment image..."
cd "${JUPYTERHUB_DIR}"
./build-user-image.sh

# Create Docker network if it doesn't exist
NETWORK_NAME="bits_bits-network"
if ! docker network ls | grep -q "${NETWORK_NAME}"; then
    echo "🌐 Creating Docker network: ${NETWORK_NAME}"
    docker network create "${NETWORK_NAME}"
else
    echo "✅ Docker network ${NETWORK_NAME} already exists"
fi

# Pull required images
echo "📥 Pulling required Docker images..."
docker pull postgres:15-alpine
docker pull redis:7-alpine
docker pull jupyterhub/jupyterhub:4.0

# Create environment file if it doesn't exist
ENV_FILE="${PROJECT_ROOT}/.env"
if [ ! -f "${ENV_FILE}" ]; then
    echo "📝 Creating environment file..."
    cp "${PROJECT_ROOT}/.env.example" "${ENV_FILE}"
    echo "⚠️  Please review and update the .env file with your specific configuration"
else
    echo "✅ Environment file already exists"
fi

# Stop any existing containers
echo "🛑 Stopping any existing containers..."
cd "${PROJECT_ROOT}"
docker-compose down --remove-orphans || true

# Start the services
echo "🚀 Starting BITS DataScience Platform services..."
docker-compose up -d postgres redis

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
timeout=60
counter=0
while ! docker exec bits-postgres pg_isready -U postgres > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Timeout waiting for database to be ready"
        exit 1
    fi
    echo "Database not ready, waiting... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

echo "✅ Database is ready"

# Start backend service
echo "🔧 Starting backend service..."
docker-compose up -d backend

# Wait for backend to be ready
echo "⏳ Waiting for backend to be ready..."
timeout=60
counter=0
while ! curl -f http://localhost:5000/health > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Timeout waiting for backend to be ready"
        exit 1
    fi
    echo "Backend not ready, waiting... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

echo "✅ Backend is ready"

# Start JupyterHub
echo "🚀 Starting JupyterHub..."
docker-compose up -d jupyterhub

# Wait for JupyterHub to be ready
echo "⏳ Waiting for JupyterHub to be ready..."
timeout=120
counter=0
while ! curl -f http://localhost:8001/hub/health > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Timeout waiting for JupyterHub to be ready"
        echo "Check logs with: docker-compose logs jupyterhub"
        exit 1
    fi
    echo "JupyterHub not ready, waiting... ($counter/$timeout)"
    sleep 3
    counter=$((counter + 3))
done

echo "✅ JupyterHub is ready"

# Display status
echo ""
echo "🎉 BITS DataScience Platform JupyterHub setup completed successfully!"
echo ""
echo "📊 Service Status:"
echo "  - Database (PostgreSQL): http://localhost:5432"
echo "  - Redis Cache: http://localhost:6379"
echo "  - Backend API: http://localhost:5000"
echo "  - JupyterHub: http://localhost:8001"
echo ""
echo "🔧 Management Tools:"
echo "  - Adminer (Database): http://localhost:8080"
echo "  - Redis Commander: http://localhost:8081"
echo ""
echo "📝 Next Steps:"
echo "  1. Access JupyterHub at http://localhost:8001"
echo "  2. Login with your BITS DataScience Platform credentials"
echo "  3. Start your first Jupyter server"
echo ""
echo "🔍 Useful Commands:"
echo "  - View logs: docker-compose logs -f [service_name]"
echo "  - Stop services: docker-compose down"
echo "  - Restart services: docker-compose restart [service_name]"
echo "  - View running containers: docker ps"
echo ""
echo "📚 Documentation:"
echo "  - JupyterHub Admin Guide: http://localhost:8001/hub/admin"
echo "  - API Documentation: http://localhost:5000/api/docs"
echo ""

# Show running containers
echo "🐳 Running Containers:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "✨ Setup completed! Happy coding! 🚀"
