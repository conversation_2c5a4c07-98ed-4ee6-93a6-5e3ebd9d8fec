<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="705" failures="89" errors="0" time="194.589">
  <testsuite name="Authentication Integration Tests" errors="0" failures="8" skipped="0" timestamp="2025-08-17T12:24:57" time="181.786" tests="15">
    <testcase classname="Authentication Integration Tests Health Check should return health status" name="Authentication Integration Tests Health Check should return health status" time="0.037">
    </testcase>
    <testcase classname="Authentication Integration Tests API Structure Tests should have authentication routes available" name="Authentication Integration Tests API Structure Tests should have authentication routes available" time="0.039">
    </testcase>
    <testcase classname="Authentication Integration Tests API Structure Tests should have user routes available" name="Authentication Integration Tests API Structure Tests should have user routes available" time="30.012">
      <failure>Error: thrown: &quot;Exceeded timeout of 30000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at test (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:59:5)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:46:3)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:6:1)
    at Runtime._execModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Authentication Integration Tests API Structure Tests should have course routes available" name="Authentication Integration Tests API Structure Tests should have course routes available" time="30.003">
      <failure>Error: thrown: &quot;Exceeded timeout of 30000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at test (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:70:5)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:46:3)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:6:1)
    at Runtime._execModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Authentication Integration Tests API Structure Tests should have project routes available" name="Authentication Integration Tests API Structure Tests should have project routes available" time="30.004">
      <failure>Error: thrown: &quot;Exceeded timeout of 30000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at test (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:81:5)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:46:3)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:6:1)
    at Runtime._execModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Authentication Integration Tests API Structure Tests should have LTI routes available" name="Authentication Integration Tests API Structure Tests should have LTI routes available" time="0.048">
      <failure>Error: expect(received).toHaveProperty(path)

Expected path: &quot;issuer&quot;
Received path: []

Received value: {&quot;claims&quot;: [&quot;iss&quot;, &quot;aud&quot;, &quot;exp&quot;, &quot;iat&quot;, &quot;nonce&quot;, &quot;https://purl.imsglobal.org/spec/lti/claim/deployment_id&quot;, &quot;https://purl.imsglobal.org/spec/lti/claim/message_type&quot;, &quot;https://purl.imsglobal.org/spec/lti/claim/version&quot;, &quot;https://purl.imsglobal.org/spec/lti/claim/resource_link&quot;, &quot;https://purl.imsglobal.org/spec/lti/claim/context&quot;, …], &quot;custom_fields&quot;: {&quot;context_id&quot;: &quot;$Context.id&quot;, &quot;project_id&quot;: &quot;$ResourceLink.id&quot;, &quot;user_id&quot;: &quot;$User.id&quot;}, &quot;description&quot;: &quot;Interactive data science projects and assignments platform for BITS Pilani&quot;, &quot;extensions&quot;: [{&quot;domain&quot;: &quot;localhost&quot;, &quot;platform&quot;: &quot;bits.edu&quot;, &quot;privacy_level&quot;: &quot;public&quot;, &quot;settings&quot;: {&quot;icon_url&quot;: &quot;http://localhost:5000/assets/bits-logo.png&quot;, &quot;selection_height&quot;: 600, &quot;selection_width&quot;: 800, &quot;text&quot;: &quot;BITS DataScience Platform&quot;}, &quot;tool_id&quot;: &quot;bits-datascience-platform&quot;}], &quot;oidc_initiation_url&quot;: &quot;http://localhost:5000/api/lti/login&quot;, &quot;public_jwk_url&quot;: &quot;http://localhost:5000/api/lti/jwks&quot;, &quot;scopes&quot;: [&quot;openid&quot;, &quot;https://purl.imsglobal.org/spec/lti-ags/scope/lineitem&quot;, &quot;https://purl.imsglobal.org/spec/lti-ags/scope/score&quot;, &quot;https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly&quot;], &quot;target_link_uri&quot;: &quot;http://localhost:5000/lti/launch&quot;, &quot;title&quot;: &quot;BITS-DataScience Projects Platform&quot;}
    at Object.toHaveProperty (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:97:29)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</failure>
    </testcase>
    <testcase classname="Authentication Integration Tests Authentication Middleware Tests should reject requests without authentication token" name="Authentication Integration Tests Authentication Middleware Tests should reject requests without authentication token" time="30.005">
      <failure>Error: thrown: &quot;Exceeded timeout of 30000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at test (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:103:5)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:102:3)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:6:1)
    at Runtime._execModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Authentication Integration Tests Authentication Middleware Tests should reject requests with invalid authentication token" name="Authentication Integration Tests Authentication Middleware Tests should reject requests with invalid authentication token" time="30.004">
      <failure>Error: thrown: &quot;Exceeded timeout of 30000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at test (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:114:5)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:102:3)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:6:1)
    at Runtime._execModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Authentication Integration Tests Authentication Middleware Tests should reject requests with malformed authentication header" name="Authentication Integration Tests Authentication Middleware Tests should reject requests with malformed authentication header" time="30.007">
      <failure>Error: thrown: &quot;Exceeded timeout of 30000 ms for a test.
Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.&quot;
    at test (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:125:5)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:102:3)
    at _dispatchDescribe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:91:26)
    at describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/index.js:55:5)
    at Object.describe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:6:1)
    at Runtime._execModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1439:24)
    at Runtime._loadModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:1022:12)
    at Runtime.requireModule (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runtime/build/index.js:882:12)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Authentication Integration Tests Error Handling Tests should handle 404 errors properly" name="Authentication Integration Tests Error Handling Tests should handle 404 errors properly" time="0.049">
    </testcase>
    <testcase classname="Authentication Integration Tests Error Handling Tests should handle validation errors properly" name="Authentication Integration Tests Error Handling Tests should handle validation errors properly" time="0.017">
    </testcase>
    <testcase classname="Authentication Integration Tests Error Handling Tests should handle malformed JSON properly" name="Authentication Integration Tests Error Handling Tests should handle malformed JSON properly" time="0.016">
    </testcase>
    <testcase classname="Authentication Integration Tests CORS Tests should handle CORS preflight requests" name="Authentication Integration Tests CORS Tests should handle CORS preflight requests" time="0.008">
      <failure>Error: expected 200 &quot;OK&quot;, got 204 &quot;No Content&quot;
    at Object.expect (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/integration/auth.integration.test.js:182:10)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)
----
    at Test._assertStatus (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/supertest/lib/test.js:252:14)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/supertest/lib/test.js:308:13
    at Test._assertFunction (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/supertest/lib/test.js:285:13)
    at Test.assert (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/supertest/lib/test.js:164:23)
    at Server.localAssert (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/supertest/lib/test.js:120:14)
    at Object.onceWrapper (node:events:638:28)
    at Server.emit (node:events:524:28)
    at emitCloseNT (node:net:2344:8)
    at processTicksAndRejections (node:internal/process/task_queues:81:21)</failure>
    </testcase>
    <testcase classname="Authentication Integration Tests CORS Tests should include CORS headers in responses" name="Authentication Integration Tests CORS Tests should include CORS headers in responses" time="0.008">
    </testcase>
    <testcase classname="Authentication Integration Tests Security Headers Tests should include security headers" name="Authentication Integration Tests Security Headers Tests should include security headers" time="0.008">
    </testcase>
  </testsuite>
  <testsuite name="S3 Service" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:27:58" time="0.405" tests="35">
    <testcase classname="S3 Service constructor should initialize with default values" name="S3 Service constructor should initialize with default values" time="0.001">
    </testcase>
    <testcase classname="S3 Service constructor should initialize with environment variables" name="S3 Service constructor should initialize with environment variables" time="0.014">
    </testcase>
    <testcase classname="S3 Service generateFilePath should generate project template path" name="S3 Service generateFilePath should generate project template path" time="0.001">
    </testcase>
    <testcase classname="S3 Service generateFilePath should generate project dataset path" name="S3 Service generateFilePath should generate project dataset path" time="0.001">
    </testcase>
    <testcase classname="S3 Service generateFilePath should generate submission path" name="S3 Service generateFilePath should generate submission path" time="0">
    </testcase>
    <testcase classname="S3 Service generateFilePath should generate user notebook path" name="S3 Service generateFilePath should generate user notebook path" time="0.001">
    </testcase>
    <testcase classname="S3 Service generateFilePath should generate profile picture path" name="S3 Service generateFilePath should generate profile picture path" time="0.001">
    </testcase>
    <testcase classname="S3 Service generateFilePath should generate course material path" name="S3 Service generateFilePath should generate course material path" time="0.001">
    </testcase>
    <testcase classname="S3 Service generateFilePath should generate misc path for unknown type" name="S3 Service generateFilePath should generate misc path for unknown type" time="0.001">
    </testcase>
    <testcase classname="S3 Service generateFilePath should generate path with UUID when filename not provided" name="S3 Service generateFilePath should generate path with UUID when filename not provided" time="0">
    </testcase>
    <testcase classname="S3 Service uploadFile should upload file successfully" name="S3 Service uploadFile should upload file successfully" time="0.002">
    </testcase>
    <testcase classname="S3 Service uploadFile should handle upload error" name="S3 Service uploadFile should handle upload error" time="0.023">
    </testcase>
    <testcase classname="S3 Service generatePresignedUploadUrl should generate presigned upload URL successfully" name="S3 Service generatePresignedUploadUrl should generate presigned upload URL successfully" time="0.001">
    </testcase>
    <testcase classname="S3 Service generatePresignedUploadUrl should handle presigned URL generation error" name="S3 Service generatePresignedUploadUrl should handle presigned URL generation error" time="0">
    </testcase>
    <testcase classname="S3 Service generatePresignedDownloadUrl should generate presigned download URL successfully" name="S3 Service generatePresignedDownloadUrl should generate presigned download URL successfully" time="0">
    </testcase>
    <testcase classname="S3 Service generatePresignedDownloadUrl should handle download URL generation error" name="S3 Service generatePresignedDownloadUrl should handle download URL generation error" time="0.002">
    </testcase>
    <testcase classname="S3 Service deleteFile should delete file successfully" name="S3 Service deleteFile should delete file successfully" time="0.001">
    </testcase>
    <testcase classname="S3 Service deleteFile should handle delete error" name="S3 Service deleteFile should handle delete error" time="0.001">
    </testcase>
    <testcase classname="S3 Service fileExists should return true when file exists" name="S3 Service fileExists should return true when file exists" time="0.001">
    </testcase>
    <testcase classname="S3 Service fileExists should return false when file does not exist" name="S3 Service fileExists should return false when file does not exist" time="0.001">
    </testcase>
    <testcase classname="S3 Service fileExists should throw error for other errors" name="S3 Service fileExists should throw error for other errors" time="0.002">
    </testcase>
    <testcase classname="S3 Service getFileMetadata should get file metadata successfully" name="S3 Service getFileMetadata should get file metadata successfully" time="0.001">
    </testcase>
    <testcase classname="S3 Service getFileMetadata should handle metadata retrieval error" name="S3 Service getFileMetadata should handle metadata retrieval error" time="0.001">
    </testcase>
    <testcase classname="S3 Service copyFile should copy file successfully" name="S3 Service copyFile should copy file successfully" time="0.001">
    </testcase>
    <testcase classname="S3 Service copyFile should handle copy error" name="S3 Service copyFile should handle copy error" time="0.001">
    </testcase>
    <testcase classname="S3 Service extractKeyFromUrl should extract key from base URL" name="S3 Service extractKeyFromUrl should extract key from base URL" time="0.001">
    </testcase>
    <testcase classname="S3 Service extractKeyFromUrl should extract key from different S3 URL format" name="S3 Service extractKeyFromUrl should extract key from different S3 URL format" time="0.003">
    </testcase>
    <testcase classname="S3 Service extractKeyFromUrl should return null for invalid URL" name="S3 Service extractKeyFromUrl should return null for invalid URL" time="0.001">
    </testcase>
    <testcase classname="S3 Service extractKeyFromUrl should return null for non-string URL" name="S3 Service extractKeyFromUrl should return null for non-string URL" time="0">
    </testcase>
    <testcase classname="S3 Service validateFile should validate file type successfully" name="S3 Service validateFile should validate file type successfully" time="0">
    </testcase>
    <testcase classname="S3 Service validateFile should reject invalid file type" name="S3 Service validateFile should reject invalid file type" time="0.001">
    </testcase>
    <testcase classname="S3 Service validateFile should reject file that is too large" name="S3 Service validateFile should reject file that is too large" time="0">
    </testcase>
    <testcase classname="S3 Service validateFile should handle multiple validation errors" name="S3 Service validateFile should handle multiple validation errors" time="0.001">
    </testcase>
    <testcase classname="S3 Service validateFile should skip type validation when no allowed types specified" name="S3 Service validateFile should skip type validation when no allowed types specified" time="0">
    </testcase>
    <testcase classname="S3 Service validateFile should skip size validation when no max size specified" name="S3 Service validateFile should skip size validation when no max size specified" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="LTI Service" errors="0" failures="14" skipped="0" timestamp="2025-08-17T12:27:59" time="0.545" tests="18">
    <testcase classname="LTI Service generateClientAssertion should generate client assertion successfully" name="LTI Service generateClientAssertion should generate client assertion successfully" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: &quot;mock-private-key&quot;, &quot;pem&quot;

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:47:30)</failure>
    </testcase>
    <testcase classname="LTI Service generateClientAssertion should handle key generation errors" name="LTI Service generateClientAssertion should handle key generation errors" time="0.001">
      <failure>Error: expect(received).rejects.toThrow()

Received promise resolved instead of rejected
Resolved to value: &quot;mock-jwt-token&quot;
    at expect (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/expect/build/index.js:113:15)
    at Object.expect (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:69:13)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Service getAccessToken should get access token successfully" name="LTI Service getAccessToken should get access token successfully" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: &quot;https://test.com/token&quot;, ObjectContaining {&quot;client_assertion&quot;: &quot;mock-assertion&quot;, &quot;client_assertion_type&quot;: &quot;urn:ietf:params:oauth:client-assertion-type:jwt-bearer&quot;, &quot;grant_type&quot;: &quot;client_credentials&quot;, &quot;scope&quot;: &quot;https://purl.imsglobal.org/spec/lti-ags/scope/lineitem https://purl.imsglobal.org/spec/lti-ags/scope/score&quot;}, ObjectContaining {&quot;headers&quot;: ObjectContaining {&quot;Content-Type&quot;: &quot;application/x-www-form-urlencoded&quot;}}
Received: &quot;https://test.com/token&quot;, {&quot;client_assertion&quot;: &quot;mock-jwt-token&quot;, &quot;client_assertion_type&quot;: &quot;urn:ietf:params:oauth:client-assertion-type:jwt-bearer&quot;, &quot;grant_type&quot;: &quot;client_credentials&quot;, &quot;scope&quot;: &quot;https://purl.imsglobal.org/spec/lti-ags/scope/score&quot;}, {&quot;headers&quot;: {&quot;Content-Type&quot;: &quot;application/x-www-form-urlencoded&quot;}}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:96:26)</failure>
    </testcase>
    <testcase classname="LTI Service getAccessToken should handle token request errors" name="LTI Service getAccessToken should handle token request errors" time="0.001">
    </testcase>
    <testcase classname="LTI Service createLineItem should create line item successfully" name="LTI Service createLineItem should create line item successfully" time="0">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at LtiService.id [as createLineItem] (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/services/ltiService.js:406:28)
    at createLineItem (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/services/ltiService.js:619:18)
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:139:42)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Service createLineItem should handle line item creation errors" name="LTI Service createLineItem should handle line item creation errors" time="0.014">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected substring: &quot;Line item creation failed&quot;
Received message:   &quot;Cannot read properties of undefined (reading &apos;id&apos;)&quot;

      404 |       where: {
      405 |         resourceLinkId: resourceLink.id,
    &gt; 406 |         projectId: project.id
          |                            ^
      407 |       },
      408 |       defaults: {
      409 |         platformId: resourceLink.platformId,

      at LtiService.id [as createLineItem] (src/services/ltiService.js:406:28)
      at createLineItem (src/services/ltiService.js:619:18)
      at Object.&lt;anonymous&gt; (tests/unit/services/ltiService.test.js:170:34)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/expect/build/index.js:218:22)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:174:19)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Service getLineItems should get line items successfully" name="LTI Service getLineItems should get line items successfully" time="0">
      <failure>Error: Line item creation failed
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:167:25)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Service getLineItems should handle line items retrieval errors" name="LTI Service getLineItems should handle line items retrieval errors" time="0.002">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected substring: &quot;Line items retrieval failed&quot;
Received message:   &quot;Line item creation failed&quot;

      165 |
      166 |     test(&apos;should handle line item creation errors&apos;, async () =&gt; {
    &gt; 167 |       const mockError = new Error(&apos;Line item creation failed&apos;);
          |                         ^
      168 |       axios.post = jest.fn().mockRejectedValue(mockError);
      169 |
      170 |       await expect(createLineItem({

      at Object.&lt;anonymous&gt; (tests/unit/services/ltiService.test.js:167:25)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/expect/build/index.js:218:22)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:214:19)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Service createScore should create score successfully" name="LTI Service createScore should create score successfully" time="0.001">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;userId&apos;)
    at userId (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/services/ltiService.js:629:34)
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:233:39)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Service createScore should handle score creation errors" name="LTI Service createScore should handle score creation errors" time="0.002">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected substring: &quot;Score creation failed&quot;
Received message:   &quot;Cannot read properties of undefined (reading &apos;userId&apos;)&quot;

      627 | export const createScore = async (platform, lineItemId, score) =&gt; {
      628 |   const service = new LtiService();
    &gt; 629 |   return service.sendGrade(score.userId, { id: lineItemId }, score.scoreGiven, platform);
          |                                  ^
      630 | };
      631 |
      632 | export const getScores = async (platform, lineItemId) =&gt; {

      at userId (src/services/ltiService.js:629:34)
      at Object.&lt;anonymous&gt; (tests/unit/services/ltiService.test.js:256:31)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/expect/build/index.js:218:22)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:260:19)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Service getScores should get scores successfully" name="LTI Service getScores should get scores successfully" time="0.001">
      <failure>Error: Score creation failed
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:253:25)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Service getScores should handle scores retrieval errors" name="LTI Service getScores should handle scores retrieval errors" time="0.002">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected substring: &quot;Scores retrieval failed&quot;
Received message:   &quot;Score creation failed&quot;

      251 |
      252 |     test(&apos;should handle score creation errors&apos;, async () =&gt; {
    &gt; 253 |       const mockError = new Error(&apos;Score creation failed&apos;);
          |                         ^
      254 |       axios.post = jest.fn().mockRejectedValue(mockError);
      255 |
      256 |       await expect(createScore({

      at Object.&lt;anonymous&gt; (tests/unit/services/ltiService.test.js:253:25)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/expect/build/index.js:218:22)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:300:19)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Service validateLtiToken should validate LTI token successfully" name="LTI Service validateLtiToken should validate LTI token successfully" time="0.001">
      <failure>Error: Invalid JWT token
    at LtiService.verifyJWT (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/services/ltiService.js:88:13)
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:318:22)</failure>
    </testcase>
    <testcase classname="LTI Service validateLtiToken should handle invalid token" name="LTI Service validateLtiToken should handle invalid token" time="0.002">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected substring: &quot;Invalid token&quot;
Received message:   &quot;Invalid JWT token&quot;

      86 |     } catch (error) {
      87 |       logger.error(&apos;JWT verification failed:&apos;, error);
    &gt; 88 |       throw new Error(&apos;Invalid JWT token&apos;);
         |             ^
      89 |     }
      90 |   }
      91 |

      at LtiService.verifyJWT (src/services/ltiService.js:88:13)
      at Object.&lt;anonymous&gt; (tests/unit/services/ltiService.test.js:339:7)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/expect/build/index.js:218:22)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:339:75)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Service validateLtiToken should handle expired token" name="LTI Service validateLtiToken should handle expired token" time="0.005">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected substring: &quot;Token expired&quot;
Received message:   &quot;Invalid JWT token&quot;

      86 |     } catch (error) {
      87 |       logger.error(&apos;JWT verification failed:&apos;, error);
    &gt; 88 |       throw new Error(&apos;Invalid JWT token&apos;);
         |             ^
      89 |     }
      90 |   }
      91 |

      at LtiService.verifyJWT (src/services/ltiService.js:88:13)
      at Object.&lt;anonymous&gt; (tests/unit/services/ltiService.test.js:350:7)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/expect/build/index.js:218:22)
    at Object.toThrow (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/ltiService.test.js:350:75)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Service parseLtiRequest should parse LTI request successfully" name="LTI Service parseLtiRequest should parse LTI request successfully" time="0.001">
    </testcase>
    <testcase classname="LTI Service parseLtiRequest should handle missing required fields" name="LTI Service parseLtiRequest should handle missing required fields" time="0">
    </testcase>
    <testcase classname="LTI Service parseLtiRequest should handle empty request body" name="LTI Service parseLtiRequest should handle empty request body" time="0">
    </testcase>
  </testsuite>
  <testsuite name="LTI Controller" errors="0" failures="16" skipped="0" timestamp="2025-08-17T12:27:59" time="0.296" tests="18">
    <testcase classname="LTI Controller getLTIConfiguration should return LTI configuration" name="LTI Controller getLTIConfiguration should return LTI configuration" time="0.001">
    </testcase>
    <testcase classname="LTI Controller initiateLogin should initiate LTI login successfully" name="LTI Controller initiateLogin should initiate LTI login successfully" time="0.128">
      <failure>Error: 
    at Query.run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/postgres/query.js:76:25)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/sequelize.js:650:28
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at PostgresQueryInterface.select (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/abstract/query-interface.js:1001:12)
    at Function.findAll (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1824:21)
    at Function.findOne (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1998:12)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/ltiController.js:28:20
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:166:7)</failure>
    </testcase>
    <testcase classname="LTI Controller initiateLogin should return 400 when platform not found" name="LTI Controller initiateLogin should return 400 when platform not found" time="0.004">
      <failure>Error: 
    at Query.run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/postgres/query.js:76:25)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/sequelize.js:650:28
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at PostgresQueryInterface.select (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/abstract/query-interface.js:1001:12)
    at Function.findAll (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1824:21)
    at Function.findOne (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1998:12)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/ltiController.js:28:20
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:186:7)</failure>
    </testcase>
    <testcase classname="LTI Controller handleLaunch should handle LTI launch successfully" name="LTI Controller handleLaunch should handle LTI launch successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: &quot;valid.jwt.token&quot;, &quot;state-123&quot;

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:228:48)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</failure>
    </testcase>
    <testcase classname="LTI Controller handleLaunch should return 400 when launch session not found" name="LTI Controller handleLaunch should return 400 when launch session not found" time="0.001">
    </testcase>
    <testcase classname="LTI Controller getJWKS should return JWKS" name="LTI Controller getJWKS should return JWKS" time="0">
      <failure>TypeError: _ltiService.default.getJWKS is not a function
    at getJWKS (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/ltiController.js:107:27)
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:261:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller handleDeepLinking should handle deep linking successfully" name="LTI Controller handleDeepLinking should handle deep linking successfully" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;contentItems&quot;: Any&lt;Array&gt;,
-   &quot;message&quot;: &quot;Deep linking successful&quot;,
-   &quot;success&quot;: true,
+   &quot;error&quot;: &quot;Deep Linking Failed&quot;,
+   &quot;message&quot;: &quot;_ltiService.default.processLaunch is not a function&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:292:33)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</failure>
    </testcase>
    <testcase classname="LTI Controller registerPlatform should register platform successfully" name="LTI Controller registerPlatform should register platform successfully" time="0.004">
      <failure>Error: 
    at Query.run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/postgres/query.js:76:25)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/sequelize.js:650:28
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at PostgresQueryInterface.select (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/abstract/query-interface.js:1001:12)
    at Function.findAll (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1824:21)
    at Function.findOne (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1998:12)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/ltiController.js:188:28
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:320:7)</failure>
    </testcase>
    <testcase classname="LTI Controller registerPlatform should return 409 when platform already exists" name="LTI Controller registerPlatform should return 409 when platform already exists" time="0.003">
      <failure>Error: 
    at Query.run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/postgres/query.js:76:25)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/sequelize.js:650:28
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at PostgresQueryInterface.select (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/abstract/query-interface.js:1001:12)
    at Function.findAll (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1824:21)
    at Function.findOne (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1998:12)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/ltiController.js:188:28
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:344:7)</failure>
    </testcase>
    <testcase classname="LTI Controller getPlatforms should get all platforms" name="LTI Controller getPlatforms should get all platforms" time="0.007">
      <failure>Error: 
    at Query.run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/postgres/query.js:76:25)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/sequelize.js:650:28
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at PostgresQueryInterface.select (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/abstract/query-interface.js:1001:12)
    at Function.findAll (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1824:21)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/ltiController.js:249:21
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:373:7)</failure>
    </testcase>
    <testcase classname="LTI Controller updatePlatform should update platform successfully" name="LTI Controller updatePlatform should update platform successfully" time="0.002">
      <failure>Error: 
    at Query.run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/postgres/query.js:76:25)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/sequelize.js:650:28
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at PostgresQueryInterface.select (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/abstract/query-interface.js:1001:12)
    at Function.findAll (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1824:21)
    at Function.findOne (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1998:12)
    at Function.findByPk (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1955:12)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/ltiController.js:293:20
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:399:7)</failure>
    </testcase>
    <testcase classname="LTI Controller updatePlatform should return 404 when platform not found" name="LTI Controller updatePlatform should return 404 when platform not found" time="0.001">
      <failure>Error: 
    at Query.run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/postgres/query.js:76:25)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/sequelize.js:650:28
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at PostgresQueryInterface.select (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/abstract/query-interface.js:1001:12)
    at Function.findAll (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1824:21)
    at Function.findOne (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1998:12)
    at Function.findByPk (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1955:12)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/ltiController.js:293:20
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:414:7)</failure>
    </testcase>
    <testcase classname="LTI Controller deletePlatform should delete platform successfully" name="LTI Controller deletePlatform should delete platform successfully" time="0.002">
      <failure>Error: 
    at Query.run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/postgres/query.js:76:25)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/sequelize.js:650:28
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at PostgresQueryInterface.select (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/abstract/query-interface.js:1001:12)
    at Function.findAll (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1824:21)
    at Function.findOne (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1998:12)
    at Function.findByPk (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1955:12)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/ltiController.js:335:20
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:434:7)</failure>
    </testcase>
    <testcase classname="LTI Controller deletePlatform should return 404 when platform not found" name="LTI Controller deletePlatform should return 404 when platform not found" time="0.002">
      <failure>Error: 
    at Query.run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/postgres/query.js:76:25)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/sequelize.js:650:28
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at PostgresQueryInterface.select (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/dialects/abstract/query-interface.js:1001:12)
    at Function.findAll (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1824:21)
    at Function.findOne (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1998:12)
    at Function.findByPk (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1955:12)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/ltiController.js:335:20
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:448:7)</failure>
    </testcase>
    <testcase classname="LTI Controller sendGradeToLMS should send grade to LMS successfully" name="LTI Controller sendGradeToLMS should send grade to LMS successfully" time="0.001">
      <failure>ReferenceError: Submission is not defined
    at Object.Submission (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:480:7)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller sendGradeToLMS should return 404 when grade not found" name="LTI Controller sendGradeToLMS should return 404 when grade not found" time="0">
      <failure>ReferenceError: Submission is not defined
    at Object.Submission (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:504:7)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller getCourseContexts should get course contexts successfully" name="LTI Controller getCourseContexts should get course contexts successfully" time="0.001">
      <failure>Error: Include unexpected. Element has to be either a Model, an Association or an object.
    at Function._conformInclude (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:416:11)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:352:59
    at Array.map (&lt;anonymous&gt;)
    at Function._conformIncludes (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:352:39)
    at Function._conformInclude (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:405:16)
    at /Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:352:59
    at Array.map (&lt;anonymous&gt;)
    at Function._conformIncludes (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:352:39)
    at Function._baseMerge (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:818:10)
    at Function._defaultsOptions (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:861:17)
    at Function._injectScope (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:3413:10)
    at Function.findAll (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/sequelize/src/model.js:1777:10)
    at findAll (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/ltiController.js:450:37)
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:539:30)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="LTI Controller getCourseContexts should handle database errors" name="LTI Controller getCourseContexts should handle database errors" time="0.002">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: [Error: Database error]
Received: [Error: Include unexpected. Element has to be either a Model, an Association or an object.]
    at Object.toBe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/ltiController.test.js:555:29)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Sandbox Orchestrator Service" errors="0" failures="6" skipped="0" timestamp="2025-08-17T12:28:00" time="0.244" tests="23">
    <testcase classname="Sandbox Orchestrator Service createSandbox should create sandbox successfully" name="Sandbox Orchestrator Service createSandbox should create sandbox successfully" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service createSandbox should handle sandbox creation errors" name="Sandbox Orchestrator Service createSandbox should handle sandbox creation errors" time="0.014">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service createSandbox should validate required configuration" name="Sandbox Orchestrator Service createSandbox should validate required configuration" time="0">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service deleteSandbox should delete sandbox successfully" name="Sandbox Orchestrator Service deleteSandbox should delete sandbox successfully" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service deleteSandbox should handle sandbox deletion errors" name="Sandbox Orchestrator Service deleteSandbox should handle sandbox deletion errors" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service deleteSandbox should handle non-existent sandbox" name="Sandbox Orchestrator Service deleteSandbox should handle non-existent sandbox" time="0.003">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service getSandboxStatus should get sandbox status successfully" name="Sandbox Orchestrator Service getSandboxStatus should get sandbox status successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: StringContaining &quot;/sandboxes/sandbox-789/status&quot;, ObjectContaining {&quot;headers&quot;: ObjectContaining {&quot;Content-Type&quot;: &quot;application/json&quot;}}
Received: &quot;https://sandbox-api.example.com/sandboxes/sandbox-789/status&quot;, {&quot;headers&quot;: {&quot;Authorization&quot;: &quot;Bearer undefined&quot;}}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:158:25)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service getSandboxStatus should handle status retrieval errors" name="Sandbox Orchestrator Service getSandboxStatus should handle status retrieval errors" time="0">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service updateSandboxResources should update sandbox resources successfully" name="Sandbox Orchestrator Service updateSandboxResources should update sandbox resources successfully" time="0">
      <failure>Error: Resource update failed
    at updateSandboxResources (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/services/sandboxOrchestrator.js:88:11)
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:197:50)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service updateSandboxResources should handle resource update errors" name="Sandbox Orchestrator Service updateSandboxResources should handle resource update errors" time="0">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service updateSandboxResources should validate resource limits" name="Sandbox Orchestrator Service updateSandboxResources should validate resource limits" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service listSandboxes should list sandboxes successfully" name="Sandbox Orchestrator Service listSandboxes should list sandboxes successfully" time="0">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: StringContaining &quot;/sandboxes&quot;, ObjectContaining {&quot;headers&quot;: ObjectContaining {&quot;Content-Type&quot;: &quot;application/json&quot;}, &quot;params&quot;: {&quot;limit&quot;: 10, &quot;page&quot;: 1, &quot;status&quot;: &quot;running&quot;}}
Received: &quot;https://sandbox-api.example.com/sandboxes?page=1&amp;limit=10&amp;status=running&quot;, {&quot;headers&quot;: {&quot;Authorization&quot;: &quot;Bearer undefined&quot;}}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:267:25)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service listSandboxes should handle listing errors" name="Sandbox Orchestrator Service listSandboxes should handle listing errors" time="0">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service listSandboxes should handle pagination parameters" name="Sandbox Orchestrator Service listSandboxes should handle pagination parameters" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: Any&lt;String&gt;, ObjectContaining {&quot;params&quot;: {&quot;limit&quot;: 5, &quot;page&quot;: 2}}
Received: &quot;https://sandbox-api.example.com/sandboxes?page=2&amp;limit=5&quot;, {&quot;headers&quot;: {&quot;Authorization&quot;: &quot;Bearer undefined&quot;}}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:307:25)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service getSandboxLogs should get sandbox logs successfully" name="Sandbox Orchestrator Service getSandboxLogs should get sandbox logs successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: StringContaining &quot;/sandboxes/sandbox-789/logs&quot;, ObjectContaining {&quot;headers&quot;: ObjectContaining {&quot;Content-Type&quot;: &quot;application/json&quot;}, &quot;params&quot;: {&quot;level&quot;: &quot;ERROR&quot;, &quot;limit&quot;: 50}}
Received: &quot;https://sandbox-api.example.com/sandboxes/sandbox-789/logs?level=ERROR&amp;limit=50&quot;, {&quot;headers&quot;: {&quot;Authorization&quot;: &quot;Bearer undefined&quot;}}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:347:25)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service getSandboxLogs should handle log retrieval errors" name="Sandbox Orchestrator Service getSandboxLogs should handle log retrieval errors" time="0">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service getSandboxLogs should handle log level filtering" name="Sandbox Orchestrator Service getSandboxLogs should handle log level filtering" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: Any&lt;String&gt;, ObjectContaining {&quot;params&quot;: {&quot;level&quot;: &quot;WARN&quot;}}
Received: &quot;https://sandbox-api.example.com/sandboxes/sandbox-789/logs?level=WARN&quot;, {&quot;headers&quot;: {&quot;Authorization&quot;: &quot;Bearer undefined&quot;}}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/sandboxOrchestrator.test.js:385:25)</failure>
    </testcase>
    <testcase classname="Sandbox Orchestrator Service restartSandbox should restart sandbox successfully" name="Sandbox Orchestrator Service restartSandbox should restart sandbox successfully" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service restartSandbox should handle restart errors" name="Sandbox Orchestrator Service restartSandbox should handle restart errors" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service restartSandbox should handle restart with force option" name="Sandbox Orchestrator Service restartSandbox should handle restart with force option" time="0">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service scaleSandbox should scale sandbox successfully" name="Sandbox Orchestrator Service scaleSandbox should scale sandbox successfully" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service scaleSandbox should handle scaling errors" name="Sandbox Orchestrator Service scaleSandbox should handle scaling errors" time="0.001">
    </testcase>
    <testcase classname="Sandbox Orchestrator Service scaleSandbox should validate scaling limits" name="Sandbox Orchestrator Service scaleSandbox should validate scaling limits" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Grade Controller" errors="0" failures="8" skipped="0" timestamp="2025-08-17T12:28:00" time="0.124" tests="18">
    <testcase classname="Grade Controller getGrades should get all grades with pagination" name="Grade Controller getGrades should get all grades with pagination" time="0.001">
    </testcase>
    <testcase classname="Grade Controller getGrades should filter grades by project ID" name="Grade Controller getGrades should filter grades by project ID" time="0.001">
    </testcase>
    <testcase classname="Grade Controller getGrades should handle database errors" name="Grade Controller getGrades should handle database errors" time="0">
    </testcase>
    <testcase classname="Grade Controller getGradeById should get grade by ID" name="Grade Controller getGradeById should get grade by ID" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;data&quot;: ObjectContaining {
+   &quot;grade&quot;: Object {
+     &quot;createdAt&quot;: undefined,
+     &quot;evaluator&quot;: Object {
+       &quot;email&quot;: &quot;<EMAIL>&quot;,
+       &quot;id&quot;: &quot;instructor-123&quot;,
+       &quot;name&quot;: &quot;Test Instructor&quot;,
+     },
      &quot;feedback&quot;: &quot;Good work!&quot;,
+     &quot;gradedAt&quot;: undefined,
      &quot;id&quot;: &quot;grade-123&quot;,
      &quot;letterGrade&quot;: &quot;B&quot;,
      &quot;maxScore&quot;: 100,
      &quot;percentage&quot;: 85,
+     &quot;rubricScores&quot;: undefined,
+     &quot;submission&quot;: Object {
+       &quot;id&quot;: &quot;submission-123&quot;,
+       &quot;notebookS3Url&quot;: undefined,
+       &quot;project&quot;: Object {
+         &quot;course&quot;: Object {
+           &quot;code&quot;: undefined,
+           &quot;id&quot;: &quot;course-123&quot;,
+           &quot;instructor&quot;: undefined,
+           &quot;name&quot;: &quot;Test Course&quot;,
+         },
+         &quot;description&quot;: undefined,
+         &quot;dueDate&quot;: undefined,
+         &quot;id&quot;: &quot;project-123&quot;,
+         &quot;instructions&quot;: undefined,
+         &quot;rubrics&quot;: Array [],
+         &quot;title&quot;: &quot;Test Project&quot;,
+       },
+       &quot;status&quot;: undefined,
+       &quot;submittedAt&quot;: undefined,
+       &quot;user&quot;: Object {
+         &quot;email&quot;: &quot;<EMAIL>&quot;,
+         &quot;id&quot;: &quot;student-123&quot;,
+         &quot;name&quot;: &quot;Test Student&quot;,
+         &quot;profilePicture&quot;: undefined,
+       },
+     },
      &quot;totalScore&quot;: 85,
+     &quot;updatedAt&quot;: undefined,
    },
    &quot;success&quot;: true,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/gradeController.test.js:264:33)</failure>
    </testcase>
    <testcase classname="Grade Controller getGradeById should return 404 when grade not found" name="Grade Controller getGradeById should return 404 when grade not found" time="0.001">
    </testcase>
    <testcase classname="Grade Controller createOrUpdateGrade should create new grade" name="Grade Controller createOrUpdateGrade should create new grade" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: {&quot;where&quot;: {&quot;submission_id&quot;: &quot;submission-123&quot;}}

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/gradeController.test.js:323:29)</failure>
    </testcase>
    <testcase classname="Grade Controller createOrUpdateGrade should update existing grade" name="Grade Controller createOrUpdateGrade should update existing grade" time="0">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: ObjectContaining {&quot;feedback&quot;: &quot;Updated feedback&quot;, &quot;total_score&quot;: 90}

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/gradeController.test.js:367:36)</failure>
    </testcase>
    <testcase classname="Grade Controller createOrUpdateGrade should return 404 when submission not found" name="Grade Controller createOrUpdateGrade should return 404 when submission not found" time="0.001">
    </testcase>
    <testcase classname="Grade Controller updateGrade should update grade successfully" name="Grade Controller updateGrade should update grade successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;data&quot;: Object {
-     &quot;evaluator_id&quot;: &quot;user-123&quot;,
+   &quot;grade&quot;: Object {
+     &quot;feedback&quot;: undefined,
+     &quot;gradedAt&quot;: undefined,
      &quot;id&quot;: &quot;grade-123&quot;,
-     &quot;submission&quot;: Object {
-       &quot;id&quot;: &quot;submission-123&quot;,
-       &quot;project&quot;: Object {
-         &quot;course&quot;: Object {
-           &quot;id&quot;: &quot;course-123&quot;,
-           &quot;instructor_id&quot;: &quot;user-123&quot;,
-         },
-         &quot;id&quot;: &quot;project-123&quot;,
-       },
-     },
-     &quot;update&quot;: [Function mockConstructor],
+     &quot;letterGrade&quot;: undefined,
+     &quot;maxScore&quot;: undefined,
+     &quot;percentage&quot;: undefined,
+     &quot;totalScore&quot;: undefined,
    },
    &quot;message&quot;: &quot;Grade updated successfully&quot;,
    &quot;success&quot;: true,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/gradeController.test.js:426:33)</failure>
    </testcase>
    <testcase classname="Grade Controller updateGrade should return 404 when grade not found" name="Grade Controller updateGrade should return 404 when grade not found" time="0">
    </testcase>
    <testcase classname="Grade Controller deleteGrade should delete grade successfully" name="Grade Controller deleteGrade should delete grade successfully" time="0">
      <failure>TypeError: grade.submission.update is not a function
    at update (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/src/controllers/gradeController.js:541:26)
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/gradeController.test.js:468:7)</failure>
    </testcase>
    <testcase classname="Grade Controller deleteGrade should return 404 when grade not found" name="Grade Controller deleteGrade should return 404 when grade not found" time="0.001">
    </testcase>
    <testcase classname="Grade Controller getGradingQueue should get grading queue successfully" name="Grade Controller getGradingQueue should get grading queue successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

@@ -4,14 +4,56 @@
        &quot;currentPage&quot;: 1,
        &quot;itemsPerPage&quot;: 10,
        &quot;totalItems&quot;: 2,
        &quot;totalPages&quot;: 1,
      },
-     &quot;submissions&quot;: ArrayContaining [
-       ObjectContaining {
+     &quot;submissions&quot;: Array [
+       Object {
+         &quot;grade&quot;: null,
          &quot;id&quot;: &quot;submission-1&quot;,
-         &quot;status&quot;: &quot;submitted&quot;,
+         &quot;isGraded&quot;: false,
+         &quot;project&quot;: Object {
+           &quot;course&quot;: Object {
+             &quot;code&quot;: undefined,
+             &quot;id&quot;: &quot;course-1&quot;,
+             &quot;name&quot;: &quot;Data Science 101&quot;,
+           },
+           &quot;difficultyLevel&quot;: undefined,
+           &quot;dueDate&quot;: undefined,
+           &quot;id&quot;: &quot;project-1&quot;,
+           &quot;title&quot;: &quot;Data Analysis Project&quot;,
+         },
+         &quot;submittedAt&quot;: 2025-08-17T12:28:00.612Z,
+         &quot;user&quot;: Object {
+           &quot;email&quot;: &quot;<EMAIL>&quot;,
+           &quot;id&quot;: &quot;student-1&quot;,
+           &quot;name&quot;: &quot;John Doe&quot;,
+           &quot;profilePicture&quot;: undefined,
+         },
+       },
+       Object {
+         &quot;grade&quot;: null,
+         &quot;id&quot;: &quot;submission-2&quot;,
+         &quot;isGraded&quot;: false,
+         &quot;project&quot;: Object {
+           &quot;course&quot;: Object {
+             &quot;code&quot;: undefined,
+             &quot;id&quot;: &quot;course-2&quot;,
+             &quot;name&quot;: &quot;Machine Learning&quot;,
+           },
+           &quot;difficultyLevel&quot;: undefined,
+           &quot;dueDate&quot;: undefined,
+           &quot;id&quot;: &quot;project-2&quot;,
+           &quot;title&quot;: &quot;ML Project&quot;,
+         },
+         &quot;submittedAt&quot;: 2025-08-17T12:28:00.612Z,
+         &quot;user&quot;: Object {
+           &quot;email&quot;: &quot;<EMAIL>&quot;,
+           &quot;id&quot;: &quot;student-2&quot;,
+           &quot;name&quot;: &quot;Jane Smith&quot;,
+           &quot;profilePicture&quot;: undefined,
+         },
        },
      ],
    },
    &quot;success&quot;: true,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/gradeController.test.js:547:33)</failure>
    </testcase>
    <testcase classname="Grade Controller getGradingQueue should handle database errors" name="Grade Controller getGradingQueue should handle database errors" time="0">
    </testcase>
    <testcase classname="Grade Controller getGradeStatistics should get grade statistics successfully" name="Grade Controller getGradeStatistics should get grade statistics successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalled()

Expected number of calls: &gt;= 1
Received number of calls:    0
    at Object.toHaveBeenCalled (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/gradeController.test.js:600:27)</failure>
    </testcase>
    <testcase classname="Grade Controller getGradeStatistics should handle database errors" name="Grade Controller getGradeStatistics should handle database errors" time="0.001">
    </testcase>
    <testcase classname="Grade Controller bulkGradeSubmissions should bulk grade submissions successfully" name="Grade Controller bulkGradeSubmissions should bulk grade submissions successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;data&quot;: ObjectContaining {
-     &quot;errors&quot;: Any&lt;Array&gt;,
-     &quot;success&quot;: Any&lt;Array&gt;,
+   &quot;message&quot;: &quot;Bulk grading completed&quot;,
+   &quot;results&quot;: Object {
+     &quot;errors&quot;: Array [],
+     &quot;success&quot;: Array [
+       Object {
+         &quot;gradeId&quot;: &quot;grade-1&quot;,
+         &quot;message&quot;: &quot;Grade created&quot;,
+         &quot;submissionId&quot;: &quot;submission-1&quot;,
+       },
+       Object {
+         &quot;gradeId&quot;: &quot;grade-2&quot;,
+         &quot;message&quot;: &quot;Grade updated&quot;,
+         &quot;submissionId&quot;: &quot;submission-2&quot;,
+       },
+     ],
    },
-   &quot;message&quot;: &quot;Bulk grading completed successfully&quot;,
    &quot;success&quot;: true,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/gradeController.test.js:689:33)</failure>
    </testcase>
    <testcase classname="Grade Controller bulkGradeSubmissions should return 400 when no submissions provided" name="Grade Controller bulkGradeSubmissions should return 400 when no submissions provided" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="JupyterHub Admin Service" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:00" time="0.148" tests="28">
    <testcase classname="JupyterHub Admin Service createUser should create user successfully" name="JupyterHub Admin Service createUser should create user successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service createUser should handle user creation errors" name="JupyterHub Admin Service createUser should handle user creation errors" time="0.012">
    </testcase>
    <testcase classname="JupyterHub Admin Service createUser should handle duplicate user creation" name="JupyterHub Admin Service createUser should handle duplicate user creation" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service deleteUser should delete user successfully" name="JupyterHub Admin Service deleteUser should delete user successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service deleteUser should handle user deletion errors" name="JupyterHub Admin Service deleteUser should handle user deletion errors" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service deleteUser should handle non-existent user deletion" name="JupyterHub Admin Service deleteUser should handle non-existent user deletion" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service getUser should get user successfully" name="JupyterHub Admin Service getUser should get user successfully" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service getUser should handle user retrieval errors" name="JupyterHub Admin Service getUser should handle user retrieval errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service listUsers should list users successfully" name="JupyterHub Admin Service listUsers should list users successfully" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service listUsers should handle user listing errors" name="JupyterHub Admin Service listUsers should handle user listing errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service startServer should start server successfully" name="JupyterHub Admin Service startServer should start server successfully" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service startServer should handle server start errors" name="JupyterHub Admin Service startServer should handle server start errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service stopServer should stop server successfully" name="JupyterHub Admin Service stopServer should stop server successfully" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service stopServer should handle server stop errors" name="JupyterHub Admin Service stopServer should handle server stop errors" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service getServerStatus should get server status successfully" name="JupyterHub Admin Service getServerStatus should get server status successfully" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service getServerStatus should handle server status retrieval errors" name="JupyterHub Admin Service getServerStatus should handle server status retrieval errors" time="0.004">
    </testcase>
    <testcase classname="JupyterHub Admin Service getUserServers should get user servers successfully" name="JupyterHub Admin Service getUserServers should get user servers successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service getUserServers should handle user servers retrieval errors" name="JupyterHub Admin Service getUserServers should handle user servers retrieval errors" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service addUserToGroup should add user to group successfully" name="JupyterHub Admin Service addUserToGroup should add user to group successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service addUserToGroup should handle add user to group errors" name="JupyterHub Admin Service addUserToGroup should handle add user to group errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service removeUserFromGroup should remove user from group successfully" name="JupyterHub Admin Service removeUserFromGroup should remove user from group successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service removeUserFromGroup should handle remove user from group errors" name="JupyterHub Admin Service removeUserFromGroup should handle remove user from group errors" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service createGroup should create group successfully" name="JupyterHub Admin Service createGroup should create group successfully" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service createGroup should handle group creation errors" name="JupyterHub Admin Service createGroup should handle group creation errors" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service deleteGroup should delete group successfully" name="JupyterHub Admin Service deleteGroup should delete group successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service deleteGroup should handle group deletion errors" name="JupyterHub Admin Service deleteGroup should handle group deletion errors" time="0">
    </testcase>
    <testcase classname="JupyterHub Admin Service listGroups should list groups successfully" name="JupyterHub Admin Service listGroups should list groups successfully" time="0.001">
    </testcase>
    <testcase classname="JupyterHub Admin Service listGroups should handle group listing errors" name="JupyterHub Admin Service listGroups should handle group listing errors" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Submission Controller" errors="0" failures="7" skipped="0" timestamp="2025-08-17T12:28:00" time="0.104" tests="16">
    <testcase classname="Submission Controller getSubmissions should get all submissions with pagination" name="Submission Controller getSubmissions should get all submissions with pagination" time="0.003">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

@@ -6,67 +6,75 @@
        &quot;totalItems&quot;: 2,
        &quot;totalPages&quot;: 1,
      },
      &quot;submissions&quot;: Array [
        Object {
-         &quot;execution_time&quot;: 120,
+         &quot;createdAt&quot;: undefined,
+         &quot;executionTime&quot;: 120,
          &quot;grade&quot;: null,
          &quot;id&quot;: &quot;submission-1&quot;,
          &quot;metadata&quot;: Object {},
-         &quot;notebook_s3_url&quot;: &quot;https://s3.example.com/notebook1.ipynb&quot;,
+         &quot;notebookS3Url&quot;: &quot;https://s3.example.com/notebook1.ipynb&quot;,
          &quot;project&quot;: Object {
            &quot;course&quot;: Object {
              &quot;code&quot;: &quot;DS101&quot;,
              &quot;id&quot;: &quot;course-1&quot;,
              &quot;name&quot;: &quot;Data Science 101&quot;,
            },
-           &quot;difficulty_level&quot;: &quot;intermediate&quot;,
-           &quot;due_date&quot;: 2025-08-17T12:28:00.864Z,
+           &quot;difficultyLevel&quot;: &quot;intermediate&quot;,
+           &quot;dueDate&quot;: 2025-08-17T12:28:00.864Z,
            &quot;id&quot;: &quot;project-1&quot;,
            &quot;title&quot;: &quot;Data Analysis Project&quot;,
          },
          &quot;status&quot;: &quot;submitted&quot;,
-         &quot;submitted_at&quot;: 2025-08-17T12:28:00.864Z,
+         &quot;submittedAt&quot;: 2025-08-17T12:28:00.864Z,
+         &quot;updatedAt&quot;: undefined,
          &quot;user&quot;: Object {
            &quot;email&quot;: &quot;<EMAIL>&quot;,
            &quot;id&quot;: &quot;user-1&quot;,
            &quot;name&quot;: &quot;John Doe&quot;,
-           &quot;profile_picture&quot;: null,
+           &quot;profilePicture&quot;: null,
          },
        },
        Object {
-         &quot;execution_time&quot;: 180,
+         &quot;createdAt&quot;: undefined,
+         &quot;executionTime&quot;: 180,
          &quot;grade&quot;: Object {
            &quot;evaluator&quot;: Object {
              &quot;email&quot;: &quot;<EMAIL>&quot;,
              &quot;id&quot;: &quot;instructor-1&quot;,
              &quot;name&quot;: &quot;Dr. Smith&quot;,
            },
+           &quot;feedback&quot;: undefined,
            &quot;id&quot;: &quot;grade-1&quot;,
+           &quot;letterGrade&quot;: undefined,
+           &quot;maxScore&quot;: undefined,
            &quot;percentage&quot;: 85,
+           &quot;totalScore&quot;: undefined,
          },
          &quot;id&quot;: &quot;submission-2&quot;,
          &quot;metadata&quot;: Object {},
-         &quot;notebook_s3_url&quot;: &quot;https://s3.example.com/notebook2.ipynb&quot;,
+         &quot;notebookS3Url&quot;: &quot;https://s3.example.com/notebook2.ipynb&quot;,
          &quot;project&quot;: Object {
            &quot;course&quot;: Object {
              &quot;code&quot;: &quot;ML101&quot;,
              &quot;id&quot;: &quot;course-2&quot;,
              &quot;name&quot;: &quot;Machine Learning&quot;,
            },
-           &quot;difficulty_level&quot;: &quot;advanced&quot;,
-           &quot;due_date&quot;: 2025-08-17T12:28:00.864Z,
+           &quot;difficultyLevel&quot;: &quot;advanced&quot;,
+           &quot;dueDate&quot;: 2025-08-17T12:28:00.864Z,
            &quot;id&quot;: &quot;project-2&quot;,
            &quot;title&quot;: &quot;ML Project&quot;,
          },
          &quot;status&quot;: &quot;graded&quot;,
-         &quot;submitted_at&quot;: 2025-08-17T12:28:00.864Z,
+         &quot;submittedAt&quot;: 2025-08-17T12:28:00.864Z,
+         &quot;updatedAt&quot;: undefined,
          &quot;user&quot;: Object {
            &quot;email&quot;: &quot;<EMAIL>&quot;,
            &quot;id&quot;: &quot;user-2&quot;,
            &quot;name&quot;: &quot;Jane Smith&quot;,
-           &quot;profile_picture&quot;: null,
+           &quot;profilePicture&quot;: null,
          },
        },
      ],
    },
    &quot;success&quot;: true,,

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/submissionController.test.js:178:33)</failure>
    </testcase>
    <testcase classname="Submission Controller getSubmissions should filter submissions by project ID" name="Submission Controller getSubmissions should filter submissions by project ID" time="0.001">
    </testcase>
    <testcase classname="Submission Controller getSubmissions should handle database errors" name="Submission Controller getSubmissions should handle database errors" time="0">
    </testcase>
    <testcase classname="Submission Controller getSubmissionById should get submission by ID" name="Submission Controller getSubmissionById should get submission by ID" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;data&quot;: Object {
+   &quot;submission&quot;: Object {
+     &quot;createdAt&quot;: undefined,
+     &quot;executionTime&quot;: undefined,
      &quot;grade&quot;: Object {
+       &quot;evaluator&quot;: undefined,
+       &quot;feedback&quot;: undefined,
+       &quot;gradedAt&quot;: undefined,
        &quot;id&quot;: &quot;grade-123&quot;,
+       &quot;letterGrade&quot;: undefined,
+       &quot;maxScore&quot;: undefined,
        &quot;percentage&quot;: 85,
+       &quot;rubricScores&quot;: undefined,
+       &quot;totalScore&quot;: undefined,
      },
      &quot;id&quot;: &quot;submission-123&quot;,
+     &quot;metadata&quot;: undefined,
+     &quot;notebookPresignedUrl&quot;: null,
+     &quot;notebookS3Url&quot;: undefined,
      &quot;project&quot;: Object {
        &quot;course&quot;: Object {
+         &quot;code&quot;: undefined,
          &quot;id&quot;: &quot;course-123&quot;,
-         &quot;instructor_id&quot;: &quot;instructor-123&quot;,
+         &quot;instructor&quot;: undefined,
+         &quot;name&quot;: undefined,
        },
+       &quot;description&quot;: undefined,
+       &quot;dueDate&quot;: undefined,
        &quot;id&quot;: &quot;project-123&quot;,
+       &quot;instructions&quot;: undefined,
        &quot;title&quot;: &quot;Test Project&quot;,
      },
      &quot;status&quot;: &quot;submitted&quot;,
+     &quot;submittedAt&quot;: undefined,
+     &quot;updatedAt&quot;: undefined,
      &quot;user&quot;: Object {
+       &quot;email&quot;: undefined,
        &quot;id&quot;: &quot;user-123&quot;,
        &quot;name&quot;: &quot;Test User&quot;,
+       &quot;profilePicture&quot;: undefined,
      },
-     &quot;user_id&quot;: &quot;user-123&quot;,
    },
    &quot;success&quot;: true,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/submissionController.test.js:251:33)</failure>
    </testcase>
    <testcase classname="Submission Controller getSubmissionById should return 404 when submission not found" name="Submission Controller getSubmissionById should return 404 when submission not found" time="0">
    </testcase>
    <testcase classname="Submission Controller createOrUpdateSubmission should create new submission" name="Submission Controller createOrUpdateSubmission should create new submission" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: &quot;project-123&quot;
Received: &quot;project-123&quot;, {&quot;include&quot;: [{&quot;as&quot;: &quot;course&quot;, &quot;include&quot;: [{&quot;as&quot;: &quot;enrollments&quot;, &quot;model&quot;: {&quot;findOne&quot;: [Function mockConstructor]}, &quot;required&quot;: false, &quot;where&quot;: {&quot;user_id&quot;: &quot;user-123&quot;}}], &quot;model&quot;: {&quot;findByPk&quot;: [Function mockConstructor]}}]}

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/submissionController.test.js:297:32)</failure>
    </testcase>
    <testcase classname="Submission Controller createOrUpdateSubmission should update existing submission" name="Submission Controller createOrUpdateSubmission should update existing submission" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: ObjectContaining {&quot;content&quot;: &quot;Updated content&quot;}

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/submissionController.test.js:337:41)</failure>
    </testcase>
    <testcase classname="Submission Controller createOrUpdateSubmission should return 404 when project not found" name="Submission Controller createOrUpdateSubmission should return 404 when project not found" time="0">
    </testcase>
    <testcase classname="Submission Controller submitAssignment should submit assignment successfully" name="Submission Controller submitAssignment should submit assignment successfully" time="0.001">
    </testcase>
    <testcase classname="Submission Controller submitAssignment should return 404 when submission not found" name="Submission Controller submitAssignment should return 404 when submission not found" time="0">
    </testcase>
    <testcase classname="Submission Controller downloadSubmissionNotebook should download submission notebook" name="Submission Controller downloadSubmissionNotebook should download submission notebook" time="0.003">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: &quot;/path/to/notebook.ipynb&quot;, Any&lt;String&gt;

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/submissionController.test.js:437:37)</failure>
    </testcase>
    <testcase classname="Submission Controller downloadSubmissionNotebook should return 404 when submission not found" name="Submission Controller downloadSubmissionNotebook should return 404 when submission not found" time="0.001">
    </testcase>
    <testcase classname="Submission Controller getSubmissionStatistics should get submission statistics successfully" name="Submission Controller getSubmissionStatistics should get submission statistics successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalled()

Expected number of calls: &gt;= 1
Received number of calls:    0
    at Object.toHaveBeenCalled (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/submissionController.test.js:478:32)</failure>
    </testcase>
    <testcase classname="Submission Controller getSubmissionStatistics should handle database errors" name="Submission Controller getSubmissionStatistics should handle database errors" time="0">
    </testcase>
    <testcase classname="Submission Controller autoSaveSubmission should auto-save submission successfully" name="Submission Controller autoSaveSubmission should auto-save submission successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: {&quot;metadata&quot;: Any&lt;Object&gt;, &quot;notebook_s3_url&quot;: Any&lt;String&gt;}

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/submissionController.test.js:525:37)</failure>
    </testcase>
    <testcase classname="Submission Controller autoSaveSubmission should return 400 when missing required fields" name="Submission Controller autoSaveSubmission should return 400 when missing required fields" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Error Handler Middleware" errors="0" failures="2" skipped="0" timestamp="2025-08-17T12:28:00" time="0.099" tests="31">
    <testcase classname="Error Handler Middleware errorHandler should handle generic errors" name="Error Handler Middleware errorHandler should handle generic errors" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle ValidationError" name="Error Handler Middleware errorHandler should handle ValidationError" time="0.008">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle SequelizeUniqueConstraintError" name="Error Handler Middleware errorHandler should handle SequelizeUniqueConstraintError" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle SequelizeForeignKeyConstraintError" name="Error Handler Middleware errorHandler should handle SequelizeForeignKeyConstraintError" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle SequelizeConnectionError" name="Error Handler Middleware errorHandler should handle SequelizeConnectionError" time="0.001">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: &quot;Database connection error&quot;
Received: undefined
    at Object.toBe (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:146:36)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle JsonWebTokenError" name="Error Handler Middleware errorHandler should handle JsonWebTokenError" time="0">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle TokenExpiredError" name="Error Handler Middleware errorHandler should handle TokenExpiredError" time="0.002">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle MulterError with LIMIT_FILE_SIZE" name="Error Handler Middleware errorHandler should handle MulterError with LIMIT_FILE_SIZE" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle MulterError with LIMIT_FILE_COUNT" name="Error Handler Middleware errorHandler should handle MulterError with LIMIT_FILE_COUNT" time="0">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle MulterError with unknown code" name="Error Handler Middleware errorHandler should handle MulterError with unknown code" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle custom errors with statusCode" name="Error Handler Middleware errorHandler should handle custom errors with statusCode" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle custom errors with status" name="Error Handler Middleware errorHandler should handle custom errors with status" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle errors without message" name="Error Handler Middleware errorHandler should handle errors without message" time="0">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should include stack trace in development mode" name="Error Handler Middleware errorHandler should include stack trace in development mode" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should not include stack trace in production mode" name="Error Handler Middleware errorHandler should not include stack trace in production mode" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should handle request without user" name="Error Handler Middleware errorHandler should handle request without user" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware errorHandler should include details for non-500 errors in production" name="Error Handler Middleware errorHandler should include details for non-500 errors in production" time="0">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes ValidationError should have correct properties" name="Error Handler Middleware Custom Error Classes ValidationError should have correct properties" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes NotFoundError should have correct properties" name="Error Handler Middleware Custom Error Classes NotFoundError should have correct properties" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes NotFoundError should use default message" name="Error Handler Middleware Custom Error Classes NotFoundError should use default message" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes UnauthorizedError should have correct properties" name="Error Handler Middleware Custom Error Classes UnauthorizedError should have correct properties" time="0">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes UnauthorizedError should use default message" name="Error Handler Middleware Custom Error Classes UnauthorizedError should use default message" time="0.002">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes ForbiddenError should have correct properties" name="Error Handler Middleware Custom Error Classes ForbiddenError should have correct properties" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes ForbiddenError should use default message" name="Error Handler Middleware Custom Error Classes ForbiddenError should use default message" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes ConflictError should have correct properties" name="Error Handler Middleware Custom Error Classes ConflictError should have correct properties" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes ConflictError should use default message" name="Error Handler Middleware Custom Error Classes ConflictError should use default message" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes BadRequestError should have correct properties" name="Error Handler Middleware Custom Error Classes BadRequestError should have correct properties" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware Custom Error Classes BadRequestError should use default message" name="Error Handler Middleware Custom Error Classes BadRequestError should use default message" time="0">
    </testcase>
    <testcase classname="Error Handler Middleware asyncHandler should handle successful async operations" name="Error Handler Middleware asyncHandler should handle successful async operations" time="0">
    </testcase>
    <testcase classname="Error Handler Middleware asyncHandler should handle async operation errors" name="Error Handler Middleware asyncHandler should handle async operation errors" time="0.001">
    </testcase>
    <testcase classname="Error Handler Middleware asyncHandler should handle synchronous errors" name="Error Handler Middleware asyncHandler should handle synchronous errors" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: [Error: Sync operation failed]

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/middlewares/errorHandler.test.js:488:24)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:316:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Workspace Controller" errors="0" failures="6" skipped="0" timestamp="2025-08-17T12:28:01" time="0.054" tests="14">
    <testcase classname="Workspace Controller forkExercise should fork exercise successfully" name="Workspace Controller forkExercise should fork exercise successfully" time="0.002">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: {&quot;forkVersion&quot;: Any&lt;String&gt;, &quot;isReady&quot;: false, &quot;projectId&quot;: &quot;project-123&quot;, &quot;s3Prefix&quot;: Any&lt;String&gt;, &quot;status&quot;: &quot;creating&quot;, &quot;studentId&quot;: &quot;user-123&quot;}

Number of calls: 0
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:129:36)</failure>
    </testcase>
    <testcase classname="Workspace Controller forkExercise should return 404 when project not found" name="Workspace Controller forkExercise should return 404 when project not found" time="0.001">
    </testcase>
    <testcase classname="Workspace Controller forkExercise should return 400 when workspace already exists" name="Workspace Controller forkExercise should return 400 when workspace already exists" time="0">
    </testcase>
    <testcase classname="Workspace Controller getWorkspaceStatus should get workspace status successfully" name="Workspace Controller getWorkspaceStatus should get workspace status successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;success&quot;: true,
-   &quot;workspace&quot;: ObjectContaining {
-     &quot;forkVersion&quot;: undefined,
-     &quot;id&quot;: &quot;workspace-123&quot;,
-     &quot;isReady&quot;: undefined,
-     &quot;projectId&quot;: &quot;project-123&quot;,
-     &quot;s3Prefix&quot;: undefined,
-     &quot;status&quot;: &quot;active&quot;,
-   },
+   &quot;error&quot;: &quot;Failed to get workspace status&quot;,
+   &quot;message&quot;: &quot;_s3WorkspaceService.default.workspaceExists is not a function&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:223:33)</failure>
    </testcase>
    <testcase classname="Workspace Controller getWorkspaceStatus should return 404 when workspace not found" name="Workspace Controller getWorkspaceStatus should return 404 when workspace not found" time="0">
    </testcase>
    <testcase classname="Workspace Controller listWorkspaceFiles should list workspace files successfully" name="Workspace Controller listWorkspaceFiles should list workspace files successfully" time="0">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;files&quot;: Any&lt;Array&gt;,
-   &quot;success&quot;: true,
+   &quot;error&quot;: &quot;Failed to list workspace files&quot;,
+   &quot;message&quot;: &quot;_s3WorkspaceService.default.listWorkspaceFiles is not a function&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:279:33)</failure>
    </testcase>
    <testcase classname="Workspace Controller listWorkspaceFiles should return 404 when workspace not found" name="Workspace Controller listWorkspaceFiles should return 404 when workspace not found" time="0">
    </testcase>
    <testcase classname="Workspace Controller uploadWorkspaceFile should upload file to workspace successfully" name="Workspace Controller uploadWorkspaceFile should upload file to workspace successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;file&quot;: Object {
-     &quot;path&quot;: &quot;test.py&quot;,
-     &quot;size&quot;: 18,
-     &quot;uploadedAt&quot;: Any&lt;Date&gt;,
-   },
-   &quot;message&quot;: &quot;File uploaded successfully&quot;,
-   &quot;success&quot;: true,
+   &quot;error&quot;: &quot;Failed to upload file&quot;,
+   &quot;message&quot;: &quot;_s3WorkspaceService.default.uploadFile is not a function&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:330:33)</failure>
    </testcase>
    <testcase classname="Workspace Controller uploadWorkspaceFile should return 404 when workspace not found" name="Workspace Controller uploadWorkspaceFile should return 404 when workspace not found" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 404
Received: 400

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:347:35)</failure>
    </testcase>
    <testcase classname="Workspace Controller uploadWorkspaceFile should return 400 when no file provided" name="Workspace Controller uploadWorkspaceFile should return 400 when no file provided" time="0.002">
    </testcase>
    <testcase classname="Workspace Controller deleteWorkspaceFile should delete file from workspace successfully" name="Workspace Controller deleteWorkspaceFile should delete file from workspace successfully" time="0.001">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

- Expected
+ Received

  Object {
-   &quot;message&quot;: &quot;File deleted successfully&quot;,
-   &quot;success&quot;: true,
+   &quot;error&quot;: &quot;Failed to delete file&quot;,
+   &quot;message&quot;: &quot;_s3WorkspaceService.default.deleteWorkspaceFile is not a function&quot;,
  },

Number of calls: 1
    at Object.toHaveBeenCalledWith (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/controllers/workspace.controller.test.js:395:33)</failure>
    </testcase>
    <testcase classname="Workspace Controller deleteWorkspaceFile should return 404 when workspace not found" name="Workspace Controller deleteWorkspaceFile should return 404 when workspace not found" time="0.001">
    </testcase>
    <testcase classname="Workspace Controller getUserWorkspaces should get user workspaces successfully" name="Workspace Controller getUserWorkspaces should get user workspaces successfully" time="0.001">
    </testcase>
    <testcase classname="Workspace Controller getUserWorkspaces should handle database errors" name="Workspace Controller getUserWorkspaces should handle database errors" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Database Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:02" time="0.493" tests="15">
    <testcase classname="Database Integration Tests Database Connection should have test models loaded" name="Database Integration Tests Database Connection should have test models loaded" time="0.001">
    </testcase>
    <testcase classname="Database Integration Tests Database Connection should have test data created" name="Database Integration Tests Database Connection should have test data created" time="0.001">
    </testcase>
    <testcase classname="Database Integration Tests Database Connection should indicate database availability" name="Database Integration Tests Database Connection should indicate database availability" time="0.001">
    </testcase>
    <testcase classname="Database Integration Tests User Model Operations should create a new user" name="Database Integration Tests User Model Operations should create a new user" time="0.001">
    </testcase>
    <testcase classname="Database Integration Tests User Model Operations should find existing user" name="Database Integration Tests User Model Operations should find existing user" time="0">
    </testcase>
    <testcase classname="Database Integration Tests User Model Operations should update user" name="Database Integration Tests User Model Operations should update user" time="0.001">
    </testcase>
    <testcase classname="Database Integration Tests User Model Operations should delete user" name="Database Integration Tests User Model Operations should delete user" time="0">
    </testcase>
    <testcase classname="Database Integration Tests Role Model Operations should create a new role" name="Database Integration Tests Role Model Operations should create a new role" time="0.001">
    </testcase>
    <testcase classname="Database Integration Tests Role Model Operations should find existing role" name="Database Integration Tests Role Model Operations should find existing role" time="0.001">
    </testcase>
    <testcase classname="Database Integration Tests Course Model Operations should create a new course" name="Database Integration Tests Course Model Operations should create a new course" time="0">
    </testcase>
    <testcase classname="Database Integration Tests Course Model Operations should find course with instructor" name="Database Integration Tests Course Model Operations should find course with instructor" time="0.001">
    </testcase>
    <testcase classname="Database Integration Tests Project Model Operations should create a new project" name="Database Integration Tests Project Model Operations should create a new project" time="0.001">
    </testcase>
    <testcase classname="Database Integration Tests Project Model Operations should find project with course" name="Database Integration Tests Project Model Operations should find project with course" time="0">
    </testcase>
    <testcase classname="Database Integration Tests Associations should have proper user-role associations" name="Database Integration Tests Associations should have proper user-role associations" time="0.004">
    </testcase>
    <testcase classname="Database Integration Tests Associations should have proper course-project associations" name="Database Integration Tests Associations should have proper course-project associations" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Grade Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:03" time="0.053" tests="34">
    <testcase classname="Grade Model (Mocked) Model Definition should define Grade model with correct attributes" name="Grade Model (Mocked) Model Definition should define Grade model with correct attributes" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Definition should define table name correctly" name="Grade Model (Mocked) Model Definition should define table name correctly" time="0.002">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Definition should define indexes correctly" name="Grade Model (Mocked) Model Definition should define indexes correctly" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate required fields" name="Grade Model (Mocked) Model Validation should validate required fields" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate score values" name="Grade Model (Mocked) Model Validation should validate score values" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate percentage range" name="Grade Model (Mocked) Model Validation should validate percentage range" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate letter grade format" name="Grade Model (Mocked) Model Validation should validate letter grade format" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate UUID format" name="Grade Model (Mocked) Model Validation should validate UUID format" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate JSONB fields" name="Grade Model (Mocked) Model Validation should validate JSONB fields" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Validation should validate grading time" name="Grade Model (Mocked) Model Validation should validate grading time" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should create grade successfully" name="Grade Model (Mocked) Model Methods should create grade successfully" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should find grade by submission ID" name="Grade Model (Mocked) Model Methods should find grade by submission ID" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should find grade by ID" name="Grade Model (Mocked) Model Methods should find grade by ID" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should find grades by evaluator" name="Grade Model (Mocked) Model Methods should find grades by evaluator" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should update grade" name="Grade Model (Mocked) Model Methods should update grade" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should delete grade" name="Grade Model (Mocked) Model Methods should delete grade" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should find final grades" name="Grade Model (Mocked) Model Methods should find final grades" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Methods should count grades" name="Grade Model (Mocked) Model Methods should count grades" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Hooks should calculate percentage before create" name="Grade Model (Mocked) Model Hooks should calculate percentage before create" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Hooks should calculate letter grade" name="Grade Model (Mocked) Model Hooks should calculate letter grade" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Hooks should set graded_at timestamp" name="Grade Model (Mocked) Model Hooks should set graded_at timestamp" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Hooks should validate rubric scores" name="Grade Model (Mocked) Model Hooks should validate rubric scores" time="0.004">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Associations should have submission association" name="Grade Model (Mocked) Model Associations should have submission association" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Associations should have evaluator association" name="Grade Model (Mocked) Model Associations should have evaluator association" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Scopes should have final grades scope" name="Grade Model (Mocked) Model Scopes should have final grades scope" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Scopes should have by evaluator scope" name="Grade Model (Mocked) Model Scopes should have by evaluator scope" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Model Scopes should have by percentage range scope" name="Grade Model (Mocked) Model Scopes should have by percentage range scope" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Error Handling should handle duplicate submission ID error" name="Grade Model (Mocked) Error Handling should handle duplicate submission ID error" time="0.01">
    </testcase>
    <testcase classname="Grade Model (Mocked) Error Handling should handle validation error" name="Grade Model (Mocked) Error Handling should handle validation error" time="0.002">
    </testcase>
    <testcase classname="Grade Model (Mocked) Error Handling should handle grade not found" name="Grade Model (Mocked) Error Handling should handle grade not found" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Business Logic should calculate grade point average" name="Grade Model (Mocked) Business Logic should calculate grade point average" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Business Logic should check if grade is passing" name="Grade Model (Mocked) Business Logic should check if grade is passing" time="0">
    </testcase>
    <testcase classname="Grade Model (Mocked) Business Logic should calculate average grade" name="Grade Model (Mocked) Business Logic should calculate average grade" time="0.001">
    </testcase>
    <testcase classname="Grade Model (Mocked) Business Logic should check if grade is late" name="Grade Model (Mocked) Business Logic should check if grade is late" time="0">
    </testcase>
  </testsuite>
  <testsuite name="LTI Authentication Middleware" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:03" time="0.408" tests="31">
    <testcase classname="LTI Authentication Middleware authenticateLTI should authenticate with Bearer token" name="LTI Authentication Middleware authenticateLTI should authenticate with Bearer token" time="0.002">
    </testcase>
    <testcase classname="LTI Authentication Middleware authenticateLTI should authenticate with session data" name="LTI Authentication Middleware authenticateLTI should authenticate with session data" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware authenticateLTI should authenticate with body token" name="LTI Authentication Middleware authenticateLTI should authenticate with body token" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware authenticateLTI should reject request without token" name="LTI Authentication Middleware authenticateLTI should reject request without token" time="0.005">
    </testcase>
    <testcase classname="LTI Authentication Middleware authenticateLTI should handle invalid token format" name="LTI Authentication Middleware authenticateLTI should handle invalid token format" time="0.002">
    </testcase>
    <testcase classname="LTI Authentication Middleware authenticateLTI should handle unknown platform" name="LTI Authentication Middleware authenticateLTI should handle unknown platform" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware authenticateLTI should create new user if not exists" name="LTI Authentication Middleware authenticateLTI should create new user if not exists" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware authenticateLTI should update user email if changed" name="LTI Authentication Middleware authenticateLTI should update user email if changed" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware requireLTIRole should allow access with required role" name="LTI Authentication Middleware requireLTIRole should allow access with required role" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware requireLTIRole should reject access without required role" name="LTI Authentication Middleware requireLTIRole should reject access without required role" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware requireLTIRole should reject access without LTI context" name="LTI Authentication Middleware requireLTIRole should reject access without LTI context" time="0">
    </testcase>
    <testcase classname="LTI Authentication Middleware requireLTIRole should allow access with partial role match" name="LTI Authentication Middleware requireLTIRole should allow access with partial role match" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware requireLTIContext should allow access with LTI context" name="LTI Authentication Middleware requireLTIContext should allow access with LTI context" time="0">
    </testcase>
    <testcase classname="LTI Authentication Middleware requireLTIContext should reject access without LTI context" name="LTI Authentication Middleware requireLTIContext should reject access without LTI context" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware validateLTIMessageType should allow valid message type" name="LTI Authentication Middleware validateLTIMessageType should allow valid message type" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware validateLTIMessageType should reject invalid message type" name="LTI Authentication Middleware validateLTIMessageType should reject invalid message type" time="0">
    </testcase>
    <testcase classname="LTI Authentication Middleware validateLTIMessageType should reject without launch data" name="LTI Authentication Middleware validateLTIMessageType should reject without launch data" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware validateLTIMessageType should allow multiple valid message types" name="LTI Authentication Middleware validateLTIMessageType should allow multiple valid message types" time="0.003">
    </testcase>
    <testcase classname="LTI Authentication Middleware extractLTICustomParams should extract custom parameters" name="LTI Authentication Middleware extractLTICustomParams should extract custom parameters" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware extractLTICustomParams should set empty object when no custom parameters" name="LTI Authentication Middleware extractLTICustomParams should set empty object when no custom parameters" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware extractLTICustomParams should set empty object when no launch data" name="LTI Authentication Middleware extractLTICustomParams should set empty object when no launch data" time="0">
    </testcase>
    <testcase classname="LTI Authentication Middleware requireGradePassback should allow access with grade passback capability" name="LTI Authentication Middleware requireGradePassback should allow access with grade passback capability" time="0">
    </testcase>
    <testcase classname="LTI Authentication Middleware requireGradePassback should reject without launch data" name="LTI Authentication Middleware requireGradePassback should reject without launch data" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware requireGradePassback should reject without AGS claim" name="LTI Authentication Middleware requireGradePassback should reject without AGS claim" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware requireGradePassback should reject without score scope" name="LTI Authentication Middleware requireGradePassback should reject without score scope" time="0">
    </testcase>
    <testcase classname="LTI Authentication Middleware validatePlatform should validate registered platform" name="LTI Authentication Middleware validatePlatform should validate registered platform" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware validatePlatform should reject missing parameters" name="LTI Authentication Middleware validatePlatform should reject missing parameters" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware validatePlatform should reject unregistered platform" name="LTI Authentication Middleware validatePlatform should reject unregistered platform" time="0">
    </testcase>
    <testcase classname="LTI Authentication Middleware rateLimitLTI should allow request (placeholder implementation)" name="LTI Authentication Middleware rateLimitLTI should allow request (placeholder implementation)" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware logLTIActivity should log LTI activity with full data" name="LTI Authentication Middleware logLTIActivity should log LTI activity with full data" time="0.001">
    </testcase>
    <testcase classname="LTI Authentication Middleware logLTIActivity should log LTI activity with minimal data" name="LTI Authentication Middleware logLTIActivity should log LTI activity with minimal data" time="0">
    </testcase>
  </testsuite>
  <testsuite name="RBAC Middleware" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:03" time="0.261" tests="22">
    <testcase classname="RBAC Middleware requirePermissions should allow user with required permission" name="RBAC Middleware requirePermissions should allow user with required permission" time="0.001">
    </testcase>
    <testcase classname="RBAC Middleware requirePermissions should reject user without required permission" name="RBAC Middleware requirePermissions should reject user without required permission" time="0.001">
    </testcase>
    <testcase classname="RBAC Middleware requirePermissions should allow super admin to bypass permission checks" name="RBAC Middleware requirePermissions should allow super admin to bypass permission checks" time="0">
    </testcase>
    <testcase classname="RBAC Middleware requirePermissions should reject unauthenticated user" name="RBAC Middleware requirePermissions should reject unauthenticated user" time="0.001">
    </testcase>
    <testcase classname="RBAC Middleware requirePermissions should handle multiple permissions with requireAll=true (default)" name="RBAC Middleware requirePermissions should handle multiple permissions with requireAll=true (default)" time="0.001">
    </testcase>
    <testcase classname="RBAC Middleware requirePermissions should reject when user missing any permission with requireAll=true" name="RBAC Middleware requirePermissions should reject when user missing any permission with requireAll=true" time="0">
    </testcase>
    <testcase classname="RBAC Middleware requirePermissions should allow when user has any permission with requireAll=false" name="RBAC Middleware requirePermissions should allow when user has any permission with requireAll=false" time="0.001">
    </testcase>
    <testcase classname="RBAC Middleware requireRoles should allow user with required role" name="RBAC Middleware requireRoles should allow user with required role" time="0.004">
    </testcase>
    <testcase classname="RBAC Middleware requireRoles should reject user without required role" name="RBAC Middleware requireRoles should reject user without required role" time="0">
    </testcase>
    <testcase classname="RBAC Middleware requireRoles should allow super admin to bypass role checks" name="RBAC Middleware requireRoles should allow super admin to bypass role checks" time="0">
    </testcase>
    <testcase classname="RBAC Middleware requireRoles should handle multiple roles with requireAll=true (default)" name="RBAC Middleware requireRoles should handle multiple roles with requireAll=true (default)" time="0.001">
    </testcase>
    <testcase classname="RBAC Middleware requireRoles should allow when user has any role with requireAll=false" name="RBAC Middleware requireRoles should allow when user has any role with requireAll=false" time="0">
    </testcase>
    <testcase classname="RBAC Middleware requireResourceAccess should allow super admin access to any resource" name="RBAC Middleware requireResourceAccess should allow super admin access to any resource" time="0.001">
    </testcase>
    <testcase classname="RBAC Middleware requireResourceAccess should allow admin access to any resource" name="RBAC Middleware requireResourceAccess should allow admin access to any resource" time="0.001">
    </testcase>
    <testcase classname="RBAC Middleware requireResourceAccess should reject unauthenticated user" name="RBAC Middleware requireResourceAccess should reject unauthenticated user" time="0.001">
    </testcase>
    <testcase classname="RBAC Middleware requireResourceAccess should reject invalid resource type" name="RBAC Middleware requireResourceAccess should reject invalid resource type" time="0.04">
    </testcase>
    <testcase classname="RBAC Middleware hasPermission utility should return true for user with permission" name="RBAC Middleware hasPermission utility should return true for user with permission" time="0">
    </testcase>
    <testcase classname="RBAC Middleware hasPermission utility should return true for super admin" name="RBAC Middleware hasPermission utility should return true for super admin" time="0.001">
    </testcase>
    <testcase classname="RBAC Middleware hasPermission utility should return false for user without permission" name="RBAC Middleware hasPermission utility should return false for user without permission" time="0.004">
    </testcase>
    <testcase classname="RBAC Middleware hasRole utility should return true for user with role" name="RBAC Middleware hasRole utility should return true for user with role" time="0.001">
    </testcase>
    <testcase classname="RBAC Middleware hasRole utility should return true for super admin" name="RBAC Middleware hasRole utility should return true for super admin" time="0">
    </testcase>
    <testcase classname="RBAC Middleware hasRole utility should return false for user without role" name="RBAC Middleware hasRole utility should return false for user without role" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="Auth Middleware" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:04" time="0.247" tests="21">
    <testcase classname="Auth Middleware jwtMiddleware should authenticate user with valid token" name="Auth Middleware jwtMiddleware should authenticate user with valid token" time="0.002">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should reject request without authorization header" name="Auth Middleware jwtMiddleware should reject request without authorization header" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should reject request with invalid authorization format" name="Auth Middleware jwtMiddleware should reject request with invalid authorization format" time="0">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should handle expired token" name="Auth Middleware jwtMiddleware should handle expired token" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should handle malformed token" name="Auth Middleware jwtMiddleware should handle malformed token" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should handle user not found" name="Auth Middleware jwtMiddleware should handle user not found" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should handle inactive user" name="Auth Middleware jwtMiddleware should handle inactive user" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should handle user with no roles" name="Auth Middleware jwtMiddleware should handle user with no roles" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should handle user with roles but no permissions" name="Auth Middleware jwtMiddleware should handle user with roles but no permissions" time="0">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should handle duplicate permissions" name="Auth Middleware jwtMiddleware should handle duplicate permissions" time="0.004">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should handle multiple roles with permissions" name="Auth Middleware jwtMiddleware should handle multiple roles with permissions" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should handle unexpected JWT errors" name="Auth Middleware jwtMiddleware should handle unexpected JWT errors" time="0">
    </testcase>
    <testcase classname="Auth Middleware jwtMiddleware should handle database errors" name="Auth Middleware jwtMiddleware should handle database errors" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware optionalJwtMiddleware should proceed without token" name="Auth Middleware optionalJwtMiddleware should proceed without token" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware optionalJwtMiddleware should validate token when provided" name="Auth Middleware optionalJwtMiddleware should validate token when provided" time="0">
    </testcase>
    <testcase classname="Auth Middleware optionalJwtMiddleware should handle invalid token format" name="Auth Middleware optionalJwtMiddleware should handle invalid token format" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware generateToken should generate valid JWT token" name="Auth Middleware generateToken should generate valid JWT token" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware generateToken should use default options when environment variables are not set" name="Auth Middleware generateToken should use default options when environment variables are not set" time="0">
    </testcase>
    <testcase classname="Auth Middleware verifyToken should verify valid token" name="Auth Middleware verifyToken should verify valid token" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware verifyToken should return null for invalid token" name="Auth Middleware verifyToken should return null for invalid token" time="0.001">
    </testcase>
    <testcase classname="Auth Middleware verifyToken should return null for expired token" name="Auth Middleware verifyToken should return null for expired token" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Sandbox Settings Controller (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:04" time="0.214" tests="7">
    <testcase classname="Sandbox Settings Controller (Mocked) GET /api/sandbox/projects/:projectId/sandbox-settings should get sandbox settings for project" name="Sandbox Settings Controller (Mocked) GET /api/sandbox/projects/:projectId/sandbox-settings should get sandbox settings for project" time="0.001">
    </testcase>
    <testcase classname="Sandbox Settings Controller (Mocked) GET /api/sandbox/projects/:projectId/sandbox-settings should return 404 for non-existent project" name="Sandbox Settings Controller (Mocked) GET /api/sandbox/projects/:projectId/sandbox-settings should return 404 for non-existent project" time="0.001">
    </testcase>
    <testcase classname="Sandbox Settings Controller (Mocked) GET /api/sandbox/projects/:projectId/sandbox-settings should return default settings when no settings exist" name="Sandbox Settings Controller (Mocked) GET /api/sandbox/projects/:projectId/sandbox-settings should return default settings when no settings exist" time="0.001">
    </testcase>
    <testcase classname="Sandbox Settings Controller (Mocked) POST /api/sandbox/projects/:projectId/sandbox-settings should create sandbox settings" name="Sandbox Settings Controller (Mocked) POST /api/sandbox/projects/:projectId/sandbox-settings should create sandbox settings" time="0.001">
    </testcase>
    <testcase classname="Sandbox Settings Controller (Mocked) POST /api/sandbox/projects/:projectId/sandbox-settings should return 400 for invalid mode" name="Sandbox Settings Controller (Mocked) POST /api/sandbox/projects/:projectId/sandbox-settings should return 400 for invalid mode" time="0.001">
    </testcase>
    <testcase classname="Sandbox Settings Controller (Mocked) PUT /api/sandbox/projects/:projectId/sandbox-settings should update sandbox settings" name="Sandbox Settings Controller (Mocked) PUT /api/sandbox/projects/:projectId/sandbox-settings should update sandbox settings" time="0.001">
    </testcase>
    <testcase classname="Sandbox Settings Controller (Mocked) DELETE /api/sandbox/projects/:projectId/sandbox-settings should delete sandbox settings" name="Sandbox Settings Controller (Mocked) DELETE /api/sandbox/projects/:projectId/sandbox-settings should delete sandbox settings" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Utility Functions" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:04" time="0.027" tests="6">
    <testcase classname="Utility Functions should perform basic math operations" name="Utility Functions should perform basic math operations" time="0">
    </testcase>
    <testcase classname="Utility Functions should handle string operations" name="Utility Functions should handle string operations" time="0">
    </testcase>
    <testcase classname="Utility Functions should handle array operations" name="Utility Functions should handle array operations" time="0.001">
    </testcase>
    <testcase classname="Utility Functions should handle object operations" name="Utility Functions should handle object operations" time="0">
    </testcase>
    <testcase classname="Utility Functions should handle async operations" name="Utility Functions should handle async operations" time="0.001">
    </testcase>
    <testcase classname="Utility Functions should handle error cases" name="Utility Functions should handle error cases" time="0.005">
    </testcase>
  </testsuite>
  <testsuite name="User Controller" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:04" time="0.068" tests="17">
    <testcase classname="User Controller getUsers should get all users with pagination" name="User Controller getUsers should get all users with pagination" time="0.001">
    </testcase>
    <testcase classname="User Controller getUsers should filter users by role" name="User Controller getUsers should filter users by role" time="0.003">
    </testcase>
    <testcase classname="User Controller getUsers should handle database errors" name="User Controller getUsers should handle database errors" time="0.001">
    </testcase>
    <testcase classname="User Controller getUserById should get user by ID" name="User Controller getUserById should get user by ID" time="0.001">
    </testcase>
    <testcase classname="User Controller getUserById should return 404 when user not found" name="User Controller getUserById should return 404 when user not found" time="0.001">
    </testcase>
    <testcase classname="User Controller updateUser should update user successfully" name="User Controller updateUser should update user successfully" time="0">
    </testcase>
    <testcase classname="User Controller updateUser should return 404 when user not found" name="User Controller updateUser should return 404 when user not found" time="0.001">
    </testcase>
    <testcase classname="User Controller updateUserStatus should update user status successfully" name="User Controller updateUserStatus should update user status successfully" time="0.001">
    </testcase>
    <testcase classname="User Controller updateUserStatus should return 404 when user not found" name="User Controller updateUserStatus should return 404 when user not found" time="0">
    </testcase>
    <testcase classname="User Controller assignUserRole should assign role to user successfully" name="User Controller assignUserRole should assign role to user successfully" time="0.001">
    </testcase>
    <testcase classname="User Controller assignUserRole should return 404 when user not found" name="User Controller assignUserRole should return 404 when user not found" time="0">
    </testcase>
    <testcase classname="User Controller assignUserRole should return 404 when role not found" name="User Controller assignUserRole should return 404 when role not found" time="0">
    </testcase>
    <testcase classname="User Controller removeUserRole should remove role from user successfully" name="User Controller removeUserRole should remove role from user successfully" time="0.001">
    </testcase>
    <testcase classname="User Controller removeUserRole should return 404 when user not found" name="User Controller removeUserRole should return 404 when user not found" time="0.001">
    </testcase>
    <testcase classname="User Controller removeUserRole should return 404 when role not found" name="User Controller removeUserRole should return 404 when role not found" time="0">
    </testcase>
    <testcase classname="User Controller getUserStatistics should get user statistics successfully" name="User Controller getUserStatistics should get user statistics successfully" time="0">
    </testcase>
    <testcase classname="User Controller getUserStatistics should handle database errors" name="User Controller getUserStatistics should handle database errors" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Health Endpoint (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:04" time="0.02" tests="5">
    <testcase classname="Health Endpoint (Mocked) GET /health should return 200 with health status" name="Health Endpoint (Mocked) GET /health should return 200 with health status" time="0.001">
    </testcase>
    <testcase classname="Health Endpoint (Mocked) GET /health should validate response structure" name="Health Endpoint (Mocked) GET /health should validate response structure" time="0.001">
    </testcase>
    <testcase classname="Health Endpoint (Mocked) GET /health should handle different environments" name="Health Endpoint (Mocked) GET /health should handle different environments" time="0">
    </testcase>
    <testcase classname="Health Endpoint (Mocked) GET /health should include database status when available" name="Health Endpoint (Mocked) GET /health should include database status when available" time="0.001">
    </testcase>
    <testcase classname="Health Endpoint (Mocked) GET /health should handle service failures" name="Health Endpoint (Mocked) GET /health should handle service failures" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Submission Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:04" time="0.052" tests="39">
    <testcase classname="Submission Model (Mocked) Model Definition should define Submission model with correct attributes" name="Submission Model (Mocked) Model Definition should define Submission model with correct attributes" time="0.002">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Definition should define table name correctly" name="Submission Model (Mocked) Model Definition should define table name correctly" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Definition should define indexes correctly" name="Submission Model (Mocked) Model Definition should define indexes correctly" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate required fields" name="Submission Model (Mocked) Model Validation should validate required fields" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate status enum values" name="Submission Model (Mocked) Model Validation should validate status enum values" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate attempt number" name="Submission Model (Mocked) Model Validation should validate attempt number" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate days late" name="Submission Model (Mocked) Model Validation should validate days late" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate UUID format" name="Submission Model (Mocked) Model Validation should validate UUID format" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate S3 URL format" name="Submission Model (Mocked) Model Validation should validate S3 URL format" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate JSONB fields" name="Submission Model (Mocked) Model Validation should validate JSONB fields" time="0.003">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Validation should validate additional files array" name="Submission Model (Mocked) Model Validation should validate additional files array" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should create submission successfully" name="Submission Model (Mocked) Model Methods should create submission successfully" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find submission by user and project" name="Submission Model (Mocked) Model Methods should find submission by user and project" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find submission by ID" name="Submission Model (Mocked) Model Methods should find submission by ID" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find submissions by user" name="Submission Model (Mocked) Model Methods should find submissions by user" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find submissions by project" name="Submission Model (Mocked) Model Methods should find submissions by project" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should update submission" name="Submission Model (Mocked) Model Methods should update submission" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should delete submission" name="Submission Model (Mocked) Model Methods should delete submission" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find submissions by status" name="Submission Model (Mocked) Model Methods should find submissions by status" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should find late submissions" name="Submission Model (Mocked) Model Methods should find late submissions" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Methods should count submissions" name="Submission Model (Mocked) Model Methods should count submissions" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Hooks should set default values before create" name="Submission Model (Mocked) Model Hooks should set default values before create" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Hooks should calculate days late" name="Submission Model (Mocked) Model Hooks should calculate days late" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Hooks should validate unique user-project-attempt combination" name="Submission Model (Mocked) Model Hooks should validate unique user-project-attempt combination" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Hooks should sanitize student comments" name="Submission Model (Mocked) Model Hooks should sanitize student comments" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Associations should have user association" name="Submission Model (Mocked) Model Associations should have user association" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Associations should have project association" name="Submission Model (Mocked) Model Associations should have project association" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Associations should have grade association" name="Submission Model (Mocked) Model Associations should have grade association" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Scopes should have by status scope" name="Submission Model (Mocked) Model Scopes should have by status scope" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Scopes should have by user scope" name="Submission Model (Mocked) Model Scopes should have by user scope" time="0.002">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Scopes should have by project scope" name="Submission Model (Mocked) Model Scopes should have by project scope" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Model Scopes should have late submissions scope" name="Submission Model (Mocked) Model Scopes should have late submissions scope" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Error Handling should handle duplicate submission error" name="Submission Model (Mocked) Error Handling should handle duplicate submission error" time="0.012">
    </testcase>
    <testcase classname="Submission Model (Mocked) Error Handling should handle validation error" name="Submission Model (Mocked) Error Handling should handle validation error" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Error Handling should handle submission not found" name="Submission Model (Mocked) Error Handling should handle submission not found" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Business Logic should check if submission is late" name="Submission Model (Mocked) Business Logic should check if submission is late" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Business Logic should check if submission is complete" name="Submission Model (Mocked) Business Logic should check if submission is complete" time="0.001">
    </testcase>
    <testcase classname="Submission Model (Mocked) Business Logic should calculate submission progress" name="Submission Model (Mocked) Business Logic should calculate submission progress" time="0">
    </testcase>
    <testcase classname="Submission Model (Mocked) Business Logic should get next attempt number" name="Submission Model (Mocked) Business Logic should get next attempt number" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Project Controller" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:04" time="0.063" tests="17">
    <testcase classname="Project Controller getProjects should get all projects with pagination" name="Project Controller getProjects should get all projects with pagination" time="0.001">
    </testcase>
    <testcase classname="Project Controller getProjects should filter projects by course ID" name="Project Controller getProjects should filter projects by course ID" time="0.001">
    </testcase>
    <testcase classname="Project Controller getProjects should handle database errors" name="Project Controller getProjects should handle database errors" time="0.004">
    </testcase>
    <testcase classname="Project Controller getProjectById should get project by ID" name="Project Controller getProjectById should get project by ID" time="0.001">
    </testcase>
    <testcase classname="Project Controller getProjectById should return 404 when project not found" name="Project Controller getProjectById should return 404 when project not found" time="0.001">
    </testcase>
    <testcase classname="Project Controller createProject should create a new project" name="Project Controller createProject should create a new project" time="0.001">
    </testcase>
    <testcase classname="Project Controller createProject should return 400 when course not found" name="Project Controller createProject should return 400 when course not found" time="0">
    </testcase>
    <testcase classname="Project Controller updateProject should update project successfully" name="Project Controller updateProject should update project successfully" time="0">
    </testcase>
    <testcase classname="Project Controller updateProject should return 404 when project not found" name="Project Controller updateProject should return 404 when project not found" time="0.001">
    </testcase>
    <testcase classname="Project Controller duplicateProject should duplicate project successfully" name="Project Controller duplicateProject should duplicate project successfully" time="0.001">
    </testcase>
    <testcase classname="Project Controller duplicateProject should return 404 when project not found" name="Project Controller duplicateProject should return 404 when project not found" time="0">
    </testcase>
    <testcase classname="Project Controller deleteProject should delete project successfully" name="Project Controller deleteProject should delete project successfully" time="0.001">
    </testcase>
    <testcase classname="Project Controller deleteProject should return 404 when project not found" name="Project Controller deleteProject should return 404 when project not found" time="0.001">
    </testcase>
    <testcase classname="Project Controller publishProject should publish project successfully" name="Project Controller publishProject should publish project successfully" time="0">
    </testcase>
    <testcase classname="Project Controller publishProject should return 404 when project not found" name="Project Controller publishProject should return 404 when project not found" time="0.001">
    </testcase>
    <testcase classname="Project Controller getProjectStatistics should get project statistics successfully" name="Project Controller getProjectStatistics should get project statistics successfully" time="0.001">
    </testcase>
    <testcase classname="Project Controller getProjectStatistics should handle database errors" name="Project Controller getProjectStatistics should handle database errors" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Project Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:04" time="0.056" tests="32">
    <testcase classname="Project Model (Mocked) Model Definition should define Project model with correct attributes" name="Project Model (Mocked) Model Definition should define Project model with correct attributes" time="0.002">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Definition should define table name correctly" name="Project Model (Mocked) Model Definition should define table name correctly" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Definition should define timestamps" name="Project Model (Mocked) Model Definition should define timestamps" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Validation should validate required fields" name="Project Model (Mocked) Model Validation should validate required fields" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Validation should validate estimated hours" name="Project Model (Mocked) Model Validation should validate estimated hours" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Validation should validate UUID format for createdBy" name="Project Model (Mocked) Model Validation should validate UUID format for createdBy" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Validation should validate tags array" name="Project Model (Mocked) Model Validation should validate tags array" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Validation should validate settings object" name="Project Model (Mocked) Model Validation should validate settings object" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should create project successfully" name="Project Model (Mocked) Model Methods should create project successfully" time="0.002">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should find project by title" name="Project Model (Mocked) Model Methods should find project by title" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should find project by ID" name="Project Model (Mocked) Model Methods should find project by ID" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should find all active projects" name="Project Model (Mocked) Model Methods should find all active projects" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should find projects by creator" name="Project Model (Mocked) Model Methods should find projects by creator" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should update project" name="Project Model (Mocked) Model Methods should update project" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should delete project" name="Project Model (Mocked) Model Methods should delete project" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should find projects by tags" name="Project Model (Mocked) Model Methods should find projects by tags" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Methods should count projects" name="Project Model (Mocked) Model Methods should count projects" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Hooks should set default values before create" name="Project Model (Mocked) Model Hooks should set default values before create" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Hooks should validate title length" name="Project Model (Mocked) Model Hooks should validate title length" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Hooks should sanitize description" name="Project Model (Mocked) Model Hooks should sanitize description" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Associations should have creator association" name="Project Model (Mocked) Model Associations should have creator association" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Associations should have submissions association" name="Project Model (Mocked) Model Associations should have submissions association" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Associations should have sandbox settings association" name="Project Model (Mocked) Model Associations should have sandbox settings association" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Scopes should have active scope" name="Project Model (Mocked) Model Scopes should have active scope" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Scopes should have inactive scope" name="Project Model (Mocked) Model Scopes should have inactive scope" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Model Scopes should have by creator scope" name="Project Model (Mocked) Model Scopes should have by creator scope" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Error Handling should handle validation error" name="Project Model (Mocked) Error Handling should handle validation error" time="0.01">
    </testcase>
    <testcase classname="Project Model (Mocked) Error Handling should handle foreign key constraint error" name="Project Model (Mocked) Error Handling should handle foreign key constraint error" time="0.001">
    </testcase>
    <testcase classname="Project Model (Mocked) Error Handling should handle project not found" name="Project Model (Mocked) Error Handling should handle project not found" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Business Logic should calculate completion percentage" name="Project Model (Mocked) Business Logic should calculate completion percentage" time="0.003">
    </testcase>
    <testcase classname="Project Model (Mocked) Business Logic should check if project is overdue" name="Project Model (Mocked) Business Logic should check if project is overdue" time="0">
    </testcase>
    <testcase classname="Project Model (Mocked) Business Logic should get project difficulty level" name="Project Model (Mocked) Business Logic should get project difficulty level" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Course Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:04" time="0.049" tests="34">
    <testcase classname="Course Model (Mocked) Model Definition should define Course model with correct attributes" name="Course Model (Mocked) Model Definition should define Course model with correct attributes" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Definition should define table name correctly" name="Course Model (Mocked) Model Definition should define table name correctly" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Definition should define indexes correctly" name="Course Model (Mocked) Model Definition should define indexes correctly" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate required fields" name="Course Model (Mocked) Model Validation should validate required fields" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate name length" name="Course Model (Mocked) Model Validation should validate name length" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate code length" name="Course Model (Mocked) Model Validation should validate code length" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate status enum values" name="Course Model (Mocked) Model Validation should validate status enum values" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate UUID format for instructor_id" name="Course Model (Mocked) Model Validation should validate UUID format for instructor_id" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate date formats" name="Course Model (Mocked) Model Validation should validate date formats" time="0.002">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Validation should validate JSONB fields" name="Course Model (Mocked) Model Validation should validate JSONB fields" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should create course successfully" name="Course Model (Mocked) Model Methods should create course successfully" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should find course by LMS ID" name="Course Model (Mocked) Model Methods should find course by LMS ID" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should find course by ID" name="Course Model (Mocked) Model Methods should find course by ID" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should find all active courses" name="Course Model (Mocked) Model Methods should find all active courses" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should find courses by instructor" name="Course Model (Mocked) Model Methods should find courses by instructor" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should update course" name="Course Model (Mocked) Model Methods should update course" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should delete course" name="Course Model (Mocked) Model Methods should delete course" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should find courses by term" name="Course Model (Mocked) Model Methods should find courses by term" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Methods should count courses" name="Course Model (Mocked) Model Methods should count courses" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Hooks should set default values before create" name="Course Model (Mocked) Model Hooks should set default values before create" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Hooks should validate unique LMS course ID" name="Course Model (Mocked) Model Hooks should validate unique LMS course ID" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Hooks should sanitize course name" name="Course Model (Mocked) Model Hooks should sanitize course name" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Associations should have instructor association" name="Course Model (Mocked) Model Associations should have instructor association" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Associations should have enrollments association" name="Course Model (Mocked) Model Associations should have enrollments association" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Associations should have projects association" name="Course Model (Mocked) Model Associations should have projects association" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Scopes should have active scope" name="Course Model (Mocked) Model Scopes should have active scope" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Scopes should have by instructor scope" name="Course Model (Mocked) Model Scopes should have by instructor scope" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Model Scopes should have by term scope" name="Course Model (Mocked) Model Scopes should have by term scope" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Error Handling should handle duplicate LMS course ID error" name="Course Model (Mocked) Error Handling should handle duplicate LMS course ID error" time="0.013">
    </testcase>
    <testcase classname="Course Model (Mocked) Error Handling should handle validation error" name="Course Model (Mocked) Error Handling should handle validation error" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Error Handling should handle course not found" name="Course Model (Mocked) Error Handling should handle course not found" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Business Logic should check if course is active" name="Course Model (Mocked) Business Logic should check if course is active" time="0">
    </testcase>
    <testcase classname="Course Model (Mocked) Business Logic should check if course is within date range" name="Course Model (Mocked) Business Logic should check if course is within date range" time="0.001">
    </testcase>
    <testcase classname="Course Model (Mocked) Business Logic should get course duration in days" name="Course Model (Mocked) Business Logic should get course duration in days" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Role Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:04" time="0.054" tests="31">
    <testcase classname="Role Model (Mocked) Model Definition should define Role model with correct attributes" name="Role Model (Mocked) Model Definition should define Role model with correct attributes" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Definition should define table name correctly" name="Role Model (Mocked) Model Definition should define table name correctly" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Definition should define indexes correctly" name="Role Model (Mocked) Model Definition should define indexes correctly" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Validation should validate required fields" name="Role Model (Mocked) Model Validation should validate required fields" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Validation should validate name length" name="Role Model (Mocked) Model Validation should validate name length" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Validation should validate priority range" name="Role Model (Mocked) Model Validation should validate priority range" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Validation should validate UUID format" name="Role Model (Mocked) Model Validation should validate UUID format" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Validation should validate JSONB fields" name="Role Model (Mocked) Model Validation should validate JSONB fields" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should create role successfully" name="Role Model (Mocked) Model Methods should create role successfully" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should find role by name" name="Role Model (Mocked) Model Methods should find role by name" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should find role by ID" name="Role Model (Mocked) Model Methods should find role by ID" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should find all system roles" name="Role Model (Mocked) Model Methods should find all system roles" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should update role" name="Role Model (Mocked) Model Methods should update role" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should delete role" name="Role Model (Mocked) Model Methods should delete role" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should find roles by priority" name="Role Model (Mocked) Model Methods should find roles by priority" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Methods should count roles" name="Role Model (Mocked) Model Methods should count roles" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Hooks should set default values before create" name="Role Model (Mocked) Model Hooks should set default values before create" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Hooks should validate unique role name" name="Role Model (Mocked) Model Hooks should validate unique role name" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Hooks should sanitize role name" name="Role Model (Mocked) Model Hooks should sanitize role name" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Associations should have users association" name="Role Model (Mocked) Model Associations should have users association" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Associations should have permissions association" name="Role Model (Mocked) Model Associations should have permissions association" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Scopes should have system roles scope" name="Role Model (Mocked) Model Scopes should have system roles scope" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Scopes should have by priority scope" name="Role Model (Mocked) Model Scopes should have by priority scope" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Model Scopes should have by name scope" name="Role Model (Mocked) Model Scopes should have by name scope" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Error Handling should handle duplicate role name error" name="Role Model (Mocked) Error Handling should handle duplicate role name error" time="0.009">
    </testcase>
    <testcase classname="Role Model (Mocked) Error Handling should handle validation error" name="Role Model (Mocked) Error Handling should handle validation error" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Error Handling should handle role not found" name="Role Model (Mocked) Error Handling should handle role not found" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Business Logic should check if role is system role" name="Role Model (Mocked) Business Logic should check if role is system role" time="0.002">
    </testcase>
    <testcase classname="Role Model (Mocked) Business Logic should check role hierarchy" name="Role Model (Mocked) Business Logic should check role hierarchy" time="0.001">
    </testcase>
    <testcase classname="Role Model (Mocked) Business Logic should get role permissions" name="Role Model (Mocked) Business Logic should get role permissions" time="0">
    </testcase>
    <testcase classname="Role Model (Mocked) Business Logic should check if role can be deleted" name="Role Model (Mocked) Business Logic should check if role can be deleted" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Course Controller" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:05" time="0.042" tests="27">
    <testcase classname="Course Controller getCourses should get all courses with pagination for admin" name="Course Controller getCourses should get all courses with pagination for admin" time="0.002">
    </testcase>
    <testcase classname="Course Controller getCourses should get courses with search filter" name="Course Controller getCourses should get courses with search filter" time="0.001">
    </testcase>
    <testcase classname="Course Controller getCourses should get courses with term filter" name="Course Controller getCourses should get courses with term filter" time="0">
    </testcase>
    <testcase classname="Course Controller getCourses should get courses with status filter" name="Course Controller getCourses should get courses with status filter" time="0">
    </testcase>
    <testcase classname="Course Controller getCourses should filter courses for non-admin users" name="Course Controller getCourses should filter courses for non-admin users" time="0.001">
    </testcase>
    <testcase classname="Course Controller getCourses should handle database errors" name="Course Controller getCourses should handle database errors" time="0">
    </testcase>
    <testcase classname="Course Controller getCourseById should get course by ID with full details" name="Course Controller getCourseById should get course by ID with full details" time="0.001">
    </testcase>
    <testcase classname="Course Controller getCourseById should return 404 when course not found" name="Course Controller getCourseById should return 404 when course not found" time="0">
    </testcase>
    <testcase classname="Course Controller getCourseById should return 403 when user has no access" name="Course Controller getCourseById should return 403 when user has no access" time="0.002">
    </testcase>
    <testcase classname="Course Controller getCourseById should allow access for instructor of the course" name="Course Controller getCourseById should allow access for instructor of the course" time="0.001">
    </testcase>
    <testcase classname="Course Controller getCourseEnrollments should get course enrollments successfully" name="Course Controller getCourseEnrollments should get course enrollments successfully" time="0.001">
    </testcase>
    <testcase classname="Course Controller getCourseEnrollments should return 404 when course not found" name="Course Controller getCourseEnrollments should return 404 when course not found" time="0.001">
    </testcase>
    <testcase classname="Course Controller getCourseEnrollments should return 403 when user has no permission" name="Course Controller getCourseEnrollments should return 403 when user has no permission" time="0">
    </testcase>
    <testcase classname="Course Controller getCourseEnrollments should filter enrollments by role" name="Course Controller getCourseEnrollments should filter enrollments by role" time="0.001">
    </testcase>
    <testcase classname="Course Controller getCourseEnrollments should filter enrollments by status" name="Course Controller getCourseEnrollments should filter enrollments by status" time="0">
    </testcase>
    <testcase classname="Course Controller enrollUserInCourse should enroll user in course successfully" name="Course Controller enrollUserInCourse should enroll user in course successfully" time="0.001">
    </testcase>
    <testcase classname="Course Controller enrollUserInCourse should return error if course not found" name="Course Controller enrollUserInCourse should return error if course not found" time="0.001">
    </testcase>
    <testcase classname="Course Controller enrollUserInCourse should return error if user not found" name="Course Controller enrollUserInCourse should return error if user not found" time="0">
    </testcase>
    <testcase classname="Course Controller enrollUserInCourse should return error if user already enrolled" name="Course Controller enrollUserInCourse should return error if user already enrolled" time="0">
    </testcase>
    <testcase classname="Course Controller enrollUserInCourse should return error if userId is missing" name="Course Controller enrollUserInCourse should return error if userId is missing" time="0">
    </testcase>
    <testcase classname="Course Controller updateCourseEnrollment should update course enrollment successfully" name="Course Controller updateCourseEnrollment should update course enrollment successfully" time="0.001">
    </testcase>
    <testcase classname="Course Controller updateCourseEnrollment should return 404 when enrollment not found" name="Course Controller updateCourseEnrollment should return 404 when enrollment not found" time="0.001">
    </testcase>
    <testcase classname="Course Controller updateCourseEnrollment should return 403 when user has no permission" name="Course Controller updateCourseEnrollment should return 403 when user has no permission" time="0">
    </testcase>
    <testcase classname="Course Controller updateCourseEnrollment should update multiple fields" name="Course Controller updateCourseEnrollment should update multiple fields" time="0">
    </testcase>
    <testcase classname="Course Controller getCourseStatistics should get course statistics successfully" name="Course Controller getCourseStatistics should get course statistics successfully" time="0.001">
    </testcase>
    <testcase classname="Course Controller getCourseStatistics should return 404 when course not found" name="Course Controller getCourseStatistics should return 404 when course not found" time="0.003">
    </testcase>
    <testcase classname="Course Controller getCourseStatistics should return 403 when user has no permission" name="Course Controller getCourseStatistics should return 403 when user has no permission" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="User Model (Mocked)" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:05" time="0.041" tests="25">
    <testcase classname="User Model (Mocked) Model Definition should define User model with correct attributes" name="User Model (Mocked) Model Definition should define User model with correct attributes" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Definition should define table name correctly" name="User Model (Mocked) Model Definition should define table name correctly" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Definition should define timestamps" name="User Model (Mocked) Model Definition should define timestamps" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Validation should validate email format" name="User Model (Mocked) Model Validation should validate email format" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Validation should validate required fields" name="User Model (Mocked) Model Validation should validate required fields" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Validation should validate UUID format" name="User Model (Mocked) Model Validation should validate UUID format" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should create user successfully" name="User Model (Mocked) Model Methods should create user successfully" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should find user by email" name="User Model (Mocked) Model Methods should find user by email" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should find user by ID" name="User Model (Mocked) Model Methods should find user by ID" time="0.002">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should find all active users" name="User Model (Mocked) Model Methods should find all active users" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should update user" name="User Model (Mocked) Model Methods should update user" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should delete user" name="User Model (Mocked) Model Methods should delete user" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should find or create user" name="User Model (Mocked) Model Methods should find or create user" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Methods should count users" name="User Model (Mocked) Model Methods should count users" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Hooks should hash password before create" name="User Model (Mocked) Model Hooks should hash password before create" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Hooks should hash password before update" name="User Model (Mocked) Model Hooks should hash password before update" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Hooks should exclude password from JSON" name="User Model (Mocked) Model Hooks should exclude password from JSON" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Associations should have roles association" name="User Model (Mocked) Model Associations should have roles association" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Associations should have courses association" name="User Model (Mocked) Model Associations should have courses association" time="0.001">
    </testcase>
    <testcase classname="User Model (Mocked) Model Associations should have projects association" name="User Model (Mocked) Model Associations should have projects association" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Scopes should have active scope" name="User Model (Mocked) Model Scopes should have active scope" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Model Scopes should have inactive scope" name="User Model (Mocked) Model Scopes should have inactive scope" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Error Handling should handle duplicate email error" name="User Model (Mocked) Error Handling should handle duplicate email error" time="0.007">
    </testcase>
    <testcase classname="User Model (Mocked) Error Handling should handle validation error" name="User Model (Mocked) Error Handling should handle validation error" time="0">
    </testcase>
    <testcase classname="User Model (Mocked) Error Handling should handle user not found" name="User Model (Mocked) Error Handling should handle user not found" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Auth Controller" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:05" time="0.044" tests="14">
    <testcase classname="Auth Controller googleCallback should handle successful Google authentication" name="Auth Controller googleCallback should handle successful Google authentication" time="0.001">
    </testcase>
    <testcase classname="Auth Controller googleCallback should handle authentication failure" name="Auth Controller googleCallback should handle authentication failure" time="0.001">
    </testcase>
    <testcase classname="Auth Controller googleCallback should handle missing user" name="Auth Controller googleCallback should handle missing user" time="0.001">
    </testcase>
    <testcase classname="Auth Controller getCurrentUser should get current user profile" name="Auth Controller getCurrentUser should get current user profile" time="0">
    </testcase>
    <testcase classname="Auth Controller refreshToken should refresh token successfully" name="Auth Controller refreshToken should refresh token successfully" time="0.001">
    </testcase>
    <testcase classname="Auth Controller logout should logout user successfully" name="Auth Controller logout should logout user successfully" time="0.001">
    </testcase>
    <testcase classname="Auth Controller updatePreferences should update user preferences successfully" name="Auth Controller updatePreferences should update user preferences successfully" time="0">
    </testcase>
    <testcase classname="Auth Controller updatePreferences should reject invalid preferences" name="Auth Controller updatePreferences should reject invalid preferences" time="0">
    </testcase>
    <testcase classname="Auth Controller updatePreferences should reject missing preferences" name="Auth Controller updatePreferences should reject missing preferences" time="0.001">
    </testcase>
    <testcase classname="Auth Controller login should login successfully with valid credentials" name="Auth Controller login should login successfully with valid credentials" time="0.001">
    </testcase>
    <testcase classname="Auth Controller login should reject login with missing credentials" name="Auth Controller login should reject login with missing credentials" time="0.001">
    </testcase>
    <testcase classname="Auth Controller login should reject login with invalid user" name="Auth Controller login should reject login with invalid user" time="0">
    </testcase>
    <testcase classname="Auth Controller login should reject login with inactive user" name="Auth Controller login should reject login with inactive user" time="0.001">
    </testcase>
    <testcase classname="Auth Controller login should reject login with invalid password" name="Auth Controller login should reject login with invalid password" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Database Configuration" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:05" time="0.034" tests="18">
    <testcase classname="Database Configuration Environment Configuration should configure development environment" name="Database Configuration Environment Configuration should configure development environment" time="0.004">
    </testcase>
    <testcase classname="Database Configuration Environment Configuration should configure production environment" name="Database Configuration Environment Configuration should configure production environment" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Environment Configuration should configure test environment" name="Database Configuration Environment Configuration should configure test environment" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Default Values should use default port when not specified" name="Database Configuration Default Values should use default port when not specified" time="0">
    </testcase>
    <testcase classname="Database Configuration Default Values should use default dialect" name="Database Configuration Default Values should use default dialect" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Default Values should handle missing environment variables" name="Database Configuration Default Values should handle missing environment variables" time="0">
    </testcase>
    <testcase classname="Database Configuration Configuration Validation should validate required database fields" name="Database Configuration Configuration Validation should validate required database fields" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Configuration Validation should validate port number" name="Database Configuration Configuration Validation should validate port number" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Configuration Validation should validate host format" name="Database Configuration Configuration Validation should validate host format" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Connection String Generation should generate connection string" name="Database Configuration Connection String Generation should generate connection string" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Connection String Generation should handle special characters in password" name="Database Configuration Connection String Generation should handle special characters in password" time="0">
    </testcase>
    <testcase classname="Database Configuration SSL Configuration should configure SSL for production" name="Database Configuration SSL Configuration should configure SSL for production" time="0.001">
    </testcase>
    <testcase classname="Database Configuration SSL Configuration should not configure SSL for development" name="Database Configuration SSL Configuration should not configure SSL for development" time="0">
    </testcase>
    <testcase classname="Database Configuration Pool Configuration should configure connection pool" name="Database Configuration Pool Configuration should configure connection pool" time="0.003">
    </testcase>
    <testcase classname="Database Configuration Pool Configuration should use environment variables for pool config" name="Database Configuration Pool Configuration should use environment variables for pool config" time="0">
    </testcase>
    <testcase classname="Database Configuration Logging Configuration should enable logging for development" name="Database Configuration Logging Configuration should enable logging for development" time="0">
    </testcase>
    <testcase classname="Database Configuration Logging Configuration should disable logging for production" name="Database Configuration Logging Configuration should disable logging for production" time="0.001">
    </testcase>
    <testcase classname="Database Configuration Logging Configuration should disable logging for test" name="Database Configuration Logging Configuration should disable logging for test" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Validation Middleware" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:05" time="0.031" tests="20">
    <testcase classname="Validation Middleware Email Validation should validate correct email format" name="Validation Middleware Email Validation should validate correct email format" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Email Validation should reject invalid email format" name="Validation Middleware Email Validation should reject invalid email format" time="0">
    </testcase>
    <testcase classname="Validation Middleware UUID Validation should validate correct UUID format" name="Validation Middleware UUID Validation should validate correct UUID format" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware UUID Validation should reject invalid UUID format" name="Validation Middleware UUID Validation should reject invalid UUID format" time="0">
    </testcase>
    <testcase classname="Validation Middleware Required Field Validation should validate required fields present" name="Validation Middleware Required Field Validation should validate required fields present" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Required Field Validation should reject missing required fields" name="Validation Middleware Required Field Validation should reject missing required fields" time="0">
    </testcase>
    <testcase classname="Validation Middleware Required Field Validation should handle empty string as missing field" name="Validation Middleware Required Field Validation should handle empty string as missing field" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware String Length Validation should validate string length within limits" name="Validation Middleware String Length Validation should validate string length within limits" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware String Length Validation should reject strings outside length limits" name="Validation Middleware String Length Validation should reject strings outside length limits" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Numeric Validation should validate numeric values" name="Validation Middleware Numeric Validation should validate numeric values" time="0">
    </testcase>
    <testcase classname="Validation Middleware Numeric Validation should reject invalid numeric values" name="Validation Middleware Numeric Validation should reject invalid numeric values" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Date Validation should validate date formats" name="Validation Middleware Date Validation should validate date formats" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Date Validation should reject invalid date formats" name="Validation Middleware Date Validation should reject invalid date formats" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Array Validation should validate array contents" name="Validation Middleware Array Validation should validate array contents" time="0">
    </testcase>
    <testcase classname="Validation Middleware Array Validation should reject invalid arrays" name="Validation Middleware Array Validation should reject invalid arrays" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware Object Validation should validate object structure" name="Validation Middleware Object Validation should validate object structure" time="0">
    </testcase>
    <testcase classname="Validation Middleware Object Validation should reject invalid objects" name="Validation Middleware Object Validation should reject invalid objects" time="0.001">
    </testcase>
    <testcase classname="Validation Middleware File Validation should validate file types" name="Validation Middleware File Validation should validate file types" time="0">
    </testcase>
    <testcase classname="Validation Middleware File Validation should reject invalid file types" name="Validation Middleware File Validation should reject invalid file types" time="0">
    </testcase>
    <testcase classname="Validation Middleware File Validation should validate file size" name="Validation Middleware File Validation should validate file size" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Utility Helper Functions" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:05" time="0.033" tests="25">
    <testcase classname="Utility Helper Functions String Utilities should capitalize first letter" name="Utility Helper Functions String Utilities should capitalize first letter" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions String Utilities should generate random string" name="Utility Helper Functions String Utilities should generate random string" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions String Utilities should slugify strings" name="Utility Helper Functions String Utilities should slugify strings" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions String Utilities should truncate strings" name="Utility Helper Functions String Utilities should truncate strings" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Array Utilities should remove duplicates from array" name="Utility Helper Functions Array Utilities should remove duplicates from array" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions Array Utilities should chunk array into smaller arrays" name="Utility Helper Functions Array Utilities should chunk array into smaller arrays" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Array Utilities should shuffle array" name="Utility Helper Functions Array Utilities should shuffle array" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions Array Utilities should group array by key" name="Utility Helper Functions Array Utilities should group array by key" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Object Utilities should deep clone object" name="Utility Helper Functions Object Utilities should deep clone object" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Object Utilities should pick specific keys from object" name="Utility Helper Functions Object Utilities should pick specific keys from object" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Object Utilities should omit specific keys from object" name="Utility Helper Functions Object Utilities should omit specific keys from object" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions Object Utilities should check if object is empty" name="Utility Helper Functions Object Utilities should check if object is empty" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Date Utilities should format date" name="Utility Helper Functions Date Utilities should format date" time="0.002">
    </testcase>
    <testcase classname="Utility Helper Functions Date Utilities should check if date is valid" name="Utility Helper Functions Date Utilities should check if date is valid" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Date Utilities should get relative time" name="Utility Helper Functions Date Utilities should get relative time" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions Number Utilities should format number with commas" name="Utility Helper Functions Number Utilities should format number with commas" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Number Utilities should round to decimal places" name="Utility Helper Functions Number Utilities should round to decimal places" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions Number Utilities should check if number is in range" name="Utility Helper Functions Number Utilities should check if number is in range" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions Number Utilities should generate random number in range" name="Utility Helper Functions Number Utilities should generate random number in range" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions Validation Utilities should validate email format" name="Utility Helper Functions Validation Utilities should validate email format" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions Validation Utilities should validate URL format" name="Utility Helper Functions Validation Utilities should validate URL format" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions Validation Utilities should validate phone number format" name="Utility Helper Functions Validation Utilities should validate phone number format" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions File Utilities should get file extension" name="Utility Helper Functions File Utilities should get file extension" time="0">
    </testcase>
    <testcase classname="Utility Helper Functions File Utilities should format file size" name="Utility Helper Functions File Utilities should format file size" time="0.001">
    </testcase>
    <testcase classname="Utility Helper Functions File Utilities should validate file type" name="Utility Helper Functions File Utilities should validate file type" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Validation Utilities" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:07" time="0.031" tests="14">
    <testcase classname="Validation Utilities Email Validation should validate correct email formats" name="Validation Utilities Email Validation should validate correct email formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities Email Validation should reject invalid email formats" name="Validation Utilities Email Validation should reject invalid email formats" time="0">
    </testcase>
    <testcase classname="Validation Utilities UUID Validation should validate correct UUID formats" name="Validation Utilities UUID Validation should validate correct UUID formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities UUID Validation should reject invalid UUID formats" name="Validation Utilities UUID Validation should reject invalid UUID formats" time="0">
    </testcase>
    <testcase classname="Validation Utilities Password Validation should validate strong passwords" name="Validation Utilities Password Validation should validate strong passwords" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities Password Validation should reject weak passwords" name="Validation Utilities Password Validation should reject weak passwords" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities URL Validation should validate correct URL formats" name="Validation Utilities URL Validation should validate correct URL formats" time="0">
    </testcase>
    <testcase classname="Validation Utilities URL Validation should reject invalid URL formats" name="Validation Utilities URL Validation should reject invalid URL formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities Date Validation should validate correct date formats" name="Validation Utilities Date Validation should validate correct date formats" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities Date Validation should reject invalid date formats" name="Validation Utilities Date Validation should reject invalid date formats" time="0">
    </testcase>
    <testcase classname="Validation Utilities File Extension Validation should validate allowed file extensions" name="Validation Utilities File Extension Validation should validate allowed file extensions" time="0.001">
    </testcase>
    <testcase classname="Validation Utilities File Extension Validation should reject disallowed file extensions" name="Validation Utilities File Extension Validation should reject disallowed file extensions" time="0">
    </testcase>
    <testcase classname="Validation Utilities Input Sanitization should sanitize HTML content" name="Validation Utilities Input Sanitization should sanitize HTML content" time="0">
    </testcase>
    <testcase classname="Validation Utilities Input Sanitization should trim whitespace" name="Validation Utilities Input Sanitization should trim whitespace" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Logger Configuration" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:07" time="0.026" tests="11">
    <testcase classname="Logger Configuration Environment Configuration should configure development logger" name="Logger Configuration Environment Configuration should configure development logger" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Environment Configuration should configure production logger" name="Logger Configuration Environment Configuration should configure production logger" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Environment Configuration should configure test logger" name="Logger Configuration Environment Configuration should configure test logger" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Log Level Configuration should handle different log levels" name="Logger Configuration Log Level Configuration should handle different log levels" time="0">
    </testcase>
    <testcase classname="Logger Configuration Log Level Configuration should use default log level when not specified" name="Logger Configuration Log Level Configuration should use default log level when not specified" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Log Formatting should format log messages correctly" name="Logger Configuration Log Formatting should format log messages correctly" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Log Formatting should handle log messages with metadata" name="Logger Configuration Log Formatting should handle log messages with metadata" time="0">
    </testcase>
    <testcase classname="Logger Configuration Error Handling should handle logger errors gracefully" name="Logger Configuration Error Handling should handle logger errors gracefully" time="0.001">
    </testcase>
    <testcase classname="Logger Configuration Error Handling should handle invalid log levels" name="Logger Configuration Error Handling should handle invalid log levels" time="0">
    </testcase>
    <testcase classname="Logger Configuration Performance Logging should log performance metrics" name="Logger Configuration Performance Logging should log performance metrics" time="0.003">
    </testcase>
    <testcase classname="Logger Configuration Security Logging should log security events" name="Logger Configuration Security Logging should log security events" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Health Check" errors="0" failures="0" skipped="0" timestamp="2025-08-17T12:28:07" time="0.017" tests="2">
    <testcase classname="Health Check should pass basic health check" name="Health Check should pass basic health check" time="0.001">
    </testcase>
    <testcase classname="Health Check should handle basic math" name="Health Check should handle basic math" time="0">
    </testcase>
  </testsuite>
  <testsuite name="Build Service" errors="0" failures="22" skipped="0" timestamp="2025-08-17T12:28:07" time="0.22" tests="22">
    <testcase classname="Build Service constructor should initialize with default values" name="Build Service constructor should initialize with default values" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service constructor should initialize with environment variables" name="Build Service constructor should initialize with environment variables" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service startImageBuild should start image build successfully" name="Build Service startImageBuild should start image build successfully" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service startImageBuild should start image build with default options" name="Build Service startImageBuild should start image build with default options" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service startImageBuild should handle build start failure" name="Build Service startImageBuild should handle build start failure" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service startImageBuild should use custom ECR repo from environment" name="Build Service startImageBuild should use custom ECR repo from environment" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildStatus should get build status successfully" name="Build Service getBuildStatus should get build status successfully" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildStatus should handle build not found" name="Build Service getBuildStatus should handle build not found" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildStatus should handle build status failure" name="Build Service getBuildStatus should handle build status failure" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildStatus should handle build with missing optional fields" name="Build Service getBuildStatus should handle build with missing optional fields" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service generateBuildSpec should generate valid buildspec" name="Build Service generateBuildSpec should generate valid buildspec" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service generateBuildSpec should include all required sections" name="Build Service generateBuildSpec should include all required sections" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service createBuildWebhook should create webhook successfully" name="Build Service createBuildWebhook should create webhook successfully" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service createBuildWebhook should handle webhook creation failure" name="Build Service createBuildWebhook should handle webhook creation failure" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service updateProjectImageUrl should update project image URL successfully" name="Build Service updateProjectImageUrl should update project image URL successfully" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service updateProjectImageUrl should handle project update failure" name="Build Service updateProjectImageUrl should handle project update failure" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service validateBuildRequirements should validate requirements successfully" name="Build Service validateBuildRequirements should validate requirements successfully" time="0.004">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service validateBuildRequirements should handle validation failure" name="Build Service validateBuildRequirements should handle validation failure" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildHistory should get build history successfully" name="Build Service getBuildHistory should get build history successfully" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service getBuildHistory should handle build history failure" name="Build Service getBuildHistory should handle build history failure" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service StartBuildCommand validation should create StartBuildCommand with correct parameters" name="Build Service StartBuildCommand validation should create StartBuildCommand with correct parameters" time="0">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Build Service BatchGetBuildsCommand validation should create BatchGetBuildsCommand with correct parameters" name="Build Service BatchGetBuildsCommand validation should create BatchGetBuildsCommand with correct parameters" time="0.001">
      <failure>TypeError: _buildService.default is not a constructor
    at Object.&lt;anonymous&gt; (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/tests/unit/services/build.service.test.js:32:20)
    at Promise.then.completed (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:281:40)
    at _runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:246:5)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:121:9)
    at run (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/Users/<USER>/projects/Experiments/BITS Pilani/DataScience/bits_ds_projects_portal_be/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
</testsuites>