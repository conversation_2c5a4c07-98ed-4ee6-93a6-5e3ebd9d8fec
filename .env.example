# Environment Configuration
NODE_ENV=development
VITE_APP_NAME=BITS Pilani Digital Data Science Platform
VITE_APP_VERSION=1.0.0
VITE_APP_URL=http://localhost:3000

# API Configuration (if needed in future)
# VITE_API_BASE_URL=https://api.example.com
# VITE_API_KEY=your_api_key_here

# Authentication Configuration (if needed in future)
# VITE_AUTH_DOMAIN=your-auth-domain.com
# VITE_AUTH_CLIENT_ID=your_client_id

# Analytics Configuration (if needed in future)
# VITE_ANALYTICS_ID=your_analytics_id

# Feature Flags (if needed in future)
# VITE_ENABLE_ANALYTICS=true
# VITE_ENABLE_NOTIFICATIONS=true
# VITE_ENABLE_DARK_MODE=true

# Development Configuration
VITE_DEV_TOOLS=true
VITE_MOCK_API=true

DB_HOST=postgres_db   ✅
DB_PORT=5432
DB_NAME=bits_platform
DB_USERNAME=postgres
DB_PASSWORD=password

# JupyterHub Configuration
JUPYTERHUB_URL=http://localhost:8001
JUPYTERHUB_ADMIN_TOKEN=607994eb70ddc4da5748e39da374e7875ead335b87e7e1ba15fc4fddb08febd6
JUPYTER_USER_SERVER_PREFIX=/user
JUPYTER_PROJECT_PREFIX=project_
JUPYTER_DEFAULT_KERNEL=python3

# Docker Configuration for JupyterHub
DOCKER_NETWORK_NAME=bits_bits-network
JUPYTER_USER_IMAGE=bits-jupyter-user:latest
