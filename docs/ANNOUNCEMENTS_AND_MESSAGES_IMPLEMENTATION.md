# Announcements and Messages System Implementation

## Overview

The BITS DataScience Platform now includes a comprehensive **Announcements** and **Messages** system designed to facilitate effective communication between instructors, students, and administrators. This system addresses the previously identified gaps in instructor role functionality.

## System Architecture

### Core Components

1. **Data Models** - Database schema for announcements and messages
2. **Service Layer** - Business logic and data operations
3. **Controller Layer** - API endpoint handlers
4. **Route Layer** - HTTP routing and middleware
5. **Database Migrations** - Schema creation and updates

### Technology Stack

- **Backend**: Node.js, Express.js, Sequelize ORM
- **Database**: PostgreSQL with JSONB support
- **Authentication**: JWT-based with RBAC permissions
- **File Storage**: AWS S3 integration for attachments
- **Validation**: Express-validator for input validation

## 1. Announcements System

### Purpose
Provide instructors with a professional announcement management system for course-wide communications, project updates, deadline reminders, and important notifications.

### Key Features

#### **Announcement Types**
- `general` - General course information
- `project_update` - Project-related updates
- `deadline_reminder` - Important deadline notifications
- `course_update` - Course structure changes
- `important` - High-priority information
- `urgent` - Critical notifications

#### **Priority Levels**
- `low` - Informational announcements
- `normal` - Standard announcements
- `high` - Important announcements
- `urgent` - Critical announcements

#### **Status Management**
- `draft` - Work in progress
- `published` - Live and visible to students
- `archived` - Historical reference

#### **Advanced Features**
- **Pinning** - Keep important announcements at the top
- **Scheduling** - Auto-publish at specific dates
- **Expiration** - Auto-archive after set dates
- **Target Audience** - Role-based visibility (all, students, instructors, TAs)
- **Attachments** - File uploads via S3 integration

### Data Model

```sql
CREATE TABLE announcements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID NOT NULL REFERENCES courses(id),
  title VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  announcement_type VARCHAR(50) NOT NULL DEFAULT 'general',
  priority VARCHAR(20) NOT NULL DEFAULT 'normal',
  status VARCHAR(20) NOT NULL DEFAULT 'draft',
  is_pinned BOOLEAN NOT NULL DEFAULT false,
  scheduled_for TIMESTAMP,
  expires_at TIMESTAMP,
  target_audience JSONB DEFAULT '["all"]',
  attachments JSONB DEFAULT '[]',
  metadata JSONB DEFAULT '{}',
  created_by UUID NOT NULL REFERENCES users(id),
  published_by UUID REFERENCES users(id),
  published_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### API Endpoints

#### **Create Announcement**
```http
POST /api/announcements
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Project Deadline Extended",
  "content": "The deadline for Project 2 has been extended to Friday...",
  "courseId": "uuid",
  "announcementType": "deadline_reminder",
  "priority": "high",
  "isPinned": true,
  "targetAudience": ["students"],
  "attachments": []
}
```

#### **Get Course Announcements**
```http
GET /api/announcements/course/:courseId?status=published&page=1&limit=20
Authorization: Bearer <token>
```

#### **Publish Announcement**
```http
POST /api/announcements/:id/publish
Authorization: Bearer <token>
```

#### **Schedule Announcement**
```http
POST /api/announcements/:id/schedule
Authorization: Bearer <token>
Content-Type: application/json

{
  "scheduledFor": "2025-09-15T09:00:00Z"
}
```

### Use Cases

1. **Course Start Announcements** - Welcome messages and course overview
2. **Project Updates** - Changes to project requirements or deadlines
3. **Deadline Reminders** - Important submission dates
4. **Course Changes** - Schedule modifications or policy updates
5. **Emergency Notifications** - Critical information requiring immediate attention

## 2. Messages System

### Purpose
Enable direct communication between users (instructor-student, instructor-instructor, student-student) with support for threaded conversations and course/project context.

### Key Features

#### **Message Types**
- `personal` - Direct user-to-user communication
- `course_related` - Course-specific discussions
- `project_related` - Project-specific communications
- `system` - Automated system notifications
- `notification` - General notifications

#### **Conversation Management**
- **Threaded Replies** - Organized conversation chains
- **Thread IDs** - Group related messages
- **Read Status** - Track message delivery and reading
- **Priority Levels** - Message importance classification

#### **Context Integration**
- **Course Linking** - Messages associated with specific courses
- **Project Linking** - Messages related to specific projects
- **User Relationships** - Sender-recipient management

### Data Model

```sql
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sender_id UUID NOT NULL REFERENCES users(id),
  recipient_id UUID NOT NULL REFERENCES users(id),
  subject VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  message_type VARCHAR(50) NOT NULL DEFAULT 'personal',
  priority VARCHAR(20) NOT NULL DEFAULT 'normal',
  status VARCHAR(20) NOT NULL DEFAULT 'sent',
  is_read BOOLEAN NOT NULL DEFAULT false,
  read_at TIMESTAMP,
  parent_message_id UUID REFERENCES messages(id),
  thread_id UUID,
  course_id UUID REFERENCES courses(id),
  project_id UUID REFERENCES projects(id),
  attachments JSONB DEFAULT '[]',
  metadata JSONB DEFAULT '{}',
  scheduled_for TIMESTAMP,
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### API Endpoints

#### **Send Message**
```http
POST /api/messages
Authorization: Bearer <token>
Content-Type: application/json

{
  "recipientId": "uuid",
  "subject": "Question about Project 1",
  "content": "I have a question regarding the data preprocessing...",
  "messageType": "project_related",
  "priority": "normal",
  "courseId": "uuid",
  "projectId": "uuid"
}
```

#### **Reply to Message**
```http
POST /api/messages/:id/reply
Authorization: Bearer <token>
Content-Type: application/json

{
  "subject": "Re: Question about Project 1",
  "content": "Great question! Here's the answer...",
  "attachments": []
}
```

#### **Get Inbox**
```http
GET /api/messages/inbox?status=all&page=1&limit=20&isRead=false
Authorization: Bearer <token>
```

#### **Get Conversation Thread**
```http
GET /api/messages/thread/:threadId
Authorization: Bearer <token>
```

#### **Course Announcement (Bulk Messaging)**
```http
POST /api/messages/course-announcement
Authorization: Bearer <token>
Content-Type: application/json

{
  "courseId": "uuid",
  "subject": "Important Course Update",
  "content": "Please note the following changes...",
  "priority": "high"
}
```

### Use Cases

1. **Student Questions** - Direct communication with instructors
2. **Project Feedback** - Detailed feedback and discussions
3. **Course Discussions** - Course-related Q&A
4. **Administrative Communication** - System-wide notifications
5. **Collaboration** - Team project communications

## 3. Implementation Details

### Database Migrations

The system requires new database tables and relationships. A migration file will be created to:

1. Create `announcements` table
2. Create `messages` table
3. Add necessary indexes for performance
4. Establish foreign key relationships

### Service Layer Architecture

#### **AnnouncementService**
- CRUD operations for announcements
- Status management (draft, publish, archive)
- Scheduling and expiration handling
- Permission-based access control
- Course-specific filtering

#### **MessageService**
- Message creation and management
- Thread management and replies
- Read status tracking
- Bulk messaging capabilities
- Context-aware filtering

### Security and Permissions

#### **Role-Based Access Control**
- **Instructors**: Full CRUD on announcements, send messages to students
- **Students**: View published announcements, send/receive messages
- **Admins**: Full system access
- **TAs**: Limited announcement management

#### **Data Validation**
- Input sanitization and validation
- File type and size restrictions
- SQL injection prevention
- XSS protection

### Performance Considerations

#### **Database Optimization**
- Strategic indexing on frequently queried fields
- JSONB for flexible metadata storage
- Efficient pagination for large datasets
- Connection pooling

#### **Caching Strategy**
- Redis caching for frequently accessed data
- Announcement cache invalidation
- Message thread caching

## 4. Integration Points

### Existing Systems

1. **User Management** - Integration with existing user roles and permissions
2. **Course System** - Linking announcements and messages to courses
3. **Project System** - Project-specific communications
4. **S3 Integration** - File attachment handling
5. **Activity Logging** - Audit trail for communications

### Frontend Integration

#### **Instructor Dashboard**
- Announcement creation and management interface
- Message center with inbox and sent items
- Course announcement tools
- Communication analytics

#### **Student Interface**
- Course announcement viewer
- Personal messaging system
- Notification center
- File attachment handling

## 5. Testing Strategy

### Unit Tests
- Service layer business logic
- Controller input validation
- Model validation and constraints
- Permission checking

### Integration Tests
- API endpoint functionality
- Database operations
- Authentication and authorization
- File upload handling

### Performance Tests
- Large dataset handling
- Concurrent user scenarios
- Database query optimization
- Memory usage monitoring

## 6. Deployment and Monitoring

### Environment Configuration
- Database connection settings
- S3 bucket configuration
- Redis cache settings
- Logging configuration

### Health Checks
- Database connectivity
- S3 service availability
- Redis cache status
- API endpoint responsiveness

### Monitoring and Alerting
- Error rate tracking
- Performance metrics
- User activity monitoring
- System resource usage

## 7. Future Enhancements

### Planned Features
1. **Email Notifications** - Automatic email alerts for important announcements
2. **Push Notifications** - Real-time mobile notifications
3. **Rich Text Editor** - Enhanced content creation
4. **Message Templates** - Predefined message formats
5. **Analytics Dashboard** - Communication effectiveness metrics
6. **Integration APIs** - Third-party system integration

### Scalability Considerations
1. **Microservices Architecture** - Separate announcement and message services
2. **Message Queuing** - Asynchronous message processing
3. **CDN Integration** - Global content delivery
4. **Multi-tenant Support** - Organization-level isolation

## 8. Troubleshooting Guide

### Common Issues

#### **Database Connection Errors**
- Check database credentials
- Verify network connectivity
- Monitor connection pool status

#### **File Upload Failures**
- Validate S3 credentials
- Check file size limits
- Verify file type restrictions

#### **Permission Errors**
- Confirm user role assignments
- Check course enrollment status
- Verify ownership permissions

### Debug Tools
- Comprehensive logging system
- Database query monitoring
- Performance profiling
- Error tracking and reporting

## Conclusion

The Announcements and Messages system provides a robust foundation for communication within the BITS DataScience Platform. With its comprehensive feature set, security measures, and scalability considerations, it addresses all identified gaps in instructor functionality while maintaining high performance and reliability standards.

The system is designed to grow with the platform's needs, supporting both current requirements and future enhancements. Regular monitoring and maintenance will ensure optimal performance and user satisfaction.
