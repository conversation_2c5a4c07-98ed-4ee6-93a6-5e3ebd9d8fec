{"info": {"_postman_id": "a1b2c3d4-e5f6-4a7b-8c9d-0e1f2a3b4c5d", "name": "Jupyter API via Node.js Proxy", "description": "A collection for testing the Jupyter Server REST API through a custom Node.js backend proxy.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Lifecycle", "item": [{"name": "Start User Server", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/jupyter/start", "host": ["{{baseUrl}}"], "path": ["api", "jup<PERSON><PERSON>", "start"]}, "description": "This is the first request you should run. It calls the dedicated endpoint on your Node.js backend to ensure the user's Jupyter server is running. This may take a minute to respond the first time."}, "response": []}]}, {"name": "Contents API (Files & Directories)", "item": [{"name": "List Root Contents", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/jupyter/api/contents", "host": ["{{baseUrl}}"], "path": ["api", "jup<PERSON><PERSON>", "api", "contents"]}, "description": "Gets a list of all files and directories in the user's root workspace."}, "response": []}, {"name": "Create Directory", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"type\": \"directory\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/jupyter/api/contents/My-New-Project", "host": ["{{baseUrl}}"], "path": ["api", "jup<PERSON><PERSON>", "api", "contents", "My-New-Project"]}, "description": "Creates a new directory. The directory name is specified in the URL."}, "response": []}, {"name": "Create Notebook", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"type\": \"notebook\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/jupyter/api/contents/My-New-Project", "host": ["{{baseUrl}}"], "path": ["api", "jup<PERSON><PERSON>", "api", "contents", "My-New-Project"]}, "description": "Creates a new, empty .ipynb notebook file inside the specified directory. It will be named 'Untitled.ipynb' by default."}, "response": []}, {"name": "Get File Content", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/jupyter/api/contents/My-New-Project/Untitled.ipynb", "host": ["{{baseUrl}}"], "path": ["api", "jup<PERSON><PERSON>", "api", "contents", "My-New-Project", "Untitled.ipynb"]}, "description": "Retrieves the full content of a file, like a notebook, in JSON format."}, "response": []}, {"name": "Delete File or Directory", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/jupyter/api/contents/My-New-Project/Untitled.ipynb", "host": ["{{baseUrl}}"], "path": ["api", "jup<PERSON><PERSON>", "api", "contents", "My-New-Project", "Untitled.ipynb"]}, "description": "Deletes a file or an entire directory."}, "response": []}]}, {"name": "Kernels API (Code Execution)", "item": [{"name": "List Running <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/jupyter/api/kernels", "host": ["{{baseUrl}}"], "path": ["api", "jup<PERSON><PERSON>", "api", "kernels"]}, "description": "Gets a list of all kernels currently running for the user."}, "response": []}, {"name": "Start a New Kernel", "event": [{"listen": "test", "script": {"exec": ["// This script automatically saves the new kernel's ID to a variable", "// so you can easily use it in other requests.", "var jsonData = pm.response.json();", "pm.collectionVariables.set(\"kernelId\", jsonData.id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/jupyter/api/kernels", "host": ["{{baseUrl}}"], "path": ["api", "jup<PERSON><PERSON>", "api", "kernels"]}, "description": "Starts a new kernel (e.g., Python 3) for code execution. The response will contain an ID for this kernel."}, "response": []}, {"name": "Get Kernel Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/jupyter/api/kernels/{{kernelId}}", "host": ["{{baseUrl}}"], "path": ["api", "jup<PERSON><PERSON>", "api", "kernels", "{{kernelId}}"]}, "description": "Gets detailed information about a specific running kernel. Uses the `kernelId` variable set by the 'Start a New Kernel' request."}, "response": []}, {"name": "Shutdown a Kernel", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/jupyter/api/kernels/{{kernelId}}", "host": ["{{baseUrl}}"], "path": ["api", "jup<PERSON><PERSON>", "api", "kernels", "{{kernelId}}"]}, "description": "Shuts down a specific running kernel to free up resources."}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "kernelId", "value": "", "description": "This variable is set automatically by the 'Start a New Kernel' request."}]}