# Authentication Integration Guide

## Overview
This document provides complete integration specifications for the BITS DataScience Platform authentication system, including Google OAuth, JWT token management, and session handling.

## Base Configuration
- **Base URL**: `http://localhost:5001` (Development) / `https://api.bits-datascience.edu` (Production)
- **Content-Type**: `application/json`
- **Authentication**: JWT <PERSON> (for protected endpoints)

---

## 1. Google OAuth Authentication

### Endpoint
```
POST /api/auth/google
```

### Request Format
```json
{
  "googleToken": "string",
  "lmsData": {
    "userId": "string",
    "email": "string",
    "name": "string",
    "roles": ["string"]
  }
}
```

### Request Headers
```http
Content-Type: application/json
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "user": {
    "id": "uuid",
    "name": "string",
    "email": "string",
    "role": "student|instructor|admin",
    "status": "active|inactive|pending",
    "profilePicture": "string",
    "lastLogin": "2025-08-17T18:00:00.000Z"
  },
  "tokens": {
    "accessToken": "string",
    "refreshToken": "string",
    "expiresIn": 3600
  },
  "message": "Authentication successful"
}
```

### Response Format (Error - 400/401)
```json
{
  "success": false,
  "error": "Invalid Google token",
  "status": 401,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/auth/google",
  "method": "POST",
  "details": "The provided Google token is invalid or expired"
}
```

### Frontend Integration Example
```javascript
// Figma Make Integration
const authenticateUser = async (googleToken, lmsData) => {
  const response = await fetch('http://localhost:5001/api/auth/google', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      googleToken,
      lmsData
    })
  });

  const data = await response.json();
  
  if (data.success) {
    // Store tokens
    localStorage.setItem('accessToken', data.tokens.accessToken);
    localStorage.setItem('refreshToken', data.tokens.refreshToken);
    localStorage.setItem('user', JSON.stringify(data.user));
    
    return data;
  } else {
    throw new Error(data.error);
  }
};
```

---

## 2. Token Refresh

### Endpoint
```
POST /api/auth/refresh
```

### Request Format
```json
{
  "refreshToken": "string"
}
```

### Request Headers
```http
Content-Type: application/json
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "tokens": {
    "accessToken": "string",
    "refreshToken": "string",
    "expiresIn": 3600
  },
  "message": "Token refreshed successfully"
}
```

### Response Format (Error - 401)
```json
{
  "success": false,
  "error": "Invalid refresh token",
  "status": 401,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/auth/refresh",
  "method": "POST",
  "details": "The refresh token is invalid or expired"
}
```

### Frontend Integration Example
```javascript
const refreshToken = async () => {
  const refreshToken = localStorage.getItem('refreshToken');
  
  const response = await fetch('http://localhost:5001/api/auth/refresh', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ refreshToken })
  });

  const data = await response.json();
  
  if (data.success) {
    localStorage.setItem('accessToken', data.tokens.accessToken);
    localStorage.setItem('refreshToken', data.tokens.refreshToken);
    return data.tokens.accessToken;
  } else {
    // Redirect to login
    localStorage.clear();
    window.location.href = '/login';
  }
};
```

---

## 3. Logout

### Endpoint
```
POST /api/auth/logout
```

### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### Response Format (Error - 401)
```json
{
  "success": false,
  "error": "Unauthorized",
  "status": 401,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/auth/logout",
  "method": "POST",
  "details": "Invalid or missing access token"
}
```

### Frontend Integration Example
```javascript
const logout = async () => {
  const accessToken = localStorage.getItem('accessToken');
  
  try {
    await fetch('http://localhost:5001/api/auth/logout', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Logout error:', error);
  } finally {
    // Clear local storage
    localStorage.clear();
    window.location.href = '/login';
  }
};
```

---

## 4. Get Current User Profile

### Endpoint
```
GET /api/users/profile
```

### Request Headers
```http
Authorization: Bearer <access_token>
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "user": {
    "id": "uuid",
    "name": "string",
    "email": "string",
    "role": "student|instructor|admin",
    "status": "active|inactive|pending",
    "profilePicture": "string",
    "lastLogin": "2025-08-17T18:00:00.000Z",
    "createdAt": "2025-08-17T18:00:00.000Z",
    "updatedAt": "2025-08-17T18:00:00.000Z"
  }
}
```

### Response Format (Error - 401)
```json
{
  "success": false,
  "error": "Unauthorized",
  "status": 401,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/users/profile",
  "method": "GET",
  "details": "Invalid or missing access token"
}
```

---

## 5. Update User Profile

### Endpoint
```
PUT /api/users/profile
```

### Request Format
```json
{
  "name": "string",
  "profilePicture": "string"
}
```

### Request Headers
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

### Response Format (Success - 200)
```json
{
  "success": true,
  "user": {
    "id": "uuid",
    "name": "string",
    "email": "string",
    "role": "student|instructor|admin",
    "status": "active|inactive|pending",
    "profilePicture": "string",
    "lastLogin": "2025-08-17T18:00:00.000Z",
    "createdAt": "2025-08-17T18:00:00.000Z",
    "updatedAt": "2025-08-17T18:00:00.000Z"
  },
  "message": "Profile updated successfully"
}
```

---

## 6. Health Check

### Endpoint
```
GET /health
```

### Response Format (Success - 200)
```json
{
  "status": "healthy",
  "timestamp": "2025-08-17T18:00:00.000Z",
  "version": "2.1.0",
  "environment": "development",
  "services": {
    "database": "connected",
    "redis": "connected",
    "s3": "connected"
  }
}
```

---

## Error Handling

### Standard Error Response Format
```json
{
  "success": false,
  "error": "Error message",
  "status": 400,
  "timestamp": "2025-08-17T18:00:00.000Z",
  "path": "/api/endpoint",
  "method": "GET|POST|PUT|DELETE",
  "details": "Detailed error description"
}
```

### Common HTTP Status Codes
- **200**: Success
- **201**: Created
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **422**: Validation Error
- **500**: Internal Server Error

---

## Frontend Integration Checklist

### Required Environment Variables
```javascript
const config = {
  API_BASE_URL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:5001',
  GOOGLE_CLIENT_ID: process.env.REACT_APP_GOOGLE_CLIENT_ID,
  ENVIRONMENT: process.env.NODE_ENV || 'development'
};
```

### Token Management
```javascript
// Token interceptor for automatic refresh
const setupTokenInterceptor = () => {
  // Add request interceptor
  axios.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Add response interceptor
  axios.interceptors.response.use(
    (response) => response,
    async (error) => {
      if (error.response?.status === 401) {
        try {
          const newToken = await refreshToken();
          error.config.headers.Authorization = `Bearer ${newToken}`;
          return axios.request(error.config);
        } catch (refreshError) {
          localStorage.clear();
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      }
      return Promise.reject(error);
    }
  );
};
```

### Authentication State Management
```javascript
// React Context for authentication
const AuthContext = createContext();

const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  const login = async (googleToken, lmsData) => {
    try {
      const response = await authenticateUser(googleToken, lmsData);
      setUser(response.user);
      return response;
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await logoutUser();
    } finally {
      setUser(null);
    }
  };

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('accessToken');
      if (token) {
        const response = await fetch(`${config.API_BASE_URL}/api/users/profile`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        if (response.ok) {
          const data = await response.json();
          setUser(data.user);
        } else {
          localStorage.clear();
        }
      }
    } catch (error) {
      localStorage.clear();
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkAuth();
  }, []);

  return (
    <AuthContext.Provider value={{ user, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};
```

---

## Testing Endpoints

### Test Authentication Flow
```bash
# 1. Health check
curl -X GET http://localhost:5001/health

# 2. Google OAuth (with mock token)
curl -X POST http://localhost:5001/api/auth/google \
  -H "Content-Type: application/json" \
  -d '{
    "googleToken": "mock-google-token",
    "lmsData": {
      "userId": "test-user-123",
      "email": "<EMAIL>",
      "name": "Test User",
      "roles": ["student"]
    }
  }'

# 3. Get profile (with token from step 2)
curl -X GET http://localhost:5001/api/users/profile \
  -H "Authorization: Bearer <access_token>"

# 4. Refresh token
curl -X POST http://localhost:5001/api/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{"refreshToken": "<refresh_token>"}'

# 5. Logout
curl -X POST http://localhost:5001/api/auth/logout \
  -H "Authorization: Bearer <access_token>"
```

---

## Security Considerations

1. **Token Storage**: Store tokens in secure HTTP-only cookies or encrypted localStorage
2. **Token Expiration**: Implement automatic token refresh before expiration
3. **CSRF Protection**: Use CSRF tokens for state-changing operations
4. **HTTPS**: Always use HTTPS in production
5. **Input Validation**: Validate all user inputs on both client and server
6. **Rate Limiting**: Implement rate limiting for authentication endpoints

---

**Last Updated**: August 17, 2025  
**Version**: 2.1.0  
**Maintainer**: BITS Pilani DataScience Team
