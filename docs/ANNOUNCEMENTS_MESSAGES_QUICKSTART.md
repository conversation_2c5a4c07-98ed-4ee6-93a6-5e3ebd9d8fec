# Announcements and Messages Quick Start Guide

## Overview

This guide provides quick examples for developers to integrate with the new **Announcements** and **Messages** APIs in the BITS DataScience Platform.

## Prerequisites

- Valid JWT authentication token
- Appropriate permissions for the endpoints you want to use
- Course ID (for course-related operations)

## 1. Announcements System

### Create Your First Announcement

```bash
curl -X POST "http://localhost:5001/api/announcements" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Welcome to Data Science 101",
    "content": "Welcome everyone! This course will cover fundamental concepts...",
    "courseId": "YOUR_COURSE_UUID",
    "announcementType": "general",
    "priority": "normal",
    "isPinned": true,
    "targetAudience": ["students"]
  }'
```

### Get Course Announcements

```bash
curl -X GET "http://localhost:5001/api/announcements/course/YOUR_COURSE_UUID?status=published&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Publish a Draft Announcement

```bash
curl -X POST "http://localhost:5001/api/announcements/ANNOUNCEMENT_UUID/publish" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Schedule an Announcement

```bash
curl -X POST "http://localhost:5001/api/announcements/ANNOUNCEMENT_UUID/schedule" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "scheduledFor": "2025-09-15T09:00:00Z"
  }'
```

## 2. Messages System

### Send a Message

```bash
curl -X POST "http://localhost:5001/api/messages" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "recipientId": "RECIPIENT_UUID",
    "subject": "Question about Project 1",
    "content": "I have a question regarding the data preprocessing step...",
    "messageType": "project_related",
    "priority": "normal",
    "courseId": "YOUR_COURSE_UUID",
    "projectId": "YOUR_PROJECT_UUID"
  }'
```

### Reply to a Message

```bash
curl -X POST "http://localhost:5001/api/messages/MESSAGE_UUID/reply" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "subject": "Re: Question about Project 1",
    "content": "Great question! Here is the answer...",
    "attachments": []
  }'
```

### Get Your Inbox

```bash
curl -X GET "http://localhost:5001/api/messages/inbox?isRead=false&page=1&limit=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Get Sent Messages

```bash
curl -X GET "http://localhost:5001/api/messages/sent?page=1&limit=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Mark Message as Read

```bash
curl -X POST "http://localhost:5001/api/messages/MESSAGE_UUID/read" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Get Conversation Thread

```bash
curl -X GET "http://localhost:5001/api/messages/thread/THREAD_UUID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Send Course Announcement (Bulk Messaging)

```bash
curl -X POST "http://localhost:5001/api/messages/course-announcement" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "courseId": "YOUR_COURSE_UUID",
    "subject": "Important Course Update",
    "content": "Please note the following changes to the course schedule...",
    "priority": "high"
  }'
```

## 3. JavaScript/Node.js Examples

### Using Fetch API

```javascript
// Create announcement
const createAnnouncement = async (announcementData) => {
  const response = await fetch('http://localhost:5001/api/announcements', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(announcementData)
  });
  
  return await response.json();
};

// Get course announcements
const getCourseAnnouncements = async (courseId) => {
  const response = await fetch(
    `http://localhost:5001/api/announcements/course/${courseId}?status=published`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );
  
  return await response.json();
};

// Send message
const sendMessage = async (messageData) => {
  const response = await fetch('http://localhost:5001/api/messages', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(messageData)
  });
  
  return await response.json();
};

// Get inbox
const getInbox = async () => {
  const response = await fetch('http://localhost:5001/api/messages/inbox?isRead=false', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return await response.json();
};
```

### Using Axios

```javascript
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:5001/api',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// Announcements
const announcementsAPI = {
  create: (data) => api.post('/announcements', data),
  getCourse: (courseId, params) => api.get(`/announcements/course/${courseId}`, { params }),
  getById: (id) => api.get(`/announcements/${id}`),
  update: (id, data) => api.put(`/announcements/${id}`, data),
  publish: (id) => api.post(`/announcements/${id}/publish`),
  archive: (id) => api.post(`/announcements/${id}/archive`),
  togglePin: (id) => api.post(`/announcements/${id}/toggle-pin`),
  delete: (id) => api.delete(`/announcements/${id}`),
  getInstructor: (params) => api.get('/announcements/instructor', { params }),
  getStudent: (params) => api.get('/announcements/student', { params }),
  schedule: (id, scheduledFor) => api.post(`/announcements/${id}/schedule`, { scheduledFor })
};

// Messages
const messagesAPI = {
  send: (data) => api.post('/messages', data),
  reply: (id, data) => api.post(`/messages/${id}/reply`, data),
  getInbox: (params) => api.get('/messages/inbox', { params }),
  getSent: (params) => api.get('/messages/sent', { params }),
  getThread: (threadId) => api.get(`/messages/thread/${threadId}`),
  markAsRead: (id) => api.post(`/messages/${id}/read`),
  markMultipleAsRead: (messageIds) => api.post('/messages/mark-read', { messageIds }),
  getById: (id) => api.get(`/messages/${id}`),
  update: (id, data) => api.put(`/messages/${id}`, data),
  delete: (id) => api.delete(`/messages/${id}`),
  getUnreadCount: () => api.get('/messages/unread-count'),
  getInstructorStats: () => api.get('/messages/instructor/stats'),
  sendCourseAnnouncement: (data) => api.post('/messages/course-announcement', data)
};
```

## 4. Frontend Integration Examples

### React Component for Announcements

```jsx
import React, { useState, useEffect } from 'react';

const AnnouncementsList = ({ courseId }) => {
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAnnouncements();
  }, [courseId]);

  const fetchAnnouncements = async () => {
    try {
      const response = await fetch(
        `/api/announcements/course/${courseId}?status=published`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );
      const data = await response.json();
      setAnnouncements(data.data.announcements);
    } catch (error) {
      console.error('Error fetching announcements:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading announcements...</div>;

  return (
    <div className="announcements-list">
      {announcements.map(announcement => (
        <div key={announcement.id} className="announcement-card">
          <div className="announcement-header">
            <h3>{announcement.title}</h3>
            {announcement.is_pinned && <span className="pinned-badge">📌 Pinned</span>}
            <span className={`priority-badge priority-${announcement.priority}`}>
              {announcement.priority}
            </span>
          </div>
          <p>{announcement.content}</p>
          <div className="announcement-meta">
            <span>By: {announcement.creator.name}</span>
            <span>Type: {announcement.announcement_type}</span>
            <span>Created: {new Date(announcement.created_at).toLocaleDateString()}</span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default AnnouncementsList;
```

### React Component for Messages

```jsx
import React, { useState, useEffect } from 'react';

const MessagesInbox = () => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchInbox();
  }, []);

  const fetchInbox = async () => {
    try {
      const response = await fetch('/api/messages/inbox?isRead=false', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setMessages(data.data.messages);
    } catch (error) {
      console.error('Error fetching inbox:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (messageId) => {
    try {
      await fetch(`/api/messages/${messageId}/read`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      // Refresh inbox
      fetchInbox();
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  if (loading) return <div>Loading messages...</div>;

  return (
    <div className="messages-inbox">
      {messages.map(message => (
        <div 
          key={message.id} 
          className={`message-item ${!message.is_read ? 'unread' : ''}`}
          onClick={() => markAsRead(message.id)}
        >
          <div className="message-header">
            <h4>{message.subject}</h4>
            <span className={`priority-badge priority-${message.priority}`}>
              {message.priority}
            </span>
          </div>
          <p className="message-preview">{message.content.substring(0, 100)}...</p>
          <div className="message-meta">
            <span>From: {message.sender.name}</span>
            <span>Type: {message.message_type}</span>
            <span>Date: {new Date(message.created_at).toLocaleDateString()}</span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default MessagesInbox;
```

## 5. Error Handling

### Common Error Responses

```javascript
// 400 Bad Request
{
  "error": "Validation Error",
  "message": "Title must be between 2 and 200 characters"
}

// 401 Unauthorized
{
  "error": "Unauthorized",
  "message": "Invalid or expired token"
}

// 403 Forbidden
{
  "error": "Access Denied",
  "message": "Permission denied: Only creator can update announcement"
}

// 404 Not Found
{
  "error": "Not Found",
  "message": "Announcement not found"
}

// 500 Internal Server Error
{
  "error": "Internal Server Error",
  "message": "Failed to create announcement"
}
```

### Error Handling Example

```javascript
const handleAPIError = (error) => {
  if (error.response) {
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        console.error('Validation Error:', data.message);
        // Show validation error to user
        break;
      case 401:
        console.error('Authentication Error:', data.message);
        // Redirect to login
        break;
      case 403:
        console.error('Permission Error:', data.message);
        // Show permission denied message
        break;
      case 404:
        console.error('Not Found:', data.message);
        // Show not found message
        break;
      default:
        console.error('API Error:', data.message);
        // Show generic error message
    }
  } else {
    console.error('Network Error:', error.message);
    // Show network error message
  }
};

// Usage
try {
  const result = await announcementsAPI.create(announcementData);
  console.log('Announcement created:', result);
} catch (error) {
  handleAPIError(error);
}
```

## 6. Testing Your Integration

### Test Endpoints

1. **Health Check**: Verify the server is running
2. **Authentication**: Test with valid/invalid tokens
3. **Permissions**: Test with different user roles
4. **Validation**: Test with invalid data
5. **CRUD Operations**: Test all create, read, update, delete operations

### Sample Test Data

```javascript
const sampleAnnouncement = {
  title: "Test Announcement",
  content: "This is a test announcement for development purposes.",
  courseId: "test-course-uuid",
  announcementType: "general",
  priority: "normal",
  isPinned: false,
  targetAudience: ["students"]
};

const sampleMessage = {
  recipientId: "test-recipient-uuid",
  subject: "Test Message",
  content: "This is a test message for development purposes.",
  messageType: "personal",
  priority: "normal"
};
```

## 7. Next Steps

1. **Review the full API documentation** in `docs/API_DOCUMENTATION.md`
2. **Check the implementation details** in `docs/ANNOUNCEMENTS_AND_MESSAGES_IMPLEMENTATION.md`
3. **Run the database migration** to create the required tables
4. **Test the endpoints** with your frontend application
5. **Implement error handling** and user feedback
6. **Add real-time updates** using WebSockets (future enhancement)

## Support

For questions or issues:
- Check the API documentation
- Review the implementation guide
- Check server logs for detailed error information
- Ensure proper permissions are set up in your database
