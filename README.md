# BITS-DataScience Projects Platform Backend

A comprehensive backend API for the BITS-DataScience Projects Platform, featuring Learning Tools Interoperability (LTI) 1.3 integration with D2L-Brightspace, AWS S3 file storage, **Sandbox Environment Management**, and robust role-based access control.

## 🚀 Features

- **LTI 1.3 Integration**: Seamless integration with D2L-Brightspace LMS
- **Role-Based Access Control**: Support for Students, Instructors, and Administrators
- **AWS S3 Integration**: Secure file storage and management
- **🆕 Sandbox Environment Management**: Isolated development environments for data science projects
- **JupyterHub Integration**: Automated Jupyter notebook server provisioning
- **JWT Authentication**: Secure token-based authentication with Google OAuth
- **Database Management**: PostgreSQL with Sequelize ORM
- **Real-time Features**: WebSocket support for live updates
- **Comprehensive API**: RESTful API with Swagger documentation
- **Docker Support**: Containerized deployment with Docker Compose
- **🧪 Enhanced Testing Suite**: Comprehensive unit and integration tests with Jest
- **📢 Announcements System**: Course-wide communications with scheduling and targeting
- **💬 Messages System**: Direct user communication with threaded conversations
- **Monitoring**: Health checks and logging with Winston
- **Security**: Input validation, rate limiting, and security headers

## 📋 Prerequisites

- Node.js 18.0.0 or higher
- PostgreSQL 12 or higher
- Redis 6 or higher
- Docker and Docker Compose (recommended)
- AWS Account (for S3 storage)
- **JupyterHub Server** (for sandbox environments)
- **Kubernetes Cluster** (optional, for advanced sandbox orchestration)

## 🛠️ Installation

### Quick Start with Docker

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Run setup script**
   ```bash
   chmod +x scripts/setup-dev.sh
   ./scripts/setup-dev.sh
   ```

3. **Start with Docker Compose**
   ```bash
   docker-compose -f docker-compose.dev.yml up
   ```

### Manual Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start PostgreSQL and Redis**
   ```bash
   # Using Docker
   docker run -d --name postgres -p 5432:5432 -e POSTGRES_PASSWORD=password postgres:15
   docker run -d --name redis -p 6379:6379 redis:7-alpine
   ```

4. **Run database migrations**
   ```bash
   npm run db:migrate
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

## 🔧 Configuration

### Environment Variables

Key environment variables to configure:

```bash
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/bits_platform

# Redis
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-jwt-secret
SESSION_SECRET=your-session-secret

# AWS S3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
S3_BUCKET_NAME=bits-datascience-platform

# LTI 1.3
LTI_TOOL_URL=https://your-domain.com
LTI_PRIVATE_KEY=your-private-key
LTI_PUBLIC_KEY=your-public-key

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# 🆕 Sandbox Configuration
JUPYTERHUB_URL=http://localhost:8000
JUPYTERHUB_TOKEN=your-jupyterhub-admin-token
SANDBOX_API_BASE_URL=https://sandbox-api.example.com
SANDBOX_API_TOKEN=your-sandbox-api-token
```

### Database Configuration

The application uses PostgreSQL with Sequelize ORM. Database configuration is handled through environment variables and the `.sequelizerc` file.

### LTI 1.3 Setup

1. **Register Platform**: Add D2L-Brightspace as an LTI platform
2. **Configure Tool**: Use the `/api/lti/config` endpoint for tool registration
3. **Set up Keys**: Generate RSA key pairs for JWT signing

### 🆕 Sandbox Environment Setup

1. **JupyterHub Configuration**: Set up JupyterHub server with admin access
2. **Sandbox API**: Configure external sandbox orchestration service
3. **Resource Limits**: Define CPU, memory, and storage limits for sandboxes
4. **Network Policies**: Configure network access for sandbox environments

## 📚 API Documentation

### Swagger Documentation

Access the interactive API documentation at:
- **Development**: http://localhost:5001/api-docs
- **Production**: https://your-domain.com/api-docs

### API Endpoints

#### Authentication
- `POST /api/auth/google` - Google OAuth authentication
- `POST /api/auth/refresh` - Refresh JWT token
- `POST /api/auth/logout` - Logout user

#### User Management
- `GET /api/users` - Get users (paginated)
- `GET /api/users/profile` - Get current user profile
- `PUT /api/users/profile` - Update user profile
- `PUT /api/users/:id/status` - Update user status (Admin)

#### Course Management
- `GET /api/courses` - Get courses
- `POST /api/courses` - Create course (Instructor/Admin)
- `GET /api/courses/:id` - Get course details
- `POST /api/courses/:id/enroll` - Enroll user in course

#### Project Management
- `GET /api/projects` - Get projects
- `POST /api/projects` - Create project (Instructor/Admin)
- `GET /api/projects/:id` - Get project details
- `PUT /api/projects/:id` - Update project

#### Submission Management
- `GET /api/submissions` - Get submissions
- `POST /api/submissions` - Create submission
- `POST /api/submissions/:id/submit` - Submit for grading

#### Grade Management
- `GET /api/grades` - Get grades
- `POST /api/grades` - Create grade (Instructor/Admin)
- `POST /api/grades/bulk` - Bulk grade submissions

#### 🆕 Sandbox Management
- `POST /api/sandbox/create` - Create sandbox environment
- `DELETE /api/sandbox/:id` - Delete sandbox environment
- `GET /api/sandbox/:id/status` - Get sandbox status
- `PUT /api/sandbox/:id/resources` - Update sandbox resources
- `GET /api/sandbox/list` - List user sandboxes
- `GET /api/sandbox/:id/logs` - Get sandbox logs
- `POST /api/sandbox/:id/restart` - Restart sandbox
- `POST /api/sandbox/:id/scale` - Scale sandbox resources

#### LTI Integration
- `GET /api/lti/config` - Get LTI configuration
- `POST /api/lti/login` - LTI login initiation
- `POST /api/lti/launch` - LTI launch handler
- `GET /api/lti/jwks` - Get public key set

#### File Storage
- `POST /api/s3/upload` - Get presigned upload URL
- `GET /api/s3/download/:key` - Get presigned download URL
- `GET /api/s3/list` - List user files

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test types
npm run test:unit
npm run test:integration

# 🆕 Run specific test suites
npm run test -- --testPathPattern="tests/unit/models"
npm run test -- --testPathPattern="tests/unit/services"
npm run test -- --testPathPattern="tests/unit/controllers"
```

### 🆕 Enhanced Test Structure

```
tests/
├── unit/                    # Unit tests
│   ├── models/             # Model tests (✅ 195 tests passing)
│   ├── services/           # Service tests
│   │   ├── s3Service.test.js
│   │   ├── sandboxOrchestrator.test.js
│   │   └── jupyterhubAdmin.test.js
│   ├── controllers/        # Controller tests
│   ├── middlewares/        # Middleware tests
│   └── utils/              # Utility tests
├── integration/            # Integration tests
├── fixtures/               # Test data
└── setup.js               # Test setup
```

### 🆕 Test Coverage Status

- **Model Tests**: ✅ 195/195 passing (100% success rate)
- **Service Tests**: 🔄 34/35 passing (97% success rate)
- **Overall Coverage**: 87% test success rate
- **Babel Configuration**: ✅ Fixed missing dependencies

### Testing with Postman

Import the Postman collection from `/docs/BITS_DataScience_Platform.postman_collection.json` for comprehensive API testing.

## 🚀 Deployment

### Production Deployment

1. **Build Docker image**
   ```bash
   docker build -t bits-datascience-backend .
   ```

2. **Deploy with Docker Compose**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. **Use deployment script**
   ```bash
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh production
   ```

### Staging Deployment

```bash
./scripts/deploy.sh staging
```

### PM2 Deployment

```bash
npm run pm2:start
```

## 🔒 Security

### Security Features

- **Input Validation**: All inputs validated with express-validator
- **Rate Limiting**: API rate limiting with express-rate-limit
- **Security Headers**: Helmet.js for security headers
- **SQL Injection Protection**: Sequelize ORM with parameterized queries
- **XSS Protection**: Input sanitization and content security policy
- **CORS Configuration**: Configurable CORS settings
- **JWT Security**: Secure token handling with rotation
- **🆕 Sandbox Isolation**: Secure containerized environments

### Security Best Practices

1. **Keep Dependencies Updated**: Regular security audits
2. **Environment Variables**: Never commit secrets to version control
3. **HTTPS**: Always use HTTPS in production
4. **Database Security**: Use strong passwords and connection encryption
5. **Regular Backups**: Automated database backups to S3
6. **🆕 Sandbox Security**: Network isolation and resource limits

## 📊 Monitoring

### Health Checks

- **Application Health**: `/health` endpoint
- **Database Health**: Connection status monitoring
- **Redis Health**: Cache connectivity checks
- **🆕 Sandbox Health**: Sandbox service status monitoring
- **Custom Health Checks**: `npm run health`

### Logging

- **Winston Logger**: Structured logging with levels
- **Request Logging**: Morgan middleware for HTTP logging
- **Error Logging**: Comprehensive error tracking
- **Log Rotation**: Daily log rotation with cleanup
- **🆕 Sandbox Logging**: Detailed sandbox operation logs

### Monitoring Tools

- **PM2 Monitoring**: Process monitoring and restart
- **Docker Health Checks**: Container health monitoring
- **Database Monitoring**: Query performance tracking
- **🆕 Sandbox Monitoring**: Resource usage and performance metrics

## 🛠️ Development

### Development Workflow

1. **Start development environment**
   ```bash
   npm run dev
   ```

2. **Run linting**
   ```bash
   npm run lint
   npm run lint:fix
   ```

3. **Format code**
   ```bash
   npm run format
   ```

4. **Run tests**
   ```bash
   npm run test
   ```

### Database Operations

```bash
# Create migration
npx sequelize-cli migration:generate --name add-new-feature

# Run migrations
npm run db:migrate

# Rollback migration
npm run db:migrate:undo

# Seed database
npm run db:seed

# Reset database
npm run db:reset
```

### Git Hooks

Pre-commit hooks are configured with Husky:
- Linting with ESLint
- Code formatting with Prettier
- Test execution

## 📖 Documentation

### Available Documentation

- **API Documentation**: `/docs/API_DOCUMENTATION.md`
- **LTI & S3 Integration**: `/docs/LTI_S3_INTEGRATION.md`
- **🆕 Sandbox Documentation**: `/docs/SANDBOX_FEATURE.md`
- **Testing Guide**: `/docs/TESTING_GUIDE.md`
- **Postman Collection**: `/docs/BITS_DataScience_Platform.postman_collection.json`

### Generating Documentation

```bash
# Generate API documentation
npm run docs:generate

# Serve documentation locally
npm run docs:serve
```

## 🤝 Contributing

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

### Code Style

- **ESLint**: JavaScript linting
- **Prettier**: Code formatting
- **JSDoc**: Function documentation
- **Conventional Commits**: Commit message format

## 📄 License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Getting Help

- **Issues**: Report bugs and request features on GitHub
- **Email**: <EMAIL>
- **Documentation**: Check the `/docs` directory for detailed guides

### Troubleshooting

#### Common Issues

1. **Database Connection Errors**
   - Check PostgreSQL is running
   - Verify DATABASE_URL in .env
   - Ensure database exists

2. **Redis Connection Errors**
   - Check Redis is running
   - Verify REDIS_URL in .env

3. **LTI Integration Issues**
   - Verify LTI keys are properly configured
   - Check platform registration
   - Validate JWT token handling

4. **File Upload Issues**
   - Check AWS S3 credentials
   - Verify bucket permissions
   - Check CORS configuration

5. **🆕 Sandbox Issues**
   - Verify JupyterHub server is accessible
   - Check sandbox API configuration
   - Validate resource limits and quotas
   - Monitor sandbox health status

### Performance Optimization

- **Database Indexing**: Proper indexes for queries
- **Connection Pooling**: Sequelize connection pooling
- **Caching**: Redis caching for frequent queries
- **CDN**: CloudFront for static assets
- **🆕 Sandbox Optimization**: Resource pooling and auto-scaling

## 🗺️ Roadmap

### Upcoming Features

- [ ] Real-time collaboration
- [ ] Advanced analytics dashboard
- [ ] Mobile app API support
- [ ] Integration with additional LMS platforms
- [ ] Advanced plagiarism detection
- [ ] Machine learning model integration
- [ ] 🆕 Advanced sandbox features (GPU support, custom environments)
- [ ] 🆕 Sandbox collaboration and sharing

### Version History

- **v1.0.0**: Initial release with LTI 1.3 support
- **v1.1.0**: Enhanced security and performance
- **v1.2.0**: Advanced grading features
- **v2.0.0**: Major architectural improvements
- **🆕 v2.1.0**: Sandbox environment management and enhanced testing

---

**Made with ❤️ by BITS Pilani**

For more information, visit our [GitHub repository](https://github.com/bits-pilani/datascience-platform-backend).