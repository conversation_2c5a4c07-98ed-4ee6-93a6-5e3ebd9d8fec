import { 
  setupTestDatabase, 
  teardownTestDatabase, 
  cleanTestDatabase,
  importTestModels,
  createTestData 
} from '../config/test-database.js';
import { createTestServer, closeTestServer } from '../config/test-server.js';
import logger from '../../src/config/logger.config.js';

// Global variables for integration tests
global.integrationTestData = null;
global.testModels = null;
global.databaseAvailable = false;
global.testApp = null;

// Setup before all integration tests
beforeAll(async () => {
  try {
    logger.info('🚀 Setting up integration test environment...');
    
    // Try to setup test database
    try {
      await setupTestDatabase();
      global.databaseAvailable = true;
      
      // Import models
      global.testModels = await importTestModels();
      
      // Create test data
      global.integrationTestData = await createTestData(global.testModels);
      
      logger.info('✅ Integration test environment setup complete with database');
    } catch (dbError) {
      logger.warn('⚠️ Database not available, running in mock mode:', dbError.message);
      global.databaseAvailable = false;
      
      // Create mock models and data for testing
      global.testModels = {
        User: {
          create: jest.fn(),
          findOne: jest.fn(),
          update: jest.fn(),
          destroy: jest.fn()
        },
        Role: {
          create: jest.fn(),
          findOne: jest.fn()
        },
        Permission: {
          bulkCreate: jest.fn()
        },
        Course: {
          create: jest.fn(),
          findOne: jest.fn()
        },
        Project: {
          create: jest.fn(),
          findOne: jest.fn()
        }
      };
      
      global.integrationTestData = {
        users: {
          adminUser: { id: 'admin-123', email: '<EMAIL>', name: 'Admin User' },
          instructorUser: { id: 'instructor-123', email: '<EMAIL>', name: 'Instructor User' },
          studentUser: { id: 'student-123', email: '<EMAIL>', name: 'Student User' }
        },
        roles: {
          adminRole: { id: 'role-1', name: 'admin', description: 'Administrator role' },
          instructorRole: { id: 'role-2', name: 'instructor', description: 'Instructor role' },
          studentRole: { id: 'role-3', name: 'student', description: 'Student role' }
        },
        permissions: [],
        course: { id: 'course-123', title: 'Test Course', description: 'A test course' },
        project: { id: 'project-123', title: 'Test Project', description: 'A test project' }
      };
      
      logger.info('✅ Integration test environment setup complete with mocks');
    }

    // Create test server
    global.testApp = await createTestServer();
    logger.info('✅ Test server created');
    
  } catch (error) {
    logger.error('❌ Integration test setup failed:', error);
    throw error;
  }
}, 30000); // 30 second timeout

// Cleanup after each test
afterEach(async () => {
  try {
    if (global.databaseAvailable) {
      await cleanTestDatabase();
    } else {
      // Clear mocks
      jest.clearAllMocks();
    }
  } catch (error) {
    logger.error('❌ Test cleanup failed:', error);
    throw error;
  }
}, 10000); // 10 second timeout

// Teardown after all integration tests
afterAll(async () => {
  try {
    logger.info('🧹 Cleaning up integration test environment...');
    
    // Close test server
    await closeTestServer();
    
    // Clean test data
    if (global.integrationTestData) {
      global.integrationTestData = null;
    }
    
    // Teardown test database if available
    if (global.databaseAvailable) {
      await teardownTestDatabase();
    }
    
    logger.info('✅ Integration test environment cleanup complete');
  } catch (error) {
    logger.error('❌ Integration test teardown failed:', error);
    throw error;
  }
}, 30000); // 30 second timeout

// Export utilities for use in individual test files
export const getTestModels = () => global.testModels;
export const getTestData = () => global.integrationTestData;
export const isDatabaseAvailable = () => global.databaseAvailable;
export const getTestApp = () => global.testApp;
