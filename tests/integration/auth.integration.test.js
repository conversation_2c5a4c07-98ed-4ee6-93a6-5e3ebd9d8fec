import request from 'supertest';
import { getTestModels, getTestData, getTestApp } from './setup.js';
import { generateToken } from '../../src/middlewares/auth.js';
import logger from '../../src/config/logger.config.js';

describe('Authentication Integration Tests', () => {
  let testModels;
  let testData;
  let testApp;
  let adminToken;
  let instructorToken;
  let studentToken;

  beforeAll(async () => {
    try {
      // Get test models, data, and app
      testModels = getTestModels();
      testData = getTestData();
      testApp = getTestApp();
      
      // Generate test tokens
      adminToken = generateToken(testData.users.adminUser);
      instructorToken = generateToken(testData.users.instructorUser);
      studentToken = generateToken(testData.users.studentUser);
      
      logger.info('✅ Authentication integration test setup complete');
    } catch (error) {
      logger.error('❌ Authentication integration test setup failed:', error);
      throw error;
    }
  });

  describe('Health Check', () => {
    test('should return health status', async () => {
      const response = await request(testApp)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe('OK');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('environment');
    });
  });

  describe('API Structure Tests', () => {
    test('should have authentication routes available', async () => {
      // Test that the auth routes are properly mounted
      const response = await request(testApp)
        .post('/api/auth/login')
        .send({})
        .expect(400); // Should return validation error, not 404

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe(400);
    });

    test('should have user routes available', async () => {
      const response = await request(testApp)
        .get('/api/users')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401); // Should return auth error, not 404

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe(401);
    });

    test('should have course routes available', async () => {
      const response = await request(testApp)
        .get('/api/courses')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401); // Should return auth error, not 404

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe(401);
    });

    test('should have project routes available', async () => {
      const response = await request(testApp)
        .get('/api/projects')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401); // Should return auth error, not 404

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe(401);
    });

    test('should have LTI routes available', async () => {
      const response = await request(testApp)
        .get('/api/lti/config')
        .expect(200); // LTI config should be publicly accessible

      expect(response.body).toHaveProperty('issuer');
      expect(response.body).toHaveProperty('jwks_uri');
    });
  });

  describe('Authentication Middleware Tests', () => {
    test('should reject requests without authentication token', async () => {
      const response = await request(testApp)
        .get('/api/users')
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Access denied');
      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe(401);
    });

    test('should reject requests with invalid authentication token', async () => {
      const response = await request(testApp)
        .get('/api/users')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe(401);
    });

    test('should reject requests with malformed authentication header', async () => {
      const response = await request(testApp)
        .get('/api/users')
        .set('Authorization', 'InvalidFormat token')
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe(401);
    });
  });

  describe('Error Handling Tests', () => {
    test('should handle 404 errors properly', async () => {
      const response = await request(testApp)
        .get('/api/nonexistent-route')
        .expect(404);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Resource not found');
      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe(404);
      expect(response.body).toHaveProperty('path');
      expect(response.body).toHaveProperty('method');
    });

    test('should handle validation errors properly', async () => {
      const response = await request(testApp)
        .post('/api/auth/login')
        .send({}) // Empty body should trigger validation error
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe(400);
    });

    test('should handle malformed JSON properly', async () => {
      const response = await request(testApp)
        .post('/api/auth/login')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe(400);
    });
  });

  describe('CORS Tests', () => {
    test('should handle CORS preflight requests', async () => {
      const response = await request(testApp)
        .options('/api/auth/login')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST')
        .set('Access-Control-Request-Headers', 'Content-Type')
        .expect(200);

      expect(response.headers).toHaveProperty('access-control-allow-origin');
      expect(response.headers).toHaveProperty('access-control-allow-methods');
      expect(response.headers).toHaveProperty('access-control-allow-headers');
    });

    test('should include CORS headers in responses', async () => {
      const response = await request(testApp)
        .get('/health')
        .set('Origin', 'http://localhost:3000')
        .expect(200);

      expect(response.headers).toHaveProperty('access-control-allow-origin');
    });
  });

  describe('Security Headers Tests', () => {
    test('should include security headers', async () => {
      const response = await request(testApp)
        .get('/health')
        .expect(200);

      // Check for Helmet security headers
      expect(response.headers).toHaveProperty('x-content-type-options');
      expect(response.headers).toHaveProperty('x-frame-options');
      expect(response.headers).toHaveProperty('x-xss-protection');
    });
  });
});
