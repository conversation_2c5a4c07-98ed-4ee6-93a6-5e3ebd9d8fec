import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('jsonwebtoken');
jest.mock('../../../src/models/associations.js');
jest.mock('../../../src/config/logger.config.js');

import jwt from 'jsonwebtoken';
import { User, Role, Permission } from '../../../src/models/associations.js';
import logger from '../../../src/config/logger.config.js';

// Import the middleware functions
import {
  jwtMiddleware,
  optionalJwtMiddleware,
  generateToken,
  verifyToken
} from '../../../src/middlewares/auth.js';

describe('Auth Middleware', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      headers: {},
      user: null,
      userPermissions: [],
      userRoles: []
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };
    mockNext = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('jwtMiddleware', () => {
    test('should authenticate user with valid token', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        status: 'active',
        roles: [
          {
            name: 'instructor',
            permissions: [
              { key: 'course:create' },
              { key: 'course:read' }
            ]
          }
        ]
      };

      const mockDecoded = {
        userId: 'user-123',
        email: '<EMAIL>'
      };

      jwt.verify = jest.fn().mockReturnValue(mockDecoded);
      User.findByPk = jest.fn().mockResolvedValue(mockUser);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(jwt.verify).toHaveBeenCalledWith('valid-token', process.env.JWT_SECRET);
      expect(User.findByPk).toHaveBeenCalledWith('user-123', expect.any(Object));
      expect(mockRequest.user).toEqual(mockUser);
      expect(mockRequest.userPermissions).toEqual(['course:create', 'course:read']);
      expect(mockRequest.userRoles).toEqual(['instructor']);
      expect(mockRequest.primaryRole).toBe('instructor');
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should reject request without authorization header', async () => {
      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Access denied',
        message: 'No token provided or invalid format'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('should reject request with invalid authorization format', async () => {
      mockRequest.headers.authorization = 'InvalidFormat token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Access denied',
        message: 'No token provided or invalid format'
      });
    });

    test('should handle expired token', async () => {
      const mockError = new Error('Token expired');
      mockError.name = 'TokenExpiredError';

      jwt.verify = jest.fn().mockImplementation(() => {
        throw mockError;
      });

      mockRequest.headers.authorization = 'Bearer expired-token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Token expired',
        message: 'Please login again'
      });
    });

    test('should handle malformed token', async () => {
      const mockError = new Error('Invalid token');
      mockError.name = 'JsonWebTokenError';

      jwt.verify = jest.fn().mockImplementation(() => {
        throw mockError;
      });

      mockRequest.headers.authorization = 'Bearer malformed-token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid token',
        message: 'Token is malformed'
      });
    });

    test('should handle user not found', async () => {
      const mockDecoded = {
        userId: 'user-123',
        email: '<EMAIL>'
      };

      jwt.verify = jest.fn().mockReturnValue(mockDecoded);
      User.findByPk = jest.fn().mockResolvedValue(null);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Access denied',
        message: 'User not found'
      });
    });

    test('should handle inactive user', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        status: 'inactive',
        roles: []
      };

      const mockDecoded = {
        userId: 'user-123',
        email: '<EMAIL>'
      };

      jwt.verify = jest.fn().mockReturnValue(mockDecoded);
      User.findByPk = jest.fn().mockResolvedValue(mockUser);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Access denied',
        message: 'User account is not active'
      });
    });

    test('should handle user with no roles', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        status: 'active',
        roles: []
      };

      const mockDecoded = {
        userId: 'user-123',
        email: '<EMAIL>'
      };

      jwt.verify = jest.fn().mockReturnValue(mockDecoded);
      User.findByPk = jest.fn().mockResolvedValue(mockUser);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockRequest.user).toEqual(mockUser);
      expect(mockRequest.userPermissions).toEqual([]);
      expect(mockRequest.userRoles).toEqual([]);
      expect(mockRequest.primaryRole).toBe('student');
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should handle user with roles but no permissions', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        status: 'active',
        roles: [
          {
            name: 'student',
            permissions: []
          }
        ]
      };

      const mockDecoded = {
        userId: 'user-123',
        email: '<EMAIL>'
      };

      jwt.verify = jest.fn().mockReturnValue(mockDecoded);
      User.findByPk = jest.fn().mockResolvedValue(mockUser);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockRequest.userPermissions).toEqual([]);
      expect(mockRequest.userRoles).toEqual(['student']);
      expect(mockRequest.primaryRole).toBe('student');
    });

    test('should handle duplicate permissions', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        status: 'active',
        roles: [
          {
            name: 'instructor',
            permissions: [
              { key: 'course:read' },
              { key: 'course:read' } // duplicate
            ]
          }
        ]
      };

      const mockDecoded = {
        userId: 'user-123',
        email: '<EMAIL>'
      };

      jwt.verify = jest.fn().mockReturnValue(mockDecoded);
      User.findByPk = jest.fn().mockResolvedValue(mockUser);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockRequest.userPermissions).toEqual(['course:read']);
      expect(mockRequest.userRoles).toEqual(['instructor']);
    });

    test('should handle multiple roles with permissions', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        status: 'active',
        roles: [
          {
            name: 'student',
            permissions: [
              { key: 'course:read' }
            ]
          },
          {
            name: 'instructor',
            permissions: [
              { key: 'course:create' },
              { key: 'course:update' }
            ]
          }
        ]
      };

      const mockDecoded = {
        userId: 'user-123',
        email: '<EMAIL>'
      };

      jwt.verify = jest.fn().mockReturnValue(mockDecoded);
      User.findByPk = jest.fn().mockResolvedValue(mockUser);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockRequest.userPermissions).toEqual(['course:read', 'course:create', 'course:update']);
      expect(mockRequest.userRoles).toEqual(['student', 'instructor']);
      expect(mockRequest.primaryRole).toBe('student');
    });

    test('should handle unexpected JWT errors', async () => {
      const mockError = new Error('Unexpected JWT error');

      jwt.verify = jest.fn().mockImplementation(() => {
        throw mockError;
      });

      mockRequest.headers.authorization = 'Bearer valid-token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(logger.error).toHaveBeenCalledWith('JWT middleware error:', mockError);
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Authentication error',
        message: 'Internal server error during authentication'
      });
    });

    test('should handle database errors', async () => {
      const mockDecoded = {
        userId: 'user-123',
        email: '<EMAIL>'
      };

      const mockError = new Error('Database connection failed');

      jwt.verify = jest.fn().mockReturnValue(mockDecoded);
      User.findByPk = jest.fn().mockRejectedValue(mockError);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await jwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(logger.error).toHaveBeenCalledWith('JWT middleware error:', mockError);
      expect(mockResponse.status).toHaveBeenCalledWith(500);
    });
  });

  describe('optionalJwtMiddleware', () => {
    test('should proceed without token', async () => {
      await optionalJwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockRequest.user).toBeNull();
      expect(mockRequest.userPermissions).toEqual([]);
      expect(mockRequest.userRoles).toEqual([]);
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should validate token when provided', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        status: 'active',
        roles: []
      };

      const mockDecoded = {
        userId: 'user-123',
        email: '<EMAIL>'
      };

      jwt.verify = jest.fn().mockReturnValue(mockDecoded);
      User.findByPk = jest.fn().mockResolvedValue(mockUser);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await optionalJwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockRequest.user).toEqual(mockUser);
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should handle invalid token format', async () => {
      mockRequest.headers.authorization = 'InvalidFormat token';

      await optionalJwtMiddleware(mockRequest, mockResponse, mockNext);

      expect(mockRequest.user).toBeNull();
      expect(mockRequest.userPermissions).toEqual([]);
      expect(mockRequest.userRoles).toEqual([]);
      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('generateToken', () => {
    test('should generate valid JWT token', () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User'
      };

      const mockToken = 'generated.jwt.token';
      jwt.sign = jest.fn().mockReturnValue(mockToken);

      const result = generateToken(mockUser);

      expect(jwt.sign).toHaveBeenCalledWith(
        {
          userId: 'user-123',
          email: '<EMAIL>',
          name: 'Test User'
        },
        process.env.JWT_SECRET,
        {
          expiresIn: process.env.JWT_EXPIRES_IN || '24h',
          issuer: process.env.JWT_ISSUER || 'bits-dataScience-platform',
          audience: process.env.JWT_AUDIENCE || 'bits-platform-users'
        }
      );
      expect(result).toBe(mockToken);
    });

    test('should use default options when environment variables are not set', () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User'
      };

      const mockToken = 'generated.jwt.token';
      jwt.sign = jest.fn().mockReturnValue(mockToken);

      // Clear environment variables
      const originalJwtExpiresIn = process.env.JWT_EXPIRES_IN;
      const originalJwtIssuer = process.env.JWT_ISSUER;
      const originalJwtAudience = process.env.JWT_AUDIENCE;

      delete process.env.JWT_EXPIRES_IN;
      delete process.env.JWT_ISSUER;
      delete process.env.JWT_AUDIENCE;

      generateToken(mockUser);

      expect(jwt.sign).toHaveBeenCalledWith(
        expect.any(Object),
        process.env.JWT_SECRET,
        {
          expiresIn: '24h',
          issuer: 'bits-dataScience-platform',
          audience: 'bits-platform-users'
        }
      );

      // Restore environment variables
      process.env.JWT_EXPIRES_IN = originalJwtExpiresIn;
      process.env.JWT_ISSUER = originalJwtIssuer;
      process.env.JWT_AUDIENCE = originalJwtAudience;
    });
  });

  describe('verifyToken', () => {
    test('should verify valid token', () => {
      const mockDecoded = {
        userId: 'user-123',
        email: '<EMAIL>'
      };

      jwt.verify = jest.fn().mockReturnValue(mockDecoded);

      const result = verifyToken('valid-token');

      expect(jwt.verify).toHaveBeenCalledWith('valid-token', process.env.JWT_SECRET);
      expect(result).toEqual(mockDecoded);
    });

    test('should return null for invalid token', () => {
      jwt.verify = jest.fn().mockImplementation(() => {
        throw new Error('Invalid token');
      });

      const result = verifyToken('invalid-token');

      expect(result).toBeNull();
    });

    test('should return null for expired token', () => {
      const mockError = new Error('Token expired');
      mockError.name = 'TokenExpiredError';

      jwt.verify = jest.fn().mockImplementation(() => {
        throw mockError;
      });

      const result = verifyToken('expired-token');

      expect(result).toBeNull();
    });
  });
});
