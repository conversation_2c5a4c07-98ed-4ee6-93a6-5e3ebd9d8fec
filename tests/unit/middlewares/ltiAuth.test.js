import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('jsonwebtoken');
jest.mock('../../../src/models/ltiAssociations.models.js');
jest.mock('../../../src/models/associations.js');
jest.mock('../../../src/config/logger.config.js');
jest.mock('../../../src/middlewares/errorHandler.middlewares.js', () => ({
  asyncHandler: jest.fn((fn) => fn)
}));

import jwt from 'jsonwebtoken';
import { LtiPlatform } from '../../../src/models/ltiAssociations.models.js';
import { User } from '../../../src/models/associations.js';
import logger from '../../../src/config/logger.config.js';

// Import the middleware functions
import {
  authenticateLTI,
  requireLTIRole,
  requireLTIContext,
  validateLTIMessageType,
  extractLTICustomParams,
  requireGradePassback,
  validatePlatform,
  rateLimitLTI,
  logLTIActivity
} from '../../../src/middlewares/ltiAuth.js';

describe('LTI Authentication Middleware', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      headers: {},
      body: {},
      session: {},
      ip: '127.0.0.1',
      method: 'POST',
      path: '/api/lti/launch',
      get: jest.fn().mockReturnValue('Mozilla/5.0')
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };
    mockNext = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('authenticateLTI', () => {
    test('should authenticate with Bearer token', async () => {
      const mockDecoded = {
        payload: {
          iss: 'https://canvas.instructure.com',
          sub: 'user-123',
          email: '<EMAIL>',
          name: 'Test User',
          given_name: 'Test',
          family_name: 'User',
          'https://purl.imsglobal.org/spec/lti/claim/roles': ['Learner']
        }
      };

      const mockPlatform = {
        platformId: 'https://canvas.instructure.com',
        clientId: 'client-123',
        isActive: true
      };

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        lmsUserId: 'user-123'
      };

      jwt.decode = jest.fn().mockReturnValue(mockDecoded);
      LtiPlatform.findOne = jest.fn().mockResolvedValue(mockPlatform);
      User.findOrCreate = jest.fn().mockResolvedValue([mockUser, false]);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await authenticateLTI(mockRequest, mockResponse, mockNext);

      expect(jwt.decode).toHaveBeenCalledWith('valid-token', { complete: true });
      expect(LtiPlatform.findOne).toHaveBeenCalledWith({
        where: { platformId: 'https://canvas.instructure.com' }
      });
      expect(User.findOrCreate).toHaveBeenCalled();
      expect(mockRequest.user).toEqual(mockUser);
      expect(mockRequest.ltiLaunchData).toEqual(mockDecoded.payload);
      expect(mockRequest.ltiPlatform).toEqual(mockPlatform);
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should authenticate with session data', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User'
      };

      const mockLtiContext = { id: 'context-123' };
      const mockLtiResourceLink = { id: 'resource-123' };
      const mockLtiLaunchData = { id_token: 'session-token' };

      mockRequest.session = {
        user: mockUser,
        ltiContext: mockLtiContext,
        ltiResourceLink: mockLtiResourceLink,
        ltiLaunchData: mockLtiLaunchData
      };

      await authenticateLTI(mockRequest, mockResponse, mockNext);

      expect(mockRequest.user).toEqual(mockUser);
      expect(mockRequest.ltiContext).toEqual(mockLtiContext);
      expect(mockRequest.ltiResourceLink).toEqual(mockLtiResourceLink);
      expect(mockRequest.ltiLaunchData).toEqual(mockLtiLaunchData);
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should authenticate with body token', async () => {
      const mockDecoded = {
        payload: {
          iss: 'https://canvas.instructure.com',
          sub: 'user-123',
          email: '<EMAIL>',
          name: 'Test User',
          'https://purl.imsglobal.org/spec/lti/claim/roles': ['Learner']
        }
      };

      const mockPlatform = {
        platformId: 'https://canvas.instructure.com',
        clientId: 'client-123',
        isActive: true
      };

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        lmsUserId: 'user-123'
      };

      jwt.decode = jest.fn().mockReturnValue(mockDecoded);
      LtiPlatform.findOne = jest.fn().mockResolvedValue(mockPlatform);
      User.findOrCreate = jest.fn().mockResolvedValue([mockUser, false]);

      mockRequest.body.id_token = 'body-token';

      await authenticateLTI(mockRequest, mockResponse, mockNext);

      expect(jwt.decode).toHaveBeenCalledWith('body-token', { complete: true });
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should reject request without token', async () => {
      await authenticateLTI(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Access Denied',
        message: 'No valid LTI token provided'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('should handle invalid token format', async () => {
      jwt.decode = jest.fn().mockReturnValue(null);

      mockRequest.headers.authorization = 'Bearer invalid-token';

      await authenticateLTI(mockRequest, mockResponse, mockNext);

      expect(logger.error).toHaveBeenCalledWith('LTI authentication failed:', expect.any(Error));
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Authentication Failed',
        message: 'Invalid or expired LTI token'
      });
    });

    test('should handle unknown platform', async () => {
      const mockDecoded = {
        payload: {
          iss: 'https://unknown-platform.com',
          sub: 'user-123'
        }
      };

      jwt.decode = jest.fn().mockReturnValue(mockDecoded);
      LtiPlatform.findOne = jest.fn().mockResolvedValue(null);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await authenticateLTI(mockRequest, mockResponse, mockNext);

      expect(logger.error).toHaveBeenCalledWith('LTI authentication failed:', expect.any(Error));
      expect(mockResponse.status).toHaveBeenCalledWith(401);
    });

    test('should create new user if not exists', async () => {
      const mockDecoded = {
        payload: {
          iss: 'https://canvas.instructure.com',
          sub: 'user-123',
          email: '<EMAIL>',
          name: 'New User',
          given_name: 'New',
          family_name: 'User',
          'https://purl.imsglobal.org/spec/lti/claim/roles': ['Instructor']
        }
      };

      const mockPlatform = {
        platformId: 'https://canvas.instructure.com',
        clientId: 'client-123',
        isActive: true
      };

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'New User',
        lmsUserId: 'user-123'
      };

      jwt.decode = jest.fn().mockReturnValue(mockDecoded);
      LtiPlatform.findOne = jest.fn().mockResolvedValue(mockPlatform);
      User.findOrCreate = jest.fn().mockResolvedValue([mockUser, true]);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await authenticateLTI(mockRequest, mockResponse, mockNext);

      expect(User.findOrCreate).toHaveBeenCalledWith({
        where: { lmsUserId: 'user-123' },
        defaults: {
          name: 'New User',
          email: '<EMAIL>',
          lmsUserId: 'user-123',
          status: 'active',
          profileData: {
            firstName: 'New',
            lastName: 'User',
            ltiRoles: ['Instructor']
          }
        }
      });
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should update user email if changed', async () => {
      const mockDecoded = {
        payload: {
          iss: 'https://canvas.instructure.com',
          sub: 'user-123',
          email: '<EMAIL>',
          name: 'Updated User',
          given_name: 'Updated',
          family_name: 'User',
          'https://purl.imsglobal.org/spec/lti/claim/roles': ['Learner']
        }
      };

      const mockPlatform = {
        platformId: 'https://canvas.instructure.com',
        clientId: 'client-123',
        isActive: true
      };

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Old User',
        lmsUserId: 'user-123',
        update: jest.fn().mockResolvedValue(true)
      };

      jwt.decode = jest.fn().mockReturnValue(mockDecoded);
      LtiPlatform.findOne = jest.fn().mockResolvedValue(mockPlatform);
      User.findOrCreate = jest.fn().mockResolvedValue([mockUser, false]);

      mockRequest.headers.authorization = 'Bearer valid-token';

      await authenticateLTI(mockRequest, mockResponse, mockNext);

      expect(mockUser.update).toHaveBeenCalledWith({
        email: '<EMAIL>',
        name: 'Updated User'
      });
    });
  });

  describe('requireLTIRole', () => {
    test('should allow access with required role', async () => {
      const middleware = requireLTIRole(['Instructor']);

      mockRequest.ltiLaunchData = {
        'https://purl.imsglobal.org/spec/lti/claim/roles': [
          'http://purl.imsglobal.org/vocab/lis/v2/membership#Instructor'
        ]
      };

      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should reject access without required role', async () => {
      const middleware = requireLTIRole(['Instructor']);

      mockRequest.ltiLaunchData = {
        'https://purl.imsglobal.org/spec/lti/claim/roles': [
          'http://purl.imsglobal.org/vocab/lis/v2/membership#Learner'
        ]
      };

      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Insufficient Permissions',
        message: 'Required LTI role not found'
      });
    });

    test('should reject access without LTI context', async () => {
      const middleware = requireLTIRole(['Instructor']);

      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Access Denied',
        message: 'LTI context required'
      });
    });

    test('should allow access with partial role match', async () => {
      const middleware = requireLTIRole(['Instructor', 'Learner']);

      mockRequest.ltiLaunchData = {
        'https://purl.imsglobal.org/spec/lti/claim/roles': [
          'http://purl.imsglobal.org/vocab/lis/v2/membership#Learner'
        ]
      };

      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('requireLTIContext', () => {
    test('should allow access with LTI context', async () => {
      mockRequest.ltiContext = { id: 'context-123' };

      await requireLTIContext(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should reject access without LTI context', async () => {
      await requireLTIContext(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Missing Context',
        message: 'LTI context (course) required for this operation'
      });
    });
  });

  describe('validateLTIMessageType', () => {
    test('should allow valid message type', async () => {
      const middleware = validateLTIMessageType(['LtiResourceLinkRequest']);

      mockRequest.ltiLaunchData = {
        'https://purl.imsglobal.org/spec/lti/claim/message_type': 'LtiResourceLinkRequest'
      };

      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should reject invalid message type', async () => {
      const middleware = validateLTIMessageType(['LtiResourceLinkRequest']);

      mockRequest.ltiLaunchData = {
        'https://purl.imsglobal.org/spec/lti/claim/message_type': 'LtiDeepLinkingRequest'
      };

      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid Message Type',
        message: 'Expected message type: LtiResourceLinkRequest'
      });
    });

    test('should reject without launch data', async () => {
      const middleware = validateLTIMessageType(['LtiResourceLinkRequest']);

      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Missing Launch Data',
        message: 'LTI launch data required'
      });
    });

    test('should allow multiple valid message types', async () => {
      const middleware = validateLTIMessageType(['LtiResourceLinkRequest', 'LtiDeepLinkingRequest']);

      mockRequest.ltiLaunchData = {
        'https://purl.imsglobal.org/spec/lti/claim/message_type': 'LtiDeepLinkingRequest'
      };

      await middleware(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('extractLTICustomParams', () => {
    test('should extract custom parameters', async () => {
      mockRequest.ltiLaunchData = {
        'https://purl.imsglobal.org/spec/lti/claim/custom': {
          course_id: 'course-123',
          assignment_id: 'assignment-456'
        }
      };

      await extractLTICustomParams(mockRequest, mockResponse, mockNext);

      expect(mockRequest.ltiCustomParams).toEqual({
        course_id: 'course-123',
        assignment_id: 'assignment-456'
      });
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should set empty object when no custom parameters', async () => {
      mockRequest.ltiLaunchData = {};

      await extractLTICustomParams(mockRequest, mockResponse, mockNext);

      expect(mockRequest.ltiCustomParams).toEqual({});
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should set empty object when no launch data', async () => {
      await extractLTICustomParams(mockRequest, mockResponse, mockNext);

      expect(mockRequest.ltiCustomParams).toEqual({});
      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('requireGradePassback', () => {
    test('should allow access with grade passback capability', async () => {
      mockRequest.ltiLaunchData = {
        'https://purl.imsglobal.org/spec/lti-ags/claim/endpoint': {
          scope: ['https://purl.imsglobal.org/spec/lti-ags/scope/score']
        }
      };

      await requireGradePassback(mockRequest, mockResponse, mockNext);

      expect(mockRequest.ltiAGSEndpoint).toEqual({
        scope: ['https://purl.imsglobal.org/spec/lti-ags/scope/score']
      });
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should reject without launch data', async () => {
      await requireGradePassback(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Missing Launch Data',
        message: 'LTI launch data required for grade passback'
      });
    });

    test('should reject without AGS claim', async () => {
      mockRequest.ltiLaunchData = {};

      await requireGradePassback(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Grade Passback Not Available',
        message: 'This LTI launch does not support grade passback'
      });
    });

    test('should reject without score scope', async () => {
      mockRequest.ltiLaunchData = {
        'https://purl.imsglobal.org/spec/lti-ags/claim/endpoint': {
          scope: ['https://purl.imsglobal.org/spec/lti-ags/scope/lineitem']
        }
      };

      await requireGradePassback(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Grade Passback Not Available',
        message: 'This LTI launch does not support grade passback'
      });
    });
  });

  describe('validatePlatform', () => {
    test('should validate registered platform', async () => {
      const mockPlatform = {
        platformId: 'https://canvas.instructure.com',
        clientId: 'client-123',
        isActive: true
      };

      LtiPlatform.findOne = jest.fn().mockResolvedValue(mockPlatform);

      mockRequest.body = {
        iss: 'https://canvas.instructure.com',
        client_id: 'client-123'
      };

      await validatePlatform(mockRequest, mockResponse, mockNext);

      expect(LtiPlatform.findOne).toHaveBeenCalledWith({
        where: {
          platformId: 'https://canvas.instructure.com',
          clientId: 'client-123',
          isActive: true
        }
      });
      expect(mockRequest.ltiPlatform).toEqual(mockPlatform);
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should reject missing parameters', async () => {
      mockRequest.body = {};

      await validatePlatform(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Missing Parameters',
        message: 'Platform issuer (iss) and client_id are required'
      });
    });

    test('should reject unregistered platform', async () => {
      LtiPlatform.findOne = jest.fn().mockResolvedValue(null);

      mockRequest.body = {
        iss: 'https://unknown-platform.com',
        client_id: 'client-123'
      };

      await validatePlatform(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid Platform',
        message: 'Platform not registered or inactive'
      });
    });
  });

  describe('rateLimitLTI', () => {
    test('should allow request (placeholder implementation)', async () => {
      await rateLimitLTI(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('logLTIActivity', () => {
    test('should log LTI activity with full data', async () => {
      mockRequest.body = {
        iss: 'https://canvas.instructure.com',
        sub: 'user-123'
      };
      mockRequest.ltiPlatform = {
        platformId: 'https://canvas.instructure.com'
      };
      mockRequest.user = {
        lmsUserId: 'user-123'
      };

      await logLTIActivity(mockRequest, mockResponse, mockNext);

      expect(logger.info).toHaveBeenCalledWith('LTI Activity:', {
        method: 'POST',
        path: '/api/lti/launch',
        platform: 'https://canvas.instructure.com',
        user: 'user-123',
        timestamp: expect.any(Date),
        ip: '127.0.0.1',
        userAgent: 'Mozilla/5.0'
      });
      expect(mockNext).toHaveBeenCalledWith();
    });

    test('should log LTI activity with minimal data', async () => {
      await logLTIActivity(mockRequest, mockResponse, mockNext);

      expect(logger.info).toHaveBeenCalledWith('LTI Activity:', {
        method: 'POST',
        path: '/api/lti/launch',
        platform: undefined,
        user: undefined,
        timestamp: expect.any(Date),
        ip: '127.0.0.1',
        userAgent: 'Mozilla/5.0'
      });
      expect(mockNext).toHaveBeenCalledWith();
    });
  });
});
