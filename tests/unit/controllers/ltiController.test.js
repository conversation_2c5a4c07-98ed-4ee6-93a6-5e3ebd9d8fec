import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../src/models/associations.js', () => ({
  LtiPlatform: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    findByPk: jest.fn()
  },
  LtiContext: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn()
  },
  LtiDeployment: {
    findOne: jest.fn(),
    create: jest.fn()
  },
  LtiLaunchSession: {
    create: jest.fn(),
    findOne: jest.fn()
  },
  LtiLineItem: {
    findOne: jest.fn(),
    create: jest.fn()
  },
  LtiResourceLink: {
    findOne: jest.fn(),
    create: jest.fn()
  },
  User: {
    findOne: jest.fn(),
    create: jest.fn()
  },
  Course: {
    findOne: jest.fn(),
    findByPk: jest.fn()
  },
  Grade: {
    findOne: jest.fn()
  }
}));

jest.mock('../../../src/config/logger.config.js', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

jest.mock('../../../src/middlewares/errorHandler.middlewares.js', () => ({
  asyncHandler: jest.fn((fn) => fn)
}));

jest.mock('../../../src/services/ltiService.js', () => ({
  default: {
    generateAuthRequest: jest.fn(),
    processLaunch: jest.fn(),
    getJWKS: jest.fn(),
    sendGradeToLMS: jest.fn()
  }
}));

// Import the controller functions
import {
  initiateLogin,
  handleLaunch,
  getJWKS,
  handleDeepLinking,
  registerPlatform,
  getPlatforms,
  updatePlatform,
  deletePlatform,
  sendGradeToLMS,
  getCourseContexts,
  getLTIConfiguration
} from '../../../src/controllers/ltiController.js';

import { LtiPlatform, LtiContext, LtiDeployment, LtiLaunchSession, LtiLineItem, LtiResourceLink, User, Course, Grade } from '../../../src/models/associations.js';
import ltiService from '../../../src/services/ltiService.js';
import logger from '../../../src/config/logger.config.js';

describe('LTI Controller', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      params: {},
      query: {},
      body: {},
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        roles: [{ name: 'instructor' }]
      },
      userRoles: ['instructor']
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      redirect: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getLTIConfiguration', () => {
    test('should return LTI configuration', async () => {
      await getLTIConfiguration(mockRequest, mockResponse, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({
        title: 'BITS-DataScience Projects Platform',
        description: 'Interactive data science projects and assignments platform for BITS Pilani',
        target_link_uri: expect.stringContaining('/lti/launch'),
        oidc_initiation_url: expect.stringContaining('/api/lti/login'),
        public_jwk_url: expect.stringContaining('/api/lti/jwks'),
        scopes: expect.arrayContaining([
          'openid',
          'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem',
          'https://purl.imsglobal.org/spec/lti-ags/scope/score',
          'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly'
        ]),
        extensions: expect.any(Array),
        custom_fields: expect.any(Object),
        claims: expect.any(Array)
      });
    });
  });

  describe('initiateLogin', () => {
    test('should initiate LTI login successfully', async () => {
      const loginData = {
        iss: 'https://canvas.instructure.com',
        login_hint: 'user-123',
        target_link_uri: 'https://example.com/launch',
        client_id: 'client-123',
        lti_message_hint: 'hint'
      };

      const mockPlatform = {
        id: 'platform-123',
        platformName: 'Canvas',
        platformId: 'https://canvas.instructure.com',
        clientId: 'client-123',
        authLoginUrl: 'https://canvas.instructure.com/login/oauth2/auth',
        authTokenUrl: 'https://canvas.instructure.com/login/oauth2/token',
        keySetUrl: 'https://canvas.instructure.com/api/lti/security/jwks',
        isActive: true
      };

      mockRequest.body = loginData;
      LtiPlatform.findOne.mockResolvedValue(mockPlatform);
      ltiService.default.generateAuthRequest.mockReturnValue('https://canvas.instructure.com/login/oauth2/auth?state=abc&nonce=def');

      await initiateLogin(mockRequest, mockResponse, mockNext);

      expect(LtiPlatform.findOne).toHaveBeenCalledWith({
        where: {
          platformId: loginData.iss,
          clientId: loginData.client_id,
          isActive: true
        }
      });
      expect(ltiService.default.generateAuthRequest).toHaveBeenCalledWith(mockPlatform, loginData.target_link_uri, loginData.login_hint, loginData.lti_message_hint);
      expect(mockResponse.redirect).toHaveBeenCalledWith('https://canvas.instructure.com/login/oauth2/auth?state=abc&nonce=def');
    });

    test('should return 400 when platform not found', async () => {
      mockRequest.body = {
        iss: 'https://invalid.com',
        client_id: 'invalid-client'
      };
      LtiPlatform.findOne.mockResolvedValue(null);

      await initiateLogin(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid Platform',
        message: 'Platform not registered or inactive'
      });
    });
  });

  describe('handleLaunch', () => {
    test('should handle LTI launch successfully', async () => {
      const launchData = {
        id_token: 'valid.jwt.token',
        state: 'state-123'
      };

      const mockLaunchSession = {
        id: 'session-123',
        state: 'state-123',
        nonce: 'nonce-123'
      };

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User'
      };

      mockRequest.body = launchData;
      mockRequest.session = {};
      ltiService.default.processLaunch.mockResolvedValue({
        user: mockUser,
        context: { id: 'course-123', label: 'CS101', title: 'Computer Science 101' },
        resourceLink: { id: 'resource-123', projectId: 'project-123' },
        launchData: {
          'https://purl.imsglobal.org/spec/lti/claim/message_type': 'LtiResourceLinkRequest'
        }
      });

      await handleLaunch(mockRequest, mockResponse, mockNext);

      expect(ltiService.default.processLaunch).toHaveBeenCalledWith(launchData.id_token, launchData.state);
      expect(mockResponse.redirect).toHaveBeenCalledWith(expect.stringContaining('/project/project-123'));
    });

    test('should return 400 when launch session not found', async () => {
      mockRequest.body = { state: 'invalid-state' };
      LtiLaunchSession.findOne.mockResolvedValue(null);

      await handleLaunch(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Missing Parameters',
        message: 'id_token and state are required'
      });
    });
  });

  describe('getJWKS', () => {
    test('should return JWKS', async () => {
      const mockJWKS = {
        keys: [
          {
            kty: 'RSA',
            kid: 'key-1',
            use: 'sig',
            alg: 'RS256'
          }
        ]
      };

      ltiService.default.getJWKS.mockReturnValue(mockJWKS);

      await getJWKS(mockRequest, mockResponse, mockNext);

      expect(ltiService.default.getJWKS).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith(mockJWKS);
    });
  });

  describe('handleDeepLinking', () => {
    test('should handle deep linking successfully', async () => {
      const deepLinkData = {
        id_token: 'valid.jwt.token',
        state: 'state-123'
      };

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.body = deepLinkData;
      // Mock the deep linking response
      mockResponse.json.mockReturnValue({
        success: true,
        message: 'Deep linking successful',
        contentItems: []
      });

      await handleDeepLinking(mockRequest, mockResponse, mockNext);

      // Deep linking test - just check that response is called
      expect(mockResponse.json).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Deep linking successful',
        contentItems: expect.any(Array)
      });
    });
  });

  describe('registerPlatform', () => {
    test('should register platform successfully', async () => {
      const platformData = {
        platformName: 'Test Platform',
        platformId: 'https://test.example.com',
        clientId: 'test-client',
        authLoginUrl: 'https://test.example.com/login',
        authTokenUrl: 'https://test.example.com/token',
        keySetUrl: 'https://test.example.com/jwks'
      };

      const createdPlatform = {
        id: 'platform-123',
        ...platformData
      };

      mockRequest.body = platformData;
      LtiPlatform.findOne.mockResolvedValue(null);
      LtiPlatform.create.mockResolvedValue(createdPlatform);

      await registerPlatform(mockRequest, mockResponse, mockNext);

      expect(LtiPlatform.findOne).toHaveBeenCalledWith({
        where: {
          platformId: platformData.platformId
        }
      });
      expect(LtiPlatform.create).toHaveBeenCalledWith(platformData);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Platform registered successfully',
        platform: createdPlatform
      });
    });

    test('should return 409 when platform already exists', async () => {
      const platformData = {
        platformId: 'https://existing.example.com',
        clientId: 'existing-client'
      };

      mockRequest.body = platformData;
      LtiPlatform.findOne.mockResolvedValue({ id: 'existing-platform' });

      await registerPlatform(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(409);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Conflict',
        message: 'Platform already registered'
      });
    });
  });

  describe('getPlatforms', () => {
    test('should get all platforms', async () => {
      const mockPlatforms = [
        {
          id: 'platform-1',
          platformName: 'Canvas',
          platformId: 'https://canvas.instructure.com',
          clientId: 'canvas-client'
        },
        {
          id: 'platform-2',
          platformName: 'Blackboard',
          platformId: 'https://blackboard.com',
          clientId: 'blackboard-client'
        }
      ];

      LtiPlatform.findAll.mockResolvedValue(mockPlatforms);

      await getPlatforms(mockRequest, mockResponse, mockNext);

      expect(LtiPlatform.findAll).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        platforms: mockPlatforms
      });
    });
  });

  describe('updatePlatform', () => {
    test('should update platform successfully', async () => {
      const updateData = {
        name: 'Updated Platform',
        auth_login_url: 'https://updated.example.com/login'
      };

      const mockPlatform = {
        id: 'platform-123',
        update: jest.fn().mockResolvedValue(true)
      };

      mockRequest.params = { id: 'platform-123' };
      mockRequest.body = updateData;
      LtiPlatform.findByPk.mockResolvedValue(mockPlatform);

      await updatePlatform(mockRequest, mockResponse, mockNext);

      expect(LtiPlatform.findByPk).toHaveBeenCalledWith('platform-123');
      expect(mockPlatform.update).toHaveBeenCalledWith(updateData);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Platform updated successfully',
        platform: mockPlatform
      });
    });

    test('should return 404 when platform not found', async () => {
      mockRequest.params = { id: 'platform-123' };
      LtiPlatform.findByPk.mockResolvedValue(null);

      await updatePlatform(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Platform not found'
      });
    });
  });

  describe('deletePlatform', () => {
    test('should delete platform successfully', async () => {
      const mockPlatform = {
        id: 'platform-123',
        destroy: jest.fn().mockResolvedValue(true)
      };

      mockRequest.params = { id: 'platform-123' };
      LtiPlatform.findByPk.mockResolvedValue(mockPlatform);

      await deletePlatform(mockRequest, mockResponse, mockNext);

      expect(LtiPlatform.findByPk).toHaveBeenCalledWith('platform-123');
      expect(mockPlatform.destroy).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Platform deleted successfully'
      });
    });

    test('should return 404 when platform not found', async () => {
      mockRequest.params = { id: 'platform-123' };
      LtiPlatform.findByPk.mockResolvedValue(null);

      await deletePlatform(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Platform not found'
      });
    });
  });

  describe('sendGradeToLMS', () => {
    test('should send grade to LMS successfully', async () => {
      const gradeData = {
        submissionId: 'submission-123',
        score: 85,
        maxScore: 100,
        comment: 'Good work!'
      };

      const mockSubmission = {
        id: 'submission-123',
        user: {
          id: 'student-123',
          ltiUserId: 'lti-student-123'
        },
        project: {
          id: 'project-123',
          ltiLineItemId: 'line-item-123'
        }
      };

      mockRequest.body = gradeData;
      Submission.findByPk.mockResolvedValue(mockSubmission);
      ltiService.default.sendGradeToLMS.mockResolvedValue({
        success: true,
        message: 'Grade sent successfully'
      });

      await sendGradeToLMS(mockRequest, mockResponse, mockNext);

      expect(Submission.findByPk).toHaveBeenCalledWith(gradeData.submissionId, expect.any(Object));
      expect(ltiService.default.sendGradeToLMS).toHaveBeenCalledWith(
        mockSubmission.project.ltiLineItemId,
        mockSubmission.user.ltiUserId,
        gradeData.score,
        gradeData.maxScore,
        gradeData.comment
      );
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Grade sent to LMS successfully'
      });
    });

    test('should return 404 when grade not found', async () => {
      mockRequest.body = { submissionId: 'submission-123' };
      Submission.findByPk.mockResolvedValue(null);

      await sendGradeToLMS(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Submission not found'
      });
    });
  });

  describe('getCourseContexts', () => {
    test('should get course contexts successfully', async () => {
      const mockContexts = [
        {
          id: 'context-1',
          contextLabel: 'CS101',
          contextTitle: 'Computer Science 101',
          contextType: 'CourseSection',
          platform: { platformName: 'Canvas' },
          resourceLinks: []
        },
        {
          id: 'context-2',
          contextLabel: 'DS101',
          contextTitle: 'Data Science 101',
          contextType: 'CourseSection',
          platform: { platformName: 'Blackboard' },
          resourceLinks: []
        }
      ];

      LtiContext.findAll.mockResolvedValue(mockContexts);

      await getCourseContexts(mockRequest, mockResponse, mockNext);

      expect(LtiContext.findAll).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        contexts: mockContexts
      });
    });

    test('should handle database errors', async () => {
      const error = new Error('Database error');
      LtiContext.findAll.mockRejectedValue(error);

      try {
        await getCourseContexts(mockRequest, mockResponse, mockNext);
      } catch (caughtError) {
        expect(caughtError).toBe(error);
      }
    });
  });
});
