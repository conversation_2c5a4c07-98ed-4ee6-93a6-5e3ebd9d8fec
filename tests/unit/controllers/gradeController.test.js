import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../src/models/associations.js', () => ({
  Grade: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    findAndCountAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    destroy: jest.fn(),
    findByPk: jest.fn(),
    count: jest.fn(),
    bulkCreate: jest.fn(),
    findOrCreate: jest.fn()
  },
  User: {
    findOne: jest.fn(),
    findByPk: jest.fn()
  },
  Project: {
    findByPk: jest.fn()
  },
  Submission: {
    findOne: jest.fn(),
    findAll: jest.fn(),
    findByPk: jest.fn(),
    findAndCountAll: jest.fn()
  },
  Course: {
    findByPk: jest.fn()
  },
  Rubric: {
    findOne: jest.fn(),
    findAll: jest.fn()
  }
}));

jest.mock('../../../src/config/logger.config.js', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

jest.mock('../../../src/middlewares/errorHandler.middlewares.js', () => ({
  asyncHandler: jest.fn((fn) => fn)
}));

// Import the controller functions
import {
  getGrades,
  getGradeById,
  createOrUpdateGrade,
  updateGrade,
  deleteGrade,
  getGradingQueue,
  getGradeStatistics,
  bulkGradeSubmissions
} from '../../../src/controllers/gradeController.js';

import { Grade, User, Project, Submission, Course, Rubric } from '../../../src/models/associations.js';
import logger from '../../../src/config/logger.config.js';

describe('Grade Controller', () => {
  let mockRequest;
  let mockResponse;
  let mockNext;

  beforeEach(() => {
    mockRequest = {
      params: {},
      query: {},
      body: {},
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        roles: [{ name: 'instructor' }]
      },
      userRoles: ['instructor']
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('getGrades', () => {
    test('should get all grades with pagination', async () => {
      const mockGrades = [
        {
          id: 'grade-1',
          total_score: 85,
          max_score: 100,
          percentage: 85,
          letter_grade: 'B',
          feedback: 'Good work!',
          submission: {
            id: 'submission-1',
            project: {
              id: 'project-1',
              title: 'Data Analysis Project',
              course: {
                id: 'course-1',
                name: 'Data Science 101'
              }
            },
            user: {
              id: 'student-1',
              name: 'John Doe',
              email: '<EMAIL>'
            }
          },
          evaluator: {
            id: 'instructor-1',
            name: 'Dr. Smith',
            email: '<EMAIL>'
          }
        },
        {
          id: 'grade-2',
          total_score: 92,
          max_score: 100,
          percentage: 92,
          letter_grade: 'A',
          feedback: 'Excellent work!',
          submission: {
            id: 'submission-2',
            project: {
              id: 'project-2',
              title: 'ML Project',
              course: {
                id: 'course-2',
                name: 'Machine Learning'
              }
            },
            user: {
              id: 'student-2',
              name: 'Jane Smith',
              email: '<EMAIL>'
            }
          },
          evaluator: {
            id: 'instructor-1',
            name: 'Dr. Smith',
            email: '<EMAIL>'
          }
        }
      ];

      mockRequest.query = { page: 1, limit: 10 };
      Grade.findAndCountAll.mockResolvedValue({
        count: 2,
        rows: mockGrades
      });

      await getGrades(mockRequest, mockResponse, mockNext);

      expect(Grade.findAndCountAll).toHaveBeenCalledWith(expect.objectContaining({
        where: {},
        limit: 10,
        offset: 0,
        order: [['graded_at', 'DESC']]
      }));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          grades: expect.arrayContaining([
            expect.objectContaining({
              id: 'grade-1',
              totalScore: 85,
              maxScore: 100,
              percentage: 85,
              letterGrade: 'B',
              feedback: 'Good work!'
            })
          ]),
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalItems: 2,
            itemsPerPage: 10
          }
        }
      });
    });

    test('should filter grades by project ID', async () => {
      mockRequest.query = { projectId: 'project-123' };
      Grade.findAndCountAll.mockResolvedValue({
        count: 0,
        rows: []
      });

      await getGrades(mockRequest, mockResponse, mockNext);

      expect(Grade.findAndCountAll).toHaveBeenCalledWith(expect.objectContaining({
        where: {},
        limit: 10,
        offset: 0,
        order: [['graded_at', 'DESC']]
      }));
    });

    test('should handle database errors', async () => {
      const error = new Error('Database error');
      Grade.findAndCountAll.mockRejectedValue(error);

      try {
        await getGrades(mockRequest, mockResponse, mockNext);
      } catch (caughtError) {
        expect(caughtError).toBe(error);
      }
    });
  });

  describe('getGradeById', () => {
    test('should get grade by ID', async () => {
      const mockGrade = {
        id: 'grade-123',
        total_score: 85,
        max_score: 100,
        percentage: 85,
        letter_grade: 'B',
        feedback: 'Good work!',
        evaluator_id: 'user-123',
        submission: {
          id: 'submission-123',
          project: {
            id: 'project-123',
            title: 'Test Project',
            course: {
              id: 'course-123',
              name: 'Test Course',
              instructor_id: 'user-123'
            }
          },
          user: {
            id: 'student-123',
            name: 'Test Student',
            email: '<EMAIL>'
          }
        },
        evaluator: {
          id: 'instructor-123',
          name: 'Test Instructor',
          email: '<EMAIL>'
        }
      };

      mockRequest.params = { id: 'grade-123' };
      Grade.findByPk.mockResolvedValue(mockGrade);

      await getGradeById(mockRequest, mockResponse, mockNext);

      expect(Grade.findByPk).toHaveBeenCalledWith('grade-123', expect.any(Object));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          id: 'grade-123',
          totalScore: 85,
          maxScore: 100,
          percentage: 85,
          letterGrade: 'B',
          feedback: 'Good work!'
        })
      });
    });

    test('should return 404 when grade not found', async () => {
      mockRequest.params = { id: 'grade-123' };
      Grade.findByPk.mockResolvedValue(null);

      await getGradeById(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Grade not found'
      });
    });
  });

  describe('createOrUpdateGrade', () => {
    test('should create new grade', async () => {
      const gradeData = {
        submissionId: 'submission-123',
        totalScore: 85,
        maxScore: 100,
        feedback: 'Good work!',
        rubricScores: { criteria1: 8, criteria2: 7 }
      };

      const createdGrade = {
        id: 'grade-123',
        ...gradeData
      };

      mockRequest.body = gradeData;
      Submission.findByPk.mockResolvedValue({ 
        id: 'submission-123',
        project: {
          id: 'project-123',
          course: {
            id: 'course-123',
            instructor_id: 'user-123'
          }
        }
      });
      Grade.findOne.mockResolvedValue(null);
      Grade.create.mockResolvedValue(createdGrade);

      await createOrUpdateGrade(mockRequest, mockResponse, mockNext);

      expect(Submission.findByPk).toHaveBeenCalledWith('submission-123', expect.any(Object));
      expect(Grade.findOne).toHaveBeenCalledWith({
        where: { submission_id: 'submission-123' }
      });
      expect(Grade.create).toHaveBeenCalledWith(expect.objectContaining({
        submission_id: gradeData.submissionId,
        evaluator_id: 'user-123',
        total_score: gradeData.totalScore,
        max_score: gradeData.maxScore,
        feedback: gradeData.feedback
      }));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Grade created successfully',
        data: createdGrade
      });
    });

    test('should update existing grade', async () => {
      const gradeData = {
        submissionId: 'submission-123',
        totalScore: 90,
        feedback: 'Updated feedback'
      };

      const existingGrade = {
        id: 'grade-123',
        update: jest.fn().mockResolvedValue(true)
      };

      mockRequest.body = gradeData;
      Submission.findByPk.mockResolvedValue({ 
        id: 'submission-123',
        project: {
          id: 'project-123',
          course: {
            id: 'course-123',
            instructor_id: 'user-123'
          }
        }
      });
      Grade.findOne.mockResolvedValue(existingGrade);

      await createOrUpdateGrade(mockRequest, mockResponse, mockNext);

      expect(existingGrade.update).toHaveBeenCalledWith(expect.objectContaining({
        total_score: gradeData.totalScore,
        feedback: gradeData.feedback
      }));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Grade updated successfully',
        data: existingGrade
      });
    });

    test('should return 404 when submission not found', async () => {
      mockRequest.body = { submissionId: 'submission-123' };
      Submission.findByPk.mockResolvedValue(null);

      await createOrUpdateGrade(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Submission not found'
      });
    });
  });

  describe('updateGrade', () => {
    test('should update grade successfully', async () => {
      const gradeData = {
        totalScore: 90,
        feedback: 'Updated feedback'
      };

      const mockGrade = {
        id: 'grade-123',
        evaluator_id: 'user-123',
        update: jest.fn().mockResolvedValue(true),
        submission: {
          id: 'submission-123',
          project: {
            id: 'project-123',
            course: {
              id: 'course-123',
              instructor_id: 'user-123'
            }
          }
        }
      };

      mockRequest.params = { id: 'grade-123' };
      mockRequest.body = gradeData;
      Grade.findByPk.mockResolvedValue(mockGrade);

      await updateGrade(mockRequest, mockResponse, mockNext);

      expect(Grade.findByPk).toHaveBeenCalledWith('grade-123', expect.any(Object));
      expect(mockGrade.update).toHaveBeenCalledWith(expect.objectContaining({
        total_score: gradeData.totalScore,
        feedback: gradeData.feedback
      }));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Grade updated successfully',
        data: mockGrade
      });
    });

    test('should return 404 when grade not found', async () => {
      mockRequest.params = { id: 'grade-123' };
      Grade.findByPk.mockResolvedValue(null);

      await updateGrade(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Grade not found'
      });
    });
  });

  describe('deleteGrade', () => {
    test('should delete grade successfully', async () => {
      const mockGrade = {
        id: 'grade-123',
        evaluator_id: 'user-123',
        destroy: jest.fn().mockResolvedValue(true),
        submission: {
          id: 'submission-123',
          project: {
            id: 'project-123',
            course: {
              id: 'course-123',
              instructor_id: 'user-123'
            }
          }
        }
      };

      mockRequest.params = { id: 'grade-123' };
      Grade.findByPk.mockResolvedValue(mockGrade);

      await deleteGrade(mockRequest, mockResponse, mockNext);

      expect(Grade.findByPk).toHaveBeenCalledWith('grade-123', expect.any(Object));
      expect(mockGrade.destroy).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Grade deleted successfully'
      });
    });

    test('should return 404 when grade not found', async () => {
      mockRequest.params = { id: 'grade-123' };
      Grade.findByPk.mockResolvedValue(null);

      await deleteGrade(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Grade not found'
      });
    });
  });

  describe('getGradingQueue', () => {
    test('should get grading queue successfully', async () => {
      const mockSubmissions = [
        {
          id: 'submission-1',
          status: 'submitted',
          submitted_at: new Date(),
          project: {
            id: 'project-1',
            title: 'Data Analysis Project',
            course: {
              id: 'course-1',
              name: 'Data Science 101'
            }
          },
          user: {
            id: 'student-1',
            name: 'John Doe',
            email: '<EMAIL>'
          }
        },
        {
          id: 'submission-2',
          status: 'submitted',
          submitted_at: new Date(),
          project: {
            id: 'project-2',
            title: 'ML Project',
            course: {
              id: 'course-2',
              name: 'Machine Learning'
            }
          },
          user: {
            id: 'student-2',
            name: 'Jane Smith',
            email: '<EMAIL>'
          }
        }
      ];

      mockRequest.query = { page: 1, limit: 10 };
      Submission.findAndCountAll.mockResolvedValue({
        count: 2,
        rows: mockSubmissions
      });

      await getGradingQueue(mockRequest, mockResponse, mockNext);

      expect(Submission.findAndCountAll).toHaveBeenCalledWith(expect.objectContaining({
        where: { status: 'submitted' },
        limit: 10,
        offset: 0,
        order: [['submitted_at', 'ASC']]
      }));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          submissions: expect.arrayContaining([
            expect.objectContaining({
              id: 'submission-1',
              status: 'submitted'
            })
          ]),
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalItems: 2,
            itemsPerPage: 10
          }
        }
      });
    });

    test('should handle database errors', async () => {
      const error = new Error('Database error');
      Submission.findAndCountAll.mockRejectedValue(error);

      try {
        await getGradingQueue(mockRequest, mockResponse, mockNext);
      } catch (caughtError) {
        expect(caughtError).toBe(error);
      }
    });
  });

  describe('getGradeStatistics', () => {
    test('should get grade statistics successfully', async () => {
      const mockStats = {
        totalGrades: 10,
        averageGrade: 85.5,
        gradeDistribution: [
          { letter_grade: 'A', count: 3 },
          { letter_grade: 'B', count: 4 },
          { letter_grade: 'C', count: 2 },
          { letter_grade: 'D', count: 1 }
        ]
      };

      Grade.count = jest.fn().mockResolvedValue(10);
      Grade.findAll = jest.fn().mockResolvedValue([
        { percentage: 85 },
        { percentage: 90 },
        { percentage: 80 }
      ]);

      await getGradeStatistics(mockRequest, mockResponse, mockNext);

      expect(Grade.count).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          totalGrades: expect.any(Number),
          averageGrade: expect.any(Number),
          gradeDistribution: expect.any(Array)
        })
      });
    });

    test('should handle database errors', async () => {
      const error = new Error('Database error');
      Grade.count.mockRejectedValue(error);

      try {
        await getGradeStatistics(mockRequest, mockResponse, mockNext);
      } catch (caughtError) {
        expect(caughtError).toBe(error);
      }
    });
  });

  describe('bulkGradeSubmissions', () => {
    test('should bulk grade submissions successfully', async () => {
      const bulkGradeData = {
        submissions: [
          {
            submissionId: 'submission-1',
            totalScore: 85,
            maxScore: 100,
            feedback: 'Good work!'
          },
          {
            submissionId: 'submission-2',
            totalScore: 92,
            maxScore: 100,
            feedback: 'Excellent work!'
          }
        ]
      };

      const mockSubmissions = [
        {
          id: 'submission-1',
          project: {
            course: {
              instructor_id: 'user-123'
            }
          }
        },
        {
          id: 'submission-2',
          project: {
            course: {
              instructor_id: 'user-123'
            }
          }
        }
      ];

      mockRequest.body = bulkGradeData;
      Submission.findByPk
        .mockResolvedValueOnce({
          id: 'submission-1',
          update: jest.fn().mockResolvedValue(true),
          project: {
            course: {
              instructor_id: 'user-123'
            }
          }
        })
        .mockResolvedValueOnce({
          id: 'submission-2',
          update: jest.fn().mockResolvedValue(true),
          project: {
            course: {
              instructor_id: 'user-123'
            }
          }
        });
      Grade.findOrCreate
        .mockResolvedValueOnce([{ id: 'grade-1', update: jest.fn().mockResolvedValue(true) }, true])
        .mockResolvedValueOnce([{ id: 'grade-2', update: jest.fn().mockResolvedValue(true) }, false]);

      await bulkGradeSubmissions(mockRequest, mockResponse, mockNext);

      expect(Submission.findByPk).toHaveBeenCalledWith('submission-1', expect.any(Object));
      expect(Submission.findByPk).toHaveBeenCalledWith('submission-2', expect.any(Object));
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Bulk grading completed successfully',
        data: expect.objectContaining({
          success: expect.any(Array),
          errors: expect.any(Array)
        })
      });
    });

    test('should return 400 when no submissions provided', async () => {
      mockRequest.body = { submissions: [] };

      await bulkGradeSubmissions(mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Validation Error',
        message: 'Submissions array is required and must not be empty'
      });
    });
  });
});
