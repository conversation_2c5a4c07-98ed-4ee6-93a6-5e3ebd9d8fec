import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../src/services/jupyterhubAdmin.service.js');
jest.mock('../../../src/services/s3Workspace.service.js');
jest.mock('../../../src/models/ProjectSandboxSettings.js');
jest.mock('../../../src/models/UserWorkspace.js');
jest.mock('../../../src/models/Session.js');
jest.mock('../../../src/models/GroupMember.js');
jest.mock('../../../src/config/logger.config.js');

import JupyterHubAdminService from '../../../src/services/jupyterhubAdmin.service.js';
import S3WorkspaceService from '../../../src/services/s3Workspace.service.js';
import ProjectSandboxSettings from '../../../src/models/ProjectSandboxSettings.js';
import UserWorkspace from '../../../src/models/UserWorkspace.js';
import Session from '../../../src/models/Session.js';
import GroupMember from '../../../src/models/GroupMember.js';
import logger from '../../../src/config/logger.config.js';

// Import the service
import SandboxOrchestrator from '../../../src/services/sandboxOrchestrator.service.js';

describe('Sandbox Orchestrator Service', () => {
  let sandboxOrchestrator;
  let mockJhubService;
  let mockS3Service;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Mock JupyterHub Admin Service
    mockJhubService = {
      buildUserOptions: jest.fn(),
      createServer: jest.fn(),
      generateSessionUrl: jest.fn(),
      deleteServer: jest.fn()
    };
    JupyterHubAdminService.mockImplementation(() => mockJhubService);

    // Mock S3 Workspace Service
    mockS3Service = {
      validateWorkspace: jest.fn(),
      getWorkspaceFiles: jest.fn()
    };
    S3WorkspaceService.mockImplementation(() => mockS3Service);

    // Create service instance
    sandboxOrchestrator = new SandboxOrchestrator();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor and service initialization', () => {
    test('should initialize with lazy loading of services', () => {
      expect(sandboxOrchestrator._jhubService).toBeNull();
      expect(sandboxOrchestrator._s3Service).toBeNull();
    });

    test('should lazy load JupyterHub service', () => {
      const jhubService = sandboxOrchestrator.jhubService;
      
      expect(jhubService).toBeDefined();
      expect(JupyterHubAdminService).toHaveBeenCalled();
      expect(sandboxOrchestrator._jhubService).toBe(jhubService);
    });

    test('should lazy load S3 service', () => {
      const s3Service = sandboxOrchestrator.s3Service;
      
      expect(s3Service).toBeDefined();
      expect(S3WorkspaceService).toHaveBeenCalled();
      expect(sandboxOrchestrator._s3Service).toBe(s3Service);
    });

    test('should reuse service instances', () => {
      const jhubService1 = sandboxOrchestrator.jhubService;
      const jhubService2 = sandboxOrchestrator.jhubService;
      
      expect(jhubService1).toBe(jhubService2);
      expect(JupyterHubAdminService).toHaveBeenCalledTimes(1);
    });
  });

  describe('launchSession', () => {
    test('should launch session successfully', async () => {
      const userId = 'user-123';
      const projectId = 'project-456';
      const launchOptions = {
        mode: 'individual',
        groupId: 'group-789'
      };

      const mockSandboxSettings = {
        id: projectId,
        mode: 'individual',
        image_url: 'test-image:latest',
        cpu_limit: '1',
        memory_limit: '1Gi'
      };

      const mockGroupMembership = {
        id: 'membership-123',
        group_id: 'group-789',
        student_id: userId
      };

      const mockSession = {
        id: 'session-123',
        update: jest.fn().mockResolvedValue(true)
      };

      const mockHubResponse = {
        data: {
          spawner_ref: 'spawner-123'
        }
      };

      const mockSessionUrl = 'https://jupyterhub.example.com/user/user-123';

      // Mock service calls
      ProjectSandboxSettings.findByPk = jest.fn().mockResolvedValue(mockSandboxSettings);
      GroupMember.findOne = jest.fn().mockResolvedValue(mockGroupMembership);
      Session.create = jest.fn().mockResolvedValue(mockSession);
      mockJhubService.buildUserOptions = jest.fn().mockReturnValue({ image: 'test-image:latest' });
      mockJhubService.createServer = jest.fn().mockResolvedValue(mockHubResponse);
      mockJhubService.generateSessionUrl = jest.fn().mockReturnValue(mockSessionUrl);

      const result = await sandboxOrchestrator.launchSession(userId, projectId, launchOptions);

      expect(ProjectSandboxSettings.findByPk).toHaveBeenCalledWith(projectId);
      expect(GroupMember.findOne).toHaveBeenCalledWith({
        where: { group_id: 'group-789', student_id: userId }
      });
      expect(Session.create).toHaveBeenCalledWith({
        user_id: userId,
        project_id: projectId,
        mode: 'individual',
        status: 'starting'
      });
      expect(mockJhubService.buildUserOptions).toHaveBeenCalledWith(mockSandboxSettings, {
        mode: 'individual',
        groupId: 'group-789',
        projectId,
        s3WsPrefix: expect.stringContaining('users/user-123/projects/project-456'),
        s3RoPrefix: expect.stringContaining('groups/group-789/projects/project-456')
      });
      expect(mockJhubService.createServer).toHaveBeenCalledWith(userId, { image: 'test-image:latest' });
      expect(mockSession.update).toHaveBeenCalledWith({
        spawner_ref: 'spawner-123',
        status: 'running'
      });
      expect(mockJhubService.generateSessionUrl).toHaveBeenCalledWith(userId);

      expect(result).toEqual({
        success: true,
        sessionId: 'session-123',
        sessionUrl: mockSessionUrl,
        status: 'running'
      });

      expect(logger.info).toHaveBeenCalledWith(`Launching session for user ${userId}, project ${projectId}`);
      expect(logger.info).toHaveBeenCalledWith(`Session launched successfully for user ${userId}, project ${projectId}`);
    });

    test('should launch session without group', async () => {
      const userId = 'user-123';
      const projectId = 'project-456';
      const launchOptions = { mode: 'individual' };

      const mockSandboxSettings = {
        id: projectId,
        mode: 'individual',
        image_url: 'test-image:latest'
      };

      const mockSession = {
        id: 'session-123',
        update: jest.fn().mockResolvedValue(true)
      };

      const mockHubResponse = {
        data: {
          spawner_ref: 'spawner-123'
        }
      };

      ProjectSandboxSettings.findByPk = jest.fn().mockResolvedValue(mockSandboxSettings);
      Session.create = jest.fn().mockResolvedValue(mockSession);
      mockJhubService.buildUserOptions = jest.fn().mockReturnValue({ image: 'test-image:latest' });
      mockJhubService.createServer = jest.fn().mockResolvedValue(mockHubResponse);
      mockJhubService.generateSessionUrl = jest.fn().mockReturnValue('https://jupyterhub.example.com/user/user-123');

      const result = await sandboxOrchestrator.launchSession(userId, projectId, launchOptions);

      expect(GroupMember.findOne).not.toHaveBeenCalled();
      expect(mockJhubService.buildUserOptions).toHaveBeenCalledWith(mockSandboxSettings, {
        mode: 'individual',
        projectId,
        s3WsPrefix: expect.stringContaining('users/user-123/projects/project-456'),
        s3RoPrefix: expect.stringContaining('templates/project-456')
      });

      expect(result.success).toBe(true);
    });

    test('should handle sandbox settings not found', async () => {
      const userId = 'user-123';
      const projectId = 'project-456';

      ProjectSandboxSettings.findByPk = jest.fn().mockResolvedValue(null);

      await expect(sandboxOrchestrator.launchSession(userId, projectId)).rejects.toThrow('Project sandbox settings not found');
    });

    test('should handle group membership validation failure', async () => {
      const userId = 'user-123';
      const projectId = 'project-456';
      const launchOptions = { groupId: 'group-789' };

      const mockSandboxSettings = {
        id: projectId,
        mode: 'individual'
      };

      ProjectSandboxSettings.findByPk = jest.fn().mockResolvedValue(mockSandboxSettings);
      GroupMember.findOne = jest.fn().mockResolvedValue(null);

      await expect(sandboxOrchestrator.launchSession(userId, projectId, launchOptions)).rejects.toThrow('User is not a member of the specified group');
    });

    test('should handle launch error', async () => {
      const userId = 'user-123';
      const projectId = 'project-456';
      const mockError = new Error('Launch failed');

      ProjectSandboxSettings.findByPk = jest.fn().mockRejectedValue(mockError);

      await expect(sandboxOrchestrator.launchSession(userId, projectId)).rejects.toThrow('Launch failed');
      expect(logger.error).toHaveBeenCalledWith(`Failed to launch session for user ${userId}, project ${projectId}:`, mockError);
    });
  });

  describe('stopSession', () => {
    test('should stop session successfully', async () => {
      const userId = 'user-123';
      const sessionId = 'session-456';

      const mockSession = {
        id: sessionId,
        status: 'running',
        update: jest.fn().mockResolvedValue(true)
      };

      Session.findOne = jest.fn().mockResolvedValue(mockSession);
      mockJhubService.deleteServer = jest.fn().mockResolvedValue({});

      const result = await sandboxOrchestrator.stopSession(userId, sessionId);

      expect(Session.findOne).toHaveBeenCalledWith({
        where: { id: sessionId, user_id: userId }
      });
      expect(mockJhubService.deleteServer).toHaveBeenCalledWith(userId);
      expect(mockSession.update).toHaveBeenCalledWith({
        status: 'stopped',
        stopped_at: expect.any(Date)
      });

      expect(result).toEqual({
        success: true,
        sessionId: sessionId,
        status: 'stopped'
      });

      expect(logger.info).toHaveBeenCalledWith(`Stopping session ${sessionId} for user ${userId}`);
      expect(logger.info).toHaveBeenCalledWith(`Session ${sessionId} stopped successfully`);
    });

    test('should handle session already stopped', async () => {
      const userId = 'user-123';
      const sessionId = 'session-456';

      const mockSession = {
        id: sessionId,
        status: 'stopped'
      };

      Session.findOne = jest.fn().mockResolvedValue(mockSession);

      const result = await sandboxOrchestrator.stopSession(userId, sessionId);

      expect(mockJhubService.deleteServer).not.toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        message: 'Session already stopped'
      });
    });

    test('should handle session not found', async () => {
      const userId = 'user-123';
      const sessionId = 'session-456';

      Session.findOne = jest.fn().mockResolvedValue(null);

      await expect(sandboxOrchestrator.stopSession(userId, sessionId)).rejects.toThrow('Session not found');
    });

    test('should handle stop error', async () => {
      const userId = 'user-123';
      const sessionId = 'session-456';
      const mockError = new Error('Stop failed');

      const mockSession = {
        id: sessionId,
        status: 'running',
        update: jest.fn().mockResolvedValue(true)
      };

      Session.findOne = jest.fn().mockResolvedValue(mockSession);
      mockJhubService.deleteServer = jest.fn().mockRejectedValue(mockError);

      await expect(sandboxOrchestrator.stopSession(userId, sessionId)).rejects.toThrow('Stop failed');
      expect(logger.error).toHaveBeenCalledWith(`Failed to stop session ${sessionId}:`, mockError);
    });
  });

  describe('getUserSessions', () => {
    test('should get user sessions for regular user', async () => {
      const userId = 'user-123';
      const projectId = 'project-456';
      const isInstructor = false;

      const mockSessions = [
        { id: 'session-1', status: 'running' },
        { id: 'session-2', status: 'stopped' }
      ];

      Session.findAll = jest.fn().mockResolvedValue(mockSessions);

      const result = await sandboxOrchestrator.getUserSessions(userId, projectId, isInstructor);

      expect(Session.findAll).toHaveBeenCalledWith({
        where: { project_id: projectId, user_id: userId },
        order: [['started_at', 'DESC']]
      });
      expect(result).toEqual(mockSessions);
    });

    test('should get all sessions for instructor', async () => {
      const userId = 'user-123';
      const projectId = 'project-456';
      const isInstructor = true;

      const mockSessions = [
        { id: 'session-1', user_id: 'user-123', status: 'running' },
        { id: 'session-2', user_id: 'user-456', status: 'stopped' }
      ];

      Session.findAll = jest.fn().mockResolvedValue(mockSessions);

      const result = await sandboxOrchestrator.getUserSessions(userId, projectId, isInstructor);

      expect(Session.findAll).toHaveBeenCalledWith({
        where: { project_id: projectId },
        order: [['started_at', 'DESC']]
      });
      expect(result).toEqual(mockSessions);
    });

    test('should handle get sessions error', async () => {
      const userId = 'user-123';
      const projectId = 'project-456';
      const mockError = new Error('Database error');

      Session.findAll = jest.fn().mockRejectedValue(mockError);

      await expect(sandboxOrchestrator.getUserSessions(userId, projectId)).rejects.toThrow('Database error');
      expect(logger.error).toHaveBeenCalledWith(`Failed to get sessions for project ${projectId}:`, mockError);
    });
  });

  describe('validateWorkspaceReadiness', () => {
    test('should validate workspace successfully', async () => {
      const userId = 'user-123';
      const projectId = 'project-456';

      const mockWorkspace = {
        id: 'workspace-123',
        student_id: userId,
        project_id: projectId,
        is_ready: true
      };

      UserWorkspace.findOne = jest.fn().mockResolvedValue(mockWorkspace);

      const result = await sandboxOrchestrator.validateWorkspaceReadiness(userId, projectId);

      expect(UserWorkspace.findOne).toHaveBeenCalledWith({
        where: { student_id: userId, project_id: projectId }
      });
      expect(result).toEqual(mockWorkspace);
    });

    test('should throw error when workspace not found', async () => {
      const userId = 'user-123';
      const projectId = 'project-456';

      UserWorkspace.findOne = jest.fn().mockResolvedValue(null);

      await expect(sandboxOrchestrator.validateWorkspaceReadiness(userId, projectId)).rejects.toThrow('Workspace not found. Please fork the project first.');
    });

    test('should throw error when workspace not ready', async () => {
      const userId = 'user-123';
      const projectId = 'project-456';

      const mockWorkspace = {
        id: 'workspace-123',
        student_id: userId,
        project_id: projectId,
        is_ready: false
      };

      UserWorkspace.findOne = jest.fn().mockResolvedValue(mockWorkspace);

      await expect(sandboxOrchestrator.validateWorkspaceReadiness(userId, projectId)).rejects.toThrow('Workspace not ready. Please push your changes and mark as ready.');
    });
  });

  describe('getProjectSandboxSettings', () => {
    test('should get project sandbox settings successfully', async () => {
      const projectId = 'project-456';

      const mockSettings = {
        id: projectId,
        mode: 'individual',
        image_url: 'test-image:latest',
        cpu_limit: '1',
        memory_limit: '1Gi'
      };

      ProjectSandboxSettings.findByPk = jest.fn().mockResolvedValue(mockSettings);

      const result = await sandboxOrchestrator.getProjectSandboxSettings(projectId);

      expect(ProjectSandboxSettings.findByPk).toHaveBeenCalledWith(projectId);
      expect(result).toEqual(mockSettings);
    });

    test('should throw error when settings not found', async () => {
      const projectId = 'project-456';

      ProjectSandboxSettings.findByPk = jest.fn().mockResolvedValue(null);

      await expect(sandboxOrchestrator.getProjectSandboxSettings(projectId)).rejects.toThrow('Project sandbox settings not found');
    });
  });

  describe('validateGroupMembership', () => {
    test('should validate group membership successfully', async () => {
      const userId = 'user-123';
      const groupId = 'group-789';

      const mockMembership = {
        id: 'membership-123',
        group_id: groupId,
        student_id: userId
      };

      GroupMember.findOne = jest.fn().mockResolvedValue(mockMembership);

      const result = await sandboxOrchestrator.validateGroupMembership(userId, groupId);

      expect(GroupMember.findOne).toHaveBeenCalledWith({
        where: { group_id: groupId, student_id: userId }
      });
      expect(result).toEqual(mockMembership);
    });

    test('should throw error when user not in group', async () => {
      const userId = 'user-123';
      const groupId = 'group-789';

      GroupMember.findOne = jest.fn().mockResolvedValue(null);

      await expect(sandboxOrchestrator.validateGroupMembership(userId, groupId)).rejects.toThrow('User is not a member of the specified group');
    });
  });

  describe('computeS3Prefixes', () => {
    test('should compute S3 prefixes with group', () => {
      const userId = 'user-123';
      const projectId = 'project-456';
      const groupId = 'group-789';

      const result = sandboxOrchestrator.computeS3Prefixes(userId, projectId, groupId);

      expect(result.workspace).toContain(`users/${userId}/projects/${projectId}`);
      expect(result.readonly).toContain(`groups/${groupId}/projects/${projectId}`);
    });

    test('should compute S3 prefixes without group', () => {
      const userId = 'user-123';
      const projectId = 'project-456';

      const result = sandboxOrchestrator.computeS3Prefixes(userId, projectId);

      expect(result.workspace).toContain(`users/${userId}/projects/${projectId}`);
      expect(result.readonly).toContain(`templates/${projectId}`);
    });

    test('should use custom bucket from environment', () => {
      const originalBucket = process.env.AWS_S3_BUCKET;
      process.env.AWS_S3_BUCKET = 'custom-bucket';

      const userId = 'user-123';
      const projectId = 'project-456';

      const result = sandboxOrchestrator.computeS3Prefixes(userId, projectId);

      expect(result.workspace).toContain('custom-bucket');
      expect(result.readonly).toContain('custom-bucket');

      process.env.AWS_S3_BUCKET = originalBucket;
    });
  });

  describe('updateSessionMetrics', () => {
    test('should update session metrics successfully', async () => {
      const sessionId = 'session-123';
      const metrics = {
        cpu_usage: 50,
        memory_usage: 75,
        last_activity: new Date()
      };

      Session.update = jest.fn().mockResolvedValue([1]);

      await sandboxOrchestrator.updateSessionMetrics(sessionId, metrics);

      expect(Session.update).toHaveBeenCalledWith(metrics, {
        where: { id: sessionId }
      });
    });

    test('should handle update metrics error', async () => {
      const sessionId = 'session-123';
      const metrics = { cpu_usage: 50 };
      const mockError = new Error('Update failed');

      Session.update = jest.fn().mockRejectedValue(mockError);

      await sandboxOrchestrator.updateSessionMetrics(sessionId, metrics);

      expect(logger.error).toHaveBeenCalledWith(`Failed to update session metrics for ${sessionId}:`, mockError);
    });
  });

  describe('cleanupExpiredSessions', () => {
    test('should cleanup expired sessions successfully', async () => {
      const mockExpiredSessions = [
        { id: 'session-1', user_id: 'user-123' },
        { id: 'session-2', user_id: 'user-456' }
      ];

      Session.findAll = jest.fn().mockResolvedValue(mockExpiredSessions);
      
      // Mock stopSession to succeed for first session, fail for second
      jest.spyOn(sandboxOrchestrator, 'stopSession')
        .mockResolvedValueOnce({ success: true })
        .mockRejectedValueOnce(new Error('Stop failed'));

      const result = await sandboxOrchestrator.cleanupExpiredSessions();

      expect(Session.findAll).toHaveBeenCalledWith({
        where: {
          status: ['starting', 'running'],
          started_at: expect.any(Object)
        }
      });
      expect(sandboxOrchestrator.stopSession).toHaveBeenCalledTimes(2);
      expect(result).toBe(1); // Only one session cleaned up successfully
      expect(logger.info).toHaveBeenCalledWith('Cleaned up 1 expired sessions');
    });

    test('should handle cleanup error', async () => {
      const mockError = new Error('Cleanup failed');

      Session.findAll = jest.fn().mockRejectedValue(mockError);

      const result = await sandboxOrchestrator.cleanupExpiredSessions();

      expect(result).toBe(0);
      expect(logger.error).toHaveBeenCalledWith('Failed to cleanup expired sessions:', mockError);
    });

    test('should use custom max session hours from environment', async () => {
      const originalMaxHours = process.env.SANDBOX_MAX_SESSION_HOURS;
      process.env.SANDBOX_MAX_SESSION_HOURS = '8';

      Session.findAll = jest.fn().mockResolvedValue([]);

      await sandboxOrchestrator.cleanupExpiredSessions();

      expect(Session.findAll).toHaveBeenCalledWith({
        where: {
          status: ['starting', 'running'],
          started_at: expect.any(Object)
        }
      });

      process.env.SANDBOX_MAX_SESSION_HOURS = originalMaxHours;
    });
  });
});
