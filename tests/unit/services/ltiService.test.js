import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('jsonwebtoken');
jest.mock('node-jose');
jest.mock('axios');
jest.mock('../../../src/config/logger.config.js');

import jwt from 'jsonwebtoken';
import jose from 'node-jose';
import axios from 'axios';

// Import the service functions
import {
  generateClientAssertion,
  getAccessToken,
  createLineItem,
  getLineItems,
  createScore,
  getScores,
  validateLtiToken,
  parseLtiRequest
} from '../../../src/services/ltiService.js';

describe('LTI Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateClientAssertion', () => {
    test('should generate client assertion successfully', async () => {
      const mockKey = {
        toPEM: jest.fn().mockReturnValue('mock-private-key')
      };
      
      const mockJWT = 'mock-jwt-token';
      
      jose.JWK.asKey = jest.fn().mockResolvedValue(mockKey);
      jwt.sign = jest.fn().mockReturnValue(mockJWT);

      const result = await generateClientAssertion({
        clientId: 'test-client-id',
        tokenUrl: 'https://test.com/token',
        privateKey: 'mock-private-key'
      });

      expect(jose.JWK.asKey).toHaveBeenCalledWith('mock-private-key', 'pem');
      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          iss: 'test-client-id',
          sub: 'test-client-id',
          aud: 'https://test.com/token',
          iat: expect.any(Number),
          exp: expect.any(Number),
          jti: expect.any(String)
        }),
        'mock-private-key',
        expect.objectContaining({
          algorithm: 'RS256',
          keyid: expect.any(String)
        })
      );
      expect(result).toBe(mockJWT);
    });

    test('should handle key generation errors', async () => {
      jose.JWK.asKey = jest.fn().mockRejectedValue(new Error('Key error'));

      await expect(generateClientAssertion({
        clientId: 'test-client-id',
        tokenUrl: 'https://test.com/token',
        privateKey: 'invalid-key'
      })).rejects.toThrow('Key error');
    });
  });

  describe('getAccessToken', () => {
    test('should get access token successfully', async () => {
      const mockAssertion = 'mock-assertion';
      const mockTokenResponse = {
        data: {
          access_token: 'mock-access-token',
          token_type: 'Bearer',
          expires_in: 3600
        }
      };

      axios.post = jest.fn().mockResolvedValue(mockTokenResponse);

      const result = await getAccessToken({
        tokenUrl: 'https://test.com/token',
        clientId: 'test-client-id',
        clientAssertion: mockAssertion
      });

      expect(axios.post).toHaveBeenCalledWith(
        'https://test.com/token',
        expect.objectContaining({
          grant_type: 'client_credentials',
          client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
          client_assertion: mockAssertion,
          scope: 'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem https://purl.imsglobal.org/spec/lti-ags/scope/score'
        }),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/x-www-form-urlencoded'
          })
        })
      );
      expect(result).toEqual(mockTokenResponse.data);
    });

    test('should handle token request errors', async () => {
      const mockError = new Error('Token request failed');
      axios.post = jest.fn().mockRejectedValue(mockError);

      await expect(getAccessToken({
        tokenUrl: 'https://test.com/token',
        clientId: 'test-client-id',
        clientAssertion: 'mock-assertion'
      })).rejects.toThrow('Token request failed');
    });
  });

  describe('createLineItem', () => {
    test('should create line item successfully', async () => {
      const mockLineItem = {
        id: 'line-item-1',
        label: 'Test Assignment',
        scoreMaximum: 100
      };

      const mockResponse = {
        data: mockLineItem
      };

      axios.post = jest.fn().mockResolvedValue(mockResponse);

      const result = await createLineItem({
        lineItemUrl: 'https://test.com/lineitems',
        accessToken: 'mock-token',
        lineItem: {
          label: 'Test Assignment',
          scoreMaximum: 100,
          resourceId: 'assignment-1'
        }
      });

      expect(axios.post).toHaveBeenCalledWith(
        'https://test.com/lineitems',
        expect.objectContaining({
          label: 'Test Assignment',
          scoreMaximum: 100,
          resourceId: 'assignment-1'
        }),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token',
            'Content-Type': 'application/vnd.ims.lis.v2.lineitem+json'
          })
        })
      );
      expect(result).toEqual(mockLineItem);
    });

    test('should handle line item creation errors', async () => {
      const mockError = new Error('Line item creation failed');
      axios.post = jest.fn().mockRejectedValue(mockError);

      await expect(createLineItem({
        lineItemUrl: 'https://test.com/lineitems',
        accessToken: 'mock-token',
        lineItem: { label: 'Test', scoreMaximum: 100 }
      })).rejects.toThrow('Line item creation failed');
    });
  });

  describe('getLineItems', () => {
    test('should get line items successfully', async () => {
      const mockLineItems = [
        { id: '1', label: 'Assignment 1', scoreMaximum: 100 },
        { id: '2', label: 'Assignment 2', scoreMaximum: 50 }
      ];

      const mockResponse = {
        data: mockLineItems
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      const result = await getLineItems({
        lineItemUrl: 'https://test.com/lineitems',
        accessToken: 'mock-token'
      });

      expect(axios.get).toHaveBeenCalledWith(
        'https://test.com/lineitems',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token'
          })
        })
      );
      expect(result).toEqual(mockLineItems);
    });

    test('should handle line items retrieval errors', async () => {
      const mockError = new Error('Line items retrieval failed');
      axios.get = jest.fn().mockRejectedValue(mockError);

      await expect(getLineItems({
        lineItemUrl: 'https://test.com/lineitems',
        accessToken: 'mock-token'
      })).rejects.toThrow('Line items retrieval failed');
    });
  });

  describe('createScore', () => {
    test('should create score successfully', async () => {
      const mockScore = {
        userId: 'user-123',
        scoreGiven: 85,
        scoreMaximum: 100,
        comment: 'Great work!'
      };

      const mockResponse = {
        data: mockScore
      };

      axios.post = jest.fn().mockResolvedValue(mockResponse);

      const result = await createScore({
        scoreUrl: 'https://test.com/scores',
        accessToken: 'mock-token',
        score: mockScore
      });

      expect(axios.post).toHaveBeenCalledWith(
        'https://test.com/scores',
        expect.objectContaining(mockScore),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token',
            'Content-Type': 'application/vnd.ims.lis.v1.score+json'
          })
        })
      );
      expect(result).toEqual(mockScore);
    });

    test('should handle score creation errors', async () => {
      const mockError = new Error('Score creation failed');
      axios.post = jest.fn().mockRejectedValue(mockError);

      await expect(createScore({
        scoreUrl: 'https://test.com/scores',
        accessToken: 'mock-token',
        score: { userId: 'user-123', scoreGiven: 85, scoreMaximum: 100 }
      })).rejects.toThrow('Score creation failed');
    });
  });

  describe('getScores', () => {
    test('should get scores successfully', async () => {
      const mockScores = [
        { userId: 'user-1', scoreGiven: 85, scoreMaximum: 100 },
        { userId: 'user-2', scoreGiven: 92, scoreMaximum: 100 }
      ];

      const mockResponse = {
        data: mockScores
      };

      axios.get = jest.fn().mockResolvedValue(mockResponse);

      const result = await getScores({
        scoreUrl: 'https://test.com/scores',
        accessToken: 'mock-token'
      });

      expect(axios.get).toHaveBeenCalledWith(
        'https://test.com/scores',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token'
          })
        })
      );
      expect(result).toEqual(mockScores);
    });

    test('should handle scores retrieval errors', async () => {
      const mockError = new Error('Scores retrieval failed');
      axios.get = jest.fn().mockRejectedValue(mockError);

      await expect(getScores({
        scoreUrl: 'https://test.com/scores',
        accessToken: 'mock-token'
      })).rejects.toThrow('Scores retrieval failed');
    });
  });

  describe('validateLtiToken', () => {
    test('should validate LTI token successfully', async () => {
      const mockToken = 'mock-jwt-token';
      const mockDecodedToken = {
        iss: 'https://test-platform.com',
        aud: 'test-client-id',
        exp: Math.floor(Date.now() / 1000) + 3600,
        iat: Math.floor(Date.now() / 1000),
        sub: 'user-123',
        'https://purl.imsglobal.org/spec/lti/claim/message_type': 'LtiResourceLinkRequest'
      };

      jwt.verify = jest.fn().mockReturnValue(mockDecodedToken);

      const result = await validateLtiToken(mockToken, 'test-client-id');

      expect(jwt.verify).toHaveBeenCalledWith(
        mockToken,
        expect.any(String),
        expect.objectContaining({
          algorithms: ['RS256'],
          issuer: expect.any(String),
          audience: 'test-client-id'
        })
      );
      expect(result).toEqual(mockDecodedToken);
    });

    test('should handle invalid token', async () => {
      const mockToken = 'invalid-token';
      
      jwt.verify = jest.fn().mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await expect(validateLtiToken(mockToken, 'test-client-id')).rejects.toThrow('Invalid token');
    });

    test('should handle expired token', async () => {
      const mockToken = 'expired-token';
      const mockDecodedToken = {
        exp: Math.floor(Date.now() / 1000) - 3600 // Expired
      };

      jwt.verify = jest.fn().mockReturnValue(mockDecodedToken);

      await expect(validateLtiToken(mockToken, 'test-client-id')).rejects.toThrow('Token expired');
    });
  });

  describe('parseLtiRequest', () => {
    test('should parse LTI request successfully', () => {
      const mockRequest = {
        body: {
          'https://purl.imsglobal.org/spec/lti/claim/message_type': 'LtiResourceLinkRequest',
          'https://purl.imsglobal.org/spec/lti/claim/version': '1.3.0',
          'https://purl.imsglobal.org/spec/lti/claim/resource_link': {
            id: 'resource-123'
          },
          'https://purl.imsglobal.org/spec/lti/claim/context': {
            id: 'context-123',
            label: 'Test Course',
            title: 'Data Science 101'
          },
          'https://purl.imsglobal.org/spec/lti/claim/launch_presentation': {
            return_url: 'https://test.com/return'
          },
          'https://purl.imsglobal.org/spec/lti/claim/roles': [
            'http://purl.imsglobal.org/vocab/lis/v2/membership#Learner'
          ],
          sub: 'user-123',
          name: 'John Doe',
          email: '<EMAIL>'
        }
      };

      const result = parseLtiRequest(mockRequest);

      expect(result).toEqual({
        messageType: 'LtiResourceLinkRequest',
        version: '1.3.0',
        resourceLinkId: 'resource-123',
        contextId: 'context-123',
        contextLabel: 'Test Course',
        contextTitle: 'Data Science 101',
        returnUrl: 'https://test.com/return',
        roles: ['http://purl.imsglobal.org/vocab/lis/v2/membership#Learner'],
        userId: 'user-123',
        userName: 'John Doe',
        userEmail: '<EMAIL>'
      });
    });

    test('should handle missing required fields', () => {
      const mockRequest = {
        body: {
          sub: 'user-123'
        }
      };

      const result = parseLtiRequest(mockRequest);

      expect(result).toEqual({
        messageType: undefined,
        version: undefined,
        resourceLinkId: undefined,
        contextId: undefined,
        contextLabel: undefined,
        contextTitle: undefined,
        returnUrl: undefined,
        roles: undefined,
        userId: 'user-123',
        userName: undefined,
        userEmail: undefined
      });
    });

    test('should handle empty request body', () => {
      const mockRequest = {
        body: {}
      };

      const result = parseLtiRequest(mockRequest);

      expect(result).toEqual({
        messageType: undefined,
        version: undefined,
        resourceLinkId: undefined,
        contextId: undefined,
        contextLabel: undefined,
        contextTitle: undefined,
        returnUrl: undefined,
        roles: undefined,
        userId: undefined,
        userName: undefined,
        userEmail: undefined
      });
    });
  });
});
