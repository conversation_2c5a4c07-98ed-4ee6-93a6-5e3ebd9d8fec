#!/bin/bash

# Build script for BITS DataScience Platform User Environment
# This script builds the custom Jupyter user environment Docker image

set -e

echo "Building BITS DataScience Platform User Environment..."

# Configuration
IMAGE_NAME="bits-jupyter-user"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

# Build directory
BUILD_DIR="$(dirname "$0")/user-environment"

# Check if build directory exists
if [ ! -d "$BUILD_DIR" ]; then
    echo "Error: Build directory not found: $BUILD_DIR"
    exit 1
fi

echo "Building Docker image: $FULL_IMAGE_NAME"
echo "Build context: $BUILD_DIR"

# Build the Docker image
docker build \
    -t "$FULL_IMAGE_NAME" \
    -f "$BUILD_DIR/Dockerfile" \
    "$BUILD_DIR"

echo "Successfully built $FULL_IMAGE_NAME"

# Verify the image
echo "Verifying the built image..."
docker images | grep "$IMAGE_NAME" || echo "Warning: Image not found in docker images list"

echo "Build completed successfully!"
echo ""
echo "To use this image with Ju<PERSON>terHub:"
echo "1. Make sure the image name in jupyterhub_config.py matches: $FULL_IMAGE_NAME"
echo "2. Start JupyterHub with docker-compose up"
echo ""
echo "Image details:"
docker inspect "$FULL_IMAGE_NAME" --format='{{.Config.Labels}}' 2>/dev/null || echo "Could not inspect image"
