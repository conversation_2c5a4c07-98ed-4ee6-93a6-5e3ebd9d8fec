"""
Custom JupyterHub Authenticator that integrates with BITS DataScience Platform Backend
"""
import os
import requests
import json
import asyncio
from datetime import datetime
from jupyterhub.auth import Authenticator
from jupyterhub.handlers import <PERSON><PERSON><PERSON><PERSON>
from tornado import gen, web
from traitlets import Unicode, <PERSON><PERSON>
from backend_integration import backend_service


class BackendAPIAuthenticator(Authenticator):
    """
    Custom authenticator that validates users against the BITS DataScience Platform backend
    """
    
    backend_api_url = Unicode(
        config=True,
        help="""
        The base URL of the backend API for authentication
        """
    )
    
    auto_login = Bool(
        True,
        config=True,
        help="""
        Automatically redirect to login URL
        """
    )
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.backend_api_url = os.environ.get('BACKEND_API_URL', 'http://backend:5000')
    
    @gen.coroutine
    def authenticate(self, handler, data):
        """
        Authenticate user against the backend API with enhanced integration
        """
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return None

        try:
            # Use backend integration service for authentication
            user_data = yield backend_service.authenticate_user(username, password)

            if user_data:
                user_info = user_data.get('user', {})
                jupyter_username = user_info.get('jupiterUserName') or str(user_info.get('id'))

                if jupyter_username:
                    # Get user's projects for environment setup
                    projects = yield backend_service.get_user_projects(user_info.get('id'))

                    # Set up user environment
                    yield backend_service.setup_user_environment(jupyter_username, projects)

                    # Notify backend of successful login
                    login_data = {
                        **user_data,
                        'login_time': datetime.utcnow().isoformat(),
                        'session_id': handler.request.headers.get('X-Session-Id', 'unknown')
                    }
                    yield backend_service.notify_backend_login(login_data)

                    # Return enhanced user model for JupyterHub
                    return {
                        'name': jupyter_username,
                        'auth_model': {
                            'user_id': user_info.get('id'),
                            'email': user_info.get('email'),
                            'role': user_info.get('role'),
                            'token': user_data.get('token'),
                            'projects': projects,
                            'login_time': login_data['login_time']
                        }
                    }

            self.log.warning(f"Authentication failed for user {username}")
            return None

        except Exception as e:
            self.log.error(f"Authentication error for user {username}: {str(e)}")
            return None
    
    def get_handlers(self, app):
        """
        Return additional handlers for custom authentication flow
        """
        return [
            (r'/login', CustomLoginHandler),
            (r'/logout', CustomLogoutHandler),
        ]


class CustomLoginHandler(BaseHandler):
    """
    Custom login handler for seamless authentication
    """
    
    @web.authenticated
    def get(self):
        """
        Handle GET request to login page
        """
        # If user is already authenticated, redirect to hub
        if self.current_user:
            self.redirect(self.hub.server.base_url)
            return
        
        # Render custom login page or redirect to backend login
        self.render_template('login.html', 
                           next_url=self.get_argument('next', ''),
                           message='')
    
    @gen.coroutine
    def post(self):
        """
        Handle POST request for login
        """
        username = self.get_body_argument('username', '')
        password = self.get_body_argument('password', '')
        
        if not username or not password:
            self.render_template('login.html',
                               next_url=self.get_argument('next', ''),
                               message='Username and password required')
            return
        
        # Authenticate user
        user = yield self.authenticator.authenticate(self, {
            'username': username,
            'password': password
        })
        
        if user:
            # Login successful
            self.set_login_cookie(user)
            next_url = self.get_argument('next', self.hub.server.base_url)
            self.redirect(next_url)
        else:
            # Login failed
            self.render_template('login.html',
                               next_url=self.get_argument('next', ''),
                               message='Invalid username or password')


class CustomLogoutHandler(BaseHandler):
    """
    Custom logout handler
    """
    
    def get(self):
        """
        Handle logout
        """
        self.clear_login_cookie()
        self.redirect(self.hub.server.base_url)


class TokenAuthenticator(Authenticator):
    """
    Token-based authenticator for API access
    """
    
    @gen.coroutine
    def authenticate(self, handler, data):
        """
        Authenticate using token from backend
        """
        token = data.get('token')
        
        if not token:
            return None
        
        try:
            # Validate token with backend
            backend_api_url = os.environ.get('BACKEND_API_URL', 'http://backend:5000')
            validate_url = f"{backend_api_url}/api/auth/validate-token"
            
            response = requests.post(
                validate_url,
                headers={
                    'Authorization': f'Bearer {token}',
                    'Content-Type': 'application/json'
                },
                timeout=30
            )
            
            if response.status_code == 200:
                user_data = response.json()
                user_info = user_data.get('user', {})
                jupyter_username = user_info.get('jupiterUserName') or user_info.get('id')
                
                if jupyter_username:
                    return {
                        'name': str(jupyter_username),
                        'auth_model': user_info
                    }
            
            return None
            
        except Exception as e:
            self.log.error(f"Token authentication error: {str(e)}")
            return None
