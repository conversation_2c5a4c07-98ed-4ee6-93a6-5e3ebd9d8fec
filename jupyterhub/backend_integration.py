"""
Backend Integration Service for JupyterHub
Handles seamless authentication and user management
"""

import os
import requests
import json
import asyncio
from typing import Dict, Optional, Any
import logging

logger = logging.getLogger(__name__)


class BackendIntegrationService:
    """
    Service to handle integration between JupyterHub and BITS DataScience Platform backend
    """
    
    def __init__(self):
        self.backend_url = os.environ.get('BACKEND_API_URL', 'http://backend:5000')
        self.admin_token = os.environ.get('JUPYTERHUB_ADMIN_TOKEN')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'JupyterHub-Backend-Integration/1.0'
        })
    
    async def authenticate_user(self, email: str, password: str) -> Optional[Dict[str, Any]]:
        """
        Authenticate user against the backend API
        
        Args:
            email: User's email address
            password: User's password
            
        Returns:
            User data if authentication successful, None otherwise
        """
        try:
            auth_url = f"{self.backend_url}/api/auth/login"
            auth_data = {
                'email': email,
                'password': password
            }
            
            response = self.session.post(auth_url, json=auth_data, timeout=30)
            
            if response.status_code == 200:
                user_data = response.json()
                logger.info(f"User {email} authenticated successfully")
                return user_data
            else:
                logger.warning(f"Authentication failed for {email}: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Authentication error for {email}: {str(e)}")
            return None
    
    async def validate_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Validate user token against the backend API
        
        Args:
            token: JWT token to validate
            
        Returns:
            User data if token is valid, None otherwise
        """
        try:
            validate_url = f"{self.backend_url}/api/auth/validate-token"
            headers = {'Authorization': f'Bearer {token}'}
            
            response = self.session.post(validate_url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                user_data = response.json()
                logger.info(f"Token validated for user {user_data.get('user', {}).get('email')}")
                return user_data
            else:
                logger.warning(f"Token validation failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
            return None
    
    async def get_user_projects(self, user_id: str) -> list:
        """
        Get user's projects from the backend API
        
        Args:
            user_id: User's ID
            
        Returns:
            List of user's projects
        """
        try:
            projects_url = f"{self.backend_url}/api/projects/user/{user_id}"
            headers = {'Authorization': f'Bearer {self.admin_token}'}
            
            response = self.session.get(projects_url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                projects_data = response.json()
                return projects_data.get('projects', [])
            else:
                logger.warning(f"Failed to get projects for user {user_id}: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting projects for user {user_id}: {str(e)}")
            return []
    
    async def create_jupyter_user(self, user_data: Dict[str, Any]) -> bool:
        """
        Create or update user in JupyterHub
        
        Args:
            user_data: User data from backend
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # This would typically call JupyterHub API to create/update user
            # For now, we'll just log the action
            user_info = user_data.get('user', {})
            jupyter_username = user_info.get('jupiterUserName') or user_info.get('id')
            
            logger.info(f"Creating/updating JupyterHub user: {jupyter_username}")
            
            # Here you would make API calls to JupyterHub to:
            # 1. Create user if not exists
            # 2. Update user permissions
            # 3. Set up user environment
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating JupyterHub user: {str(e)}")
            return False
    
    async def setup_user_environment(self, username: str, projects: list) -> bool:
        """
        Set up user's Jupyter environment with project directories
        
        Args:
            username: JupyterHub username
            projects: List of user's projects
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Setting up environment for user {username} with {len(projects)} projects")
            
            # Here you would:
            # 1. Create project-specific volumes
            # 2. Set up directory structure
            # 3. Configure environment variables
            # 4. Install required packages
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting up environment for {username}: {str(e)}")
            return False
    
    async def notify_backend_login(self, user_data: Dict[str, Any]) -> bool:
        """
        Notify backend about successful JupyterHub login
        
        Args:
            user_data: User data
            
        Returns:
            True if notification successful, False otherwise
        """
        try:
            notify_url = f"{self.backend_url}/api/jupyter/login-notification"
            headers = {'Authorization': f'Bearer {self.admin_token}'}
            
            notification_data = {
                'user_id': user_data.get('user', {}).get('id'),
                'jupyter_username': user_data.get('user', {}).get('jupiterUserName'),
                'login_time': user_data.get('login_time'),
                'session_id': user_data.get('session_id')
            }
            
            response = self.session.post(
                notify_url, 
                json=notification_data, 
                headers=headers, 
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"Backend notified of login for user {notification_data['user_id']}")
                return True
            else:
                logger.warning(f"Failed to notify backend of login: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error notifying backend of login: {str(e)}")
            return False
    
    def health_check(self) -> bool:
        """
        Check if backend API is accessible
        
        Returns:
            True if backend is healthy, False otherwise
        """
        try:
            health_url = f"{self.backend_url}/health"
            response = self.session.get(health_url, timeout=10)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Backend health check failed: {str(e)}")
            return False


# Global instance
backend_service = BackendIntegrationService()
