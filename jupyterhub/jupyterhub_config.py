"""
JupyterHub Configuration for BITS DataScience Platform
Multi-user Docker-based Jupyter environment with backend integration
"""

import os
import sys
from dockerspawner import DockerSpawner
from custom_authenticator import BackendAPIAuthenticator

# Add current directory to Python path
sys.path.insert(0, '/srv/jupyterhub')

# =============================================================================
# JupyterHub Configuration
# =============================================================================

# Basic JupyterHub settings
c.JupyterHub.ip = '0.0.0.0'
c.JupyterHub.port = 8000
c.JupyterHub.hub_ip = '0.0.0.0'
c.JupyterHub.hub_port = 8081

# Database configuration
c.JupyterHub.db_url = '********************************************/bits_platform'

# Admin configuration
c.JupyterHub.admin_access = True
c.JupyterHub.admin_users = {'admin'}

# API token for backend integration
c.JupyterHub.api_tokens = {
    os.environ.get('JUPYTERHUB_ADMIN_TOKEN', '607994eb70ddc4da5748e39da374e7875ead335b87e7e1ba15fc4fddb08febd6'): 'admin'
}

# =============================================================================
# Authentication Configuration
# =============================================================================

# Use custom authenticator
c.JupyterHub.authenticator_class = BackendAPIAuthenticator

# Authenticator settings
c.BackendAPIAuthenticator.backend_api_url = os.environ.get('BACKEND_API_URL', 'http://backend:5000')
c.BackendAPIAuthenticator.auto_login = True

# =============================================================================
# Spawner Configuration (Docker)
# =============================================================================

# Use DockerSpawner for isolated user environments
c.JupyterHub.spawner_class = DockerSpawner

# Docker configuration
c.DockerSpawner.image = 'bits-jupyter-user:latest'  # Custom user environment
c.DockerSpawner.remove = True
c.DockerSpawner.debug = True
c.DockerSpawner.pull_policy = 'ifnotpresent'

# Network configuration
c.DockerSpawner.network_name = 'bits_bits-network'
c.DockerSpawner.use_internal_ip = True
c.DockerSpawner.hub_ip_connect = 'jupyterhub'

# Container naming
c.DockerSpawner.name_template = 'jupyter-{username}'

# Resource limits
c.DockerSpawner.mem_limit = '2G'
c.DockerSpawner.cpu_limit = 1.0

# Volume mounts for user data
c.DockerSpawner.volumes = {
    'jupyterhub-user-{username}': '/home/<USER>/work',
    '/var/run/docker.sock': {'bind': '/var/run/docker.sock', 'mode': 'rw'}
}

# Environment variables for user containers
c.DockerSpawner.environment = {
    'JUPYTER_ENABLE_LAB': '1',
    'GRANT_SUDO': 'yes',
    'CHOWN_HOME': 'yes',
    'CHOWN_HOME_OPTS': '-R'
}

# =============================================================================
# User Management
# =============================================================================

# Allow named servers for project isolation
c.JupyterHub.allow_named_servers = True
c.JupyterHub.named_server_limit_per_user = 10

# User data persistence
c.Spawner.default_url = '/lab'

# =============================================================================
# Security Configuration
# =============================================================================

# SSL/TLS (disable for internal Docker network)
c.JupyterHub.ssl_cert = ''
c.JupyterHub.ssl_key = ''

# Cookie settings
c.JupyterHub.cookie_secret_file = '/srv/jupyterhub/cookie_secret'
c.JupyterHub.cookie_max_age_days = 1

# =============================================================================
# Logging Configuration
# =============================================================================

c.JupyterHub.log_level = 'INFO'
c.Application.log_level = 'INFO'

# =============================================================================
# Custom Hooks and Functions
# =============================================================================

def pre_spawn_hook(spawner):
    """
    Custom pre-spawn hook to set up user environment
    """
    username = spawner.user.name
    
    # Create project-specific directories
    project_volumes = {}
    
    # Add project volumes based on user's projects
    # This can be enhanced to fetch user's projects from backend API
    for i in range(1, 6):  # Allow up to 5 projects per user
        project_name = f'project_{i}'
        volume_name = f'jupyterhub-{username}-{project_name}'
        project_volumes[volume_name] = f'/home/<USER>/work/{project_name}'
    
    # Update spawner volumes
    spawner.volumes.update(project_volumes)
    
    # Set user-specific environment variables
    spawner.environment.update({
        'JUPYTER_USER': username,
        'USER_ID': str(spawner.user.id),
        'PROJECT_PREFIX': 'project_'
    })

# Apply pre-spawn hook
c.Spawner.pre_spawn_hook = pre_spawn_hook

# =============================================================================
# API Configuration
# =============================================================================

# Enable API access
c.JupyterHub.api_url = 'http://jupyterhub:8081/hub/api'

# =============================================================================
# Custom Services
# =============================================================================

# Health check service
c.JupyterHub.services = [
    {
        'name': 'health-check',
        'url': 'http://0.0.0.0:8000/hub/health',
        'command': ['python', '-c', '''
import tornado.web
import tornado.ioloop
from tornado.httpserver import HTTPServer

class HealthHandler(tornado.web.RequestHandler):
    def get(self):
        self.write({"status": "healthy", "service": "jupyterhub"})

app = tornado.web.Application([
    (r"/hub/health", HealthHandler),
])

if __name__ == "__main__":
    server = HTTPServer(app)
    server.listen(8000)
    tornado.ioloop.IOLoop.current().start()
''']
    }
]

# =============================================================================
# Cleanup Configuration
# =============================================================================

# Cull idle servers
c.JupyterHub.load_roles = [
    {
        'name': 'server-culler',
        'scopes': [
            'list:users',
            'read:users:activity',
            'read:servers',
            'delete:servers'
        ],
        'services': ['server-culler']
    }
]

# Add server culler service
c.JupyterHub.services.append({
    'name': 'server-culler',
    'command': [
        'python', '-m', 'jupyterhub_idle_culler',
        '--timeout=3600',  # Cull after 1 hour of inactivity
        '--cull-every=300',  # Check every 5 minutes
        '--concurrency=10'
    ]
})

print("JupyterHub configuration loaded successfully!")
print(f"Hub URL: http://0.0.0.0:8000")
print(f"Backend API URL: {os.environ.get('BACKEND_API_URL', 'http://backend:5000')}")
print(f"Database URL: ********************************************/bits_platform")
