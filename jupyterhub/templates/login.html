{% extends "page.html" %}

{% block title %}BITS DataScience Platform - Login{% endblock %}

{% block main %}
<div class="container">
  <div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
      <div class="card">
        <div class="card-header text-center">
          <h3>BITS DataScience Platform</h3>
          <p class="text-muted">Jupyter Environment</p>
        </div>
        <div class="card-body">
          {% if message %}
          <div class="alert alert-danger" role="alert">
            {{ message }}
          </div>
          {% endif %}
          
          <form action="{{ login_url }}" method="post" role="form">
            {{ xsrf_form_html() }}
            
            <div class="form-group">
              <label for="username" class="form-label">Email</label>
              <input 
                type="email" 
                class="form-control" 
                id="username" 
                name="username" 
                placeholder="Enter your email"
                required
                autofocus
              >
            </div>
            
            <div class="form-group">
              <label for="password" class="form-label">Password</label>
              <input 
                type="password" 
                class="form-control" 
                id="password" 
                name="password" 
                placeholder="Enter your password"
                required
              >
            </div>
            
            {% if next_url %}
            <input type="hidden" name="next" value="{{ next_url }}">
            {% endif %}
            
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary btn-lg">
                Sign In
              </button>
            </div>
          </form>
          
          <div class="text-center mt-3">
            <small class="text-muted">
              Access your personalized Jupyter environment
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.card {
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px 10px 0 0 !important;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 25px;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.form-control {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 12px 15px;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
}

body {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}
</style>
{% endblock %}
