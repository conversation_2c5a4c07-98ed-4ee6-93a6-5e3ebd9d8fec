# BITS DataScience Platform - JupyterHub Setup

This directory contains the Docker-based JupyterHub configuration for the BITS DataScience Platform, providing multi-user Jupyter environments with seamless backend integration.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   JupyterHub    │
│   (React)       │◄──►│   (Node.js)     │◄──►│   (Python)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   PostgreSQL    │    │  User Jupyter   │
                       │   Database      │    │  Containers     │
                       └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- At least 4GB RAM available
- Ports 8001, 5000, 5432 available

### Setup

1. **Run the setup script:**
   ```bash
   chmod +x scripts/setup-jupyterhub.sh
   ./scripts/setup-jupyterhub.sh
   ```

2. **Access JupyterHub:**
   - Open http://localhost:8001
   - Login with your BITS platform credentials
   - Start your Jupyter server

## 📁 Directory Structure

```
jupyterhub/
├── Dockerfile                 # JupyterHub container configuration
├── jupyterhub_config.py      # Main JupyterHub configuration
├── custom_authenticator.py   # Backend integration authenticator
├── backend_integration.py    # Backend API service
├── start-jupyterhub.sh      # JupyterHub startup script
├── build-user-image.sh      # User environment build script
├── templates/               # Custom HTML templates
│   └── login.html          # Custom login page
└── user-environment/        # Custom user environment
    ├── Dockerfile          # User container configuration
    ├── start-user-server.sh # User server startup script
    └── welcome.ipynb       # Welcome notebook
```

## ⚙️ Configuration

### Environment Variables

Key environment variables for JupyterHub integration:

```bash
# JupyterHub Configuration
JUPYTERHUB_URL=http://localhost:8001
JUPYTERHUB_ADMIN_TOKEN=607994eb70ddc4da5748e39da374e7875ead335b87e7e1ba15fc4fddb08febd6
JUPYTER_USER_SERVER_PREFIX=/user
JUPYTER_PROJECT_PREFIX=project_
JUPYTER_DEFAULT_KERNEL=python3

# Backend Integration
BACKEND_API_URL=http://backend:5000
POSTGRES_HOST=postgres
POSTGRES_DB=bits_platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
```

### JupyterHub Features

- **Multi-user Support**: Isolated environments for each user
- **Docker Spawner**: Containerized user servers
- **Custom Authentication**: Integration with BITS platform backend
- **Project Isolation**: Separate workspaces for different projects
- **Resource Management**: Memory and CPU limits per user
- **Auto-cleanup**: Idle server culling

## 🔧 User Environment

### Pre-installed Packages

The custom user environment includes:

**Python Libraries:**
- Data Science: pandas, numpy, scipy, scikit-learn
- Visualization: matplotlib, seaborn, plotly
- Machine Learning: tensorflow, pytorch, xgboost
- Web Frameworks: fastapi, streamlit, dash
- Database: sqlalchemy, psycopg2-binary
- Cloud: boto3, azure-storage-blob

**R Packages:**
- tidyverse, caret, randomForest, xgboost
- plotly, shiny, rmarkdown

**Jupyter Extensions:**
- JupyterLab Git integration
- Code formatting (black, isort)
- Language server protocol
- Widgets and interactive tools

### Directory Structure

Each user gets the following directory structure:

```
/home/<USER>/work/
├── projects/           # Project-specific workspaces
│   ├── project_1/     # Individual project directories
│   ├── project_2/
│   └── ...
├── shared/            # Shared resources
├── datasets/          # Personal datasets
├── models/            # Trained models
├── notebooks/         # General notebooks
└── scripts/           # Reusable scripts
```

## 🔐 Authentication Flow

1. User accesses JupyterHub at http://localhost:8001
2. Custom authenticator redirects to login page
3. User enters BITS platform credentials
4. Authenticator validates against backend API
5. On success, user is logged into JupyterHub
6. User can start their personal Jupyter server
7. Backend is notified of successful login

## 🐳 Docker Services

### JupyterHub Container

- **Image**: Custom built from `jupyterhub/jupyterhub:4.0`
- **Port**: 8001 (external) → 8000 (internal)
- **Volumes**: Docker socket, configuration files, data persistence
- **Network**: Connected to backend and database

### User Containers

- **Image**: Custom `bits-jupyter-user:latest`
- **Spawning**: On-demand via DockerSpawner
- **Isolation**: Separate container per user
- **Persistence**: Named volumes for user data
- **Resources**: 2GB RAM, 1 CPU limit per container

## 🔍 Monitoring and Logs

### View Logs

```bash
# JupyterHub logs
docker-compose logs -f jupyterhub

# User container logs
docker logs jupyter-{username}

# All services
docker-compose logs -f
```

### Health Checks

- JupyterHub: http://localhost:8001/hub/health
- Backend API: http://localhost:5000/health
- Database: `docker exec bits-postgres pg_isready`

## 🛠️ Management

### Admin Interface

Access the JupyterHub admin interface at:
http://localhost:8001/hub/admin

Admin capabilities:
- View all users and their servers
- Start/stop user servers
- Access user environments
- Monitor resource usage

### User Management

Users are automatically created when they first login through the backend authentication system.

### Server Management

```bash
# Stop all user servers
docker stop $(docker ps -q --filter "name=jupyter-")

# Remove idle containers
docker container prune -f

# Restart JupyterHub
docker-compose restart jupyterhub
```

## 🔧 Troubleshooting

### Common Issues

1. **JupyterHub won't start**
   - Check database connection
   - Verify backend API is running
   - Check Docker socket permissions

2. **User servers fail to spawn**
   - Verify Docker network configuration
   - Check user image availability
   - Review spawner logs

3. **Authentication failures**
   - Verify backend API connectivity
   - Check admin token configuration
   - Review authenticator logs

### Debug Commands

```bash
# Check container status
docker ps -a

# Inspect JupyterHub configuration
docker exec bits-jupyterhub cat /srv/jupyterhub/jupyterhub_config.py

# Test backend connectivity
curl http://localhost:5000/health

# Check database connection
docker exec bits-postgres psql -U postgres -d bits_platform -c "SELECT 1;"
```

## 📚 Additional Resources

- [JupyterHub Documentation](https://jupyterhub.readthedocs.io/)
- [DockerSpawner Documentation](https://jupyterhub-dockerspawner.readthedocs.io/)
- [Custom Authenticators Guide](https://jupyterhub.readthedocs.io/en/stable/reference/authenticators.html)

## 🤝 Support

For issues and questions:
1. Check the logs using the commands above
2. Review the troubleshooting section
3. Consult the JupyterHub documentation
4. Contact the platform administrators
