version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: bits-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: bits_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: '--auth-host=scram-sha-256'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - '5432:5432'
    networks:
      - bits-network
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 10s
      timeout: 5s
      retries: 3

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: bits-backend
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      NODE_ENV: production
      PORT: 5000
      DATABASE_URL: ********************************************/bits_platform
      JWT_SECRET: your-jwt-secret-change-in-production
      SESSION_SECRET: your-session-secret-change-in-production
      JUPYTERHUB_URL: http://jupyterhub:8000
      JUPYTERHUB_ADMIN_TOKEN: 607994eb70ddc4da5748e39da374e7875ead335b87e7e1ba15fc4fddb08febd6
      JUPYTER_USER_SERVER_PREFIX: /user
      JUPYTER_PROJECT_PREFIX: project_
      JUPYTER_DEFAULT_KERNEL: python3
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./temp:/app/temp
    ports:
      - '5000:5000'
    networks:
      - bits-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5000/health']
      interval: 20s
      timeout: 5s
      retries: 3

  # JupyterHub Service
  jupyterhub:
    build:
      context: ./jupyterhub
      dockerfile: Dockerfile
    container_name: bits-jupyterhub
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      JUPYTERHUB_ADMIN_TOKEN: 607994eb70ddc4da5748e39da374e7875ead335b87e7e1ba15fc4fddb08febd6
      BACKEND_API_URL: http://backend:5000
      POSTGRES_HOST: postgres
      POSTGRES_DB: bits_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:rw
      - jupyterhub_data:/srv/jupyterhub
      - ./jupyterhub/jupyterhub_config.py:/srv/jupyterhub/jupyterhub_config.py:ro
      - ./jupyterhub/custom_authenticator.py:/srv/jupyterhub/custom_authenticator.py:ro
    ports:
      - '8001:8000'
    networks:
      - bits-network
    privileged: true
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8000/hub/health']
      interval: 30s
      timeout: 10s
      retries: 3

# Volumes
volumes:
  postgres_data:
    driver: local
  jupyterhub_data:
    driver: local

# Networks
networks:
  bits-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
