version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: bits-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: bits_platform_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - '5432:5432'
    networks:
      - bits-dev-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: bits-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - '6379:6379'
    networks:
      - bits-dev-network

  # Backend API (Development)
  backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: bits-backend-dev
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
    environment:
      NODE_ENV: development
      PORT: 5000
      DATABASE_URL: ********************************************/bits_platform_dev
      REDIS_URL: redis://redis:6379
      JWT_SECRET: dev-jwt-secret
      SESSION_SECRET: dev-session-secret
      DEBUG: 'true'
      SWAGGER_ENABLED: 'true'
      JUPYTERHUB_URL: http://jupyterhub:8000
      JUPYTERHUB_ADMIN_TOKEN: 607994eb70ddc4da5748e39da374e7875ead335b87e7e1ba15fc4fddb08febd6
      JUPYTER_USER_SERVER_PREFIX: /user
      JUPYTER_PROJECT_PREFIX: project_
      JUPYTER_DEFAULT_KERNEL: python3
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    ports:
      - '5000:5000'
      - '9229:9229' # Debug port
    networks:
      - bits-dev-network
    command: npm run dev

  # JupyterHub Service (Development)
  jupyterhub:
    build:
      context: ./jupyterhub
      dockerfile: Dockerfile
    container_name: bits-jupyterhub-dev
    restart: unless-stopped
    depends_on:
      - postgres
      - backend
    environment:
      JUPYTERHUB_ADMIN_TOKEN: 607994eb70ddc4da5748e39da374e7875ead335b87e7e1ba15fc4fddb08febd6
      BACKEND_API_URL: http://backend:5000
      POSTGRES_HOST: postgres
      POSTGRES_DB: bits_platform_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:rw
      - jupyterhub_dev_data:/srv/jupyterhub
      - ./jupyterhub/jupyterhub_config.py:/srv/jupyterhub/jupyterhub_config.py:ro
      - ./jupyterhub/custom_authenticator.py:/srv/jupyterhub/custom_authenticator.py:ro
    ports:
      - '8001:8000'
    networks:
      - bits-dev-network
    privileged: true

  # Adminer (Database management)
  adminer:
    image: adminer:latest
    container_name: bits-adminer
    restart: unless-stopped
    depends_on:
      - postgres
    ports:
      - '8080:8080'
    networks:
      - bits-dev-network

  # Redis Commander (Redis management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: bits-redis-commander
    restart: unless-stopped
    depends_on:
      - redis
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - '8081:8081'
    networks:
      - bits-dev-network

# Volumes
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  jupyterhub_dev_data:
    driver: local

# Networks
networks:
  bits-dev-network:
    driver: bridge
